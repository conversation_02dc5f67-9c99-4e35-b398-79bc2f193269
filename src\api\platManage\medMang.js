import request from '@/utils/request'

// 查询媒体账号列表
export function listMediacount (query) {
    return request({
        url: '/platform/mediacount/list',
        method: 'get',
        params: query
    })
}

// 查询媒体账号详细
export function getMediacount (id) {
    return request({
        url: '/platform/mediacount/' + id,
        method: 'get'
    })
}

// 新增媒体账号
export function addMediacount (data) {
    return request({
        url: '/platform/mediacount',
        method: 'post',
        data: data
    })
}

// 修改媒体账号
export function updateMediacount (data) {
    return request({
        url: '/platform/mediacount',
        method: 'put',
        data: data
    })
}

// 删除媒体账号
export function delMediacount (id) {
    return request({
        url: '/platform/mediacount/' + id,
        method: 'delete'
    })
}

// 导出媒体账号
export function exportMediacount (query) {
    return request({
        url: '/platform/mediacount/export',
        method: 'get',
        params: query
    })
}

// 媒体模板下载
export function fileMedDownApi () {
    window.location.href = process.env.VUE_APP_BASE_API + "/platform/mediacount/downModel"
}


// 确定上传文件按钮
export function saveImportMediaAccountApi () {
    return request({
        url: '/platform/mediacount/saveImportMediaAccount',
        method: 'get',
    })
}

// 获取所属分组数据
export function groupOptionApi (query) {
    return request({
        url: '/manage/siteType/list',
        method: 'get',
        params: query
    })
}
// 确定批量分组
export function submitGroupFormApi (data) {
    return request({
        url: '/manage/siteType/sourceGroupingBatch',
        method: 'post',
        data: data
    })
}
// 导出媒体账号列表 
export function mediaExport (data) {
    return request({
        url: '/platform/mediacount/export',
        method: 'post',
        data: data
    })
}
// 导出网站账号列表 
export function websiteExport (data) {
    return request({
        url: '/platform/website/export',
        method: 'post',
        data: data
    })
}