<template>
  <div style="height: 70px">
    <!-- 一级分类 -->
    <ul class="checkTypeUl">
      <li
        v-for="(item, index) in checkType"
        @click="clickLiTwo(index)"
        :class="curLiTwo == index ? 'active' : ''"
        :key="index"
      >
        {{ item.name }}
        <i v-show="curLiTwo == index" class="el-icon-arrow-down"></i>
        <p
          style="margin: 0; padding: 0; text-align: center"
          v-if="item.checkArrLen"
        >
          {{ item.checkArrLen }}
        </p>
      </li>
      <!-- <li>
        <i
          class="el-icon-setting"
          @click="drawerOpen"
          style="font-size: 16px; vertical-align: middle"
        ></i>
      </li> -->
    </ul>
    <!-- 系统 -->
    <div class="showCheckTypeOne" v-show="showSystem" ref="treeWrapOne">
      <el-checkbox
        :indeterminate="isIndeterminateSys"
        v-model="checkSysAll"
        @change="handleSystemAllChange"
        >全选</el-checkbox
      >
      <el-checkbox-group
        v-model="queryForm.checkTaskTypeId"
        @change="changeSystemTypeTwo"
      >
        <el-checkbox
          v-for="(item, index) in getSysData"
          :label="item.id"
          :key="index"
          >{{ item.name }}</el-checkbox
        >
      </el-checkbox-group>
    </div>
    <!-- 自定义 -->
    <div class="showCheckTypeOne" v-show="showReview" ref="treeWrapTwo">
      <el-checkbox
        :indeterminate="isIndeterminateOur"
        v-model="checkOurAll"
        @change="handleOurAllChange"
        >全选</el-checkbox
      >
      <el-checkbox-group
        v-model="queryForm.checkTaskId"
        @change="changeOurTypeTwo"
      >
        <el-checkbox
          v-for="(item, index) in ourData"
          :label="item.id"
          :key="index"
          >{{ item.name }}</el-checkbox
        >
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>
import { getWebDataApi } from "@/api/index";
import { getBigTitleTwoApi } from "@/api/conentReview/themManTopic";
import { getBigTitleThreeApi } from "@/api/conentReview/themManTopic";
import { nextTick } from "process";
export default {
  data() {
    return {
      checkType: [
        { name: "系统专题", value: "0" },
        { name: "自定义专题", value: "1" },
      ],

      drawer: true,
      showMed: false,
      curLiTwo: 0,
      showSystem: false,
      showReview: false,
      isIndeterminateSys: false,
      isIndeterminateOur: false,
      checkAll: false,
      checkSysAll: true,
      checkOurAll: true,

      getSysData: [],
      queryForm: {
        checkTaskId: [], // 自定义专题
        checkTaskTypeId: [], // 系统专题
      },

      ourData: [],
      // 系统专题全部选中的id
      checkIdAllSys: [],
      // 自定义专题全部选中的id
      checkOurAllId: [],
    };
  },
  watch: {
    checkSysAll: {
      handler(newval, oldval) {
        if (newval == true) {
          this.queryForm.checkTaskTypeId = this.checkIdAllSys;
          this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
          this.isIndeterminateSys = false;
        } else {
          this.queryForm.checkTaskTypeId = [];
          this.checkType[0].checkArrLen = 0;
          this.isIndeterminateSys = false;
        }
      },
      immediate: true,
    },
    checkOurAll: {
      handler(newval, oldval) {
        if (newval == true) {
          this.queryForm.checkTaskId = this.checkOurAllId;
          this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
          this.isIndeterminateOur = false;
        } else {
          this.queryForm.checkTaskId = [];
          this.checkType[1].checkArrLen = 0;
          this.isIndeterminateOur = false;
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getOurData();
    this.getSystemData();
  },
  mounted() {
    // 全局点击事件
    document.addEventListener("mouseup", (e) => {
      let treesys = this.$refs.treeWrapOne;
      let treeself = this.$refs.treeWrapTwo;
      let treeweb = this.$refs.treeWrapThree;

      if (treesys) {
        // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
        if (!treesys.contains(e.target)) {
          this.showSystem = false;
        }
      }
      if (treeself) {
        // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
        if (!treeself.contains(e.target)) {
          this.showReview = false;
        }
      }
      if (treeweb) {
        // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
        if (!treeweb.contains(e.target)) {
          this.showMed = false;
        }
      }
    });
  },
  methods: {
    // 设置全选状态
    async handleAllChange(queryData) {
      if (this.getSysData.length == 0 && this.ourData.length == 0) {
        await this.getSystemData();
        await this.getOurData();
      }
      this.changeSystemTypeTwo(queryData.checkTaskTypeId);
      this.changeOurTypeTwo(queryData.checkTaskId);
    },
    async getSystemData() {
      let res = await getBigTitleTwoApi();
      this.getSysData = res.data;
      const newcheckIdAllSys = [];
      for (let i = 0; i < this.getSysData.length; i++) {
        newcheckIdAllSys.push(this.getSysData[i].id);
      }
      this.checkIdAllSys = newcheckIdAllSys;
    },
    // 类型选择
    clickLiTwo(index) {
      this.curLiTwo = index;
      if (index == 0) {
        // 系统
        this.showSystem = true;
        this.showReview = false;
      } else if (index == 1) {
        // 自定义
        this.showSystem = false;
        this.showReview = true;
      }
    },
    drawerOpen() {
      this.drawer = true;
      this.showReview = false;
      this.showMed = false;
      this.getDiffChange();
    },
    // 获取自定义专题数据
    async getOurData() {
      let res = await getBigTitleThreeApi();
      this.ourData = res.data;
      const newourData = [];
      this.ourData.map((item) => {
        newourData.push(item.id);
      });
      this.checkOurAllId = newourData;
    },
    async getDiffChange() {
      let params = {
        pageNum: 1,
        pageSize: 50,
        isCollect: 1,
      };
      let res = await getWebDataApi(params);
      this.getWebData = res.rows;
    },
    //   系统专题全选
    handleSystemAllChange(val) {
      if (val == true) {
        let aaa = [];
        for (let i = 0; i < this.getSysData.length; i++) {
          aaa.push(this.getSysData[i].id);
        }
        this.queryForm.checkTaskTypeId = aaa;
        this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
        this.isIndeterminateSys = false;
      } else {
        this.queryForm.checkTaskTypeId = [];
        this.checkType[0].checkArrLen = 0;
        this.isIndeterminateSys = false;
      }
    },
    // 系统专题单选
    changeSystemTypeTwo(value) {
      let checkedCount = value.length;
      this.checkSysAll = checkedCount === this.getSysData.length;
      nextTick(() => {
        this.isIndeterminateSys =
          checkedCount > 0 && checkedCount < this.getSysData.length;
        this.queryForm.checkTaskTypeId = value;
        this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
      });
    },
    //   自定义专题全选
    handleOurAllChange(val) {
      if (val == true) {
        let aaa = [];
        for (let i = 0; i < this.ourData.length; i++) {
          aaa.push(this.ourData[i].id);
        }
        this.queryForm.checkTaskId = aaa;
        this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
        this.isIndeterminateOur = false;
      } else {
        this.queryForm.checkTaskId = [];
        this.checkType[1].checkArrLen = 0;
        this.isIndeterminateOur = false;
      }
    },
    // 自定义专题单选
    changeOurTypeTwo(value) {
      let checkedCount = value.length;
      this.checkOurAll = checkedCount === this.ourData.length;
      nextTick(() => {
        this.isIndeterminateOur =
          checkedCount > 0 && checkedCount < this.ourData.length;
        this.queryForm.checkTaskId = value;
        this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
      });
    },
    // draw全选
    chooseMeEvent() {
      if (this.chooseMe == true) {
        // 系统全选
        this.checkSysAll = true;
        if (this.checkSysAll == true) {
          let aaa = [];
          for (let i = 0; i < this.getSysData.length; i++) {
            aaa.push(this.getSysData[i].id);
          }
          this.queryForm.checkTaskTypeId = aaa;
          this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
          this.isIndeterminateSys = false;
        }
        // 自定义全选
        this.checkOurAll = true;
        if (this.checkOurAll == true) {
          let aaa = [];
          for (let i = 0; i < this.ourData.length; i++) {
            aaa.push(this.ourData[i].id);
          }
          this.queryForm.checkTaskId = aaa;
          this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
          this.isIndeterminateOur = false;
        }
        // 网站||媒体全选
        this.secondAct = true;
        if (this.secondAct == true) {
          this.filterDataTree.map((item) => {
            item.tag = true;
            item.checkAll = true;
            if (item.webSiteAndMediaAccountList.length > 0) {
              item.webSiteAndMediaAccountList.map(
                (itemb) => (itemb.tag = true)
              );
              let id = item.webSiteAndMediaAccountList.map((itemb) => itemb.id);
              item.single = id;
              this.checkTaskIds.push(...id);
              this.queryForm.assignIds = this.checkTaskIds;
              this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
            } else {
              this.checkTaskIds.push(item.id);
              this.queryForm.assignIds = this.checkTaskIds;
              this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
            }
          });
        }
        this.$forceUpdate();
      } else {
        // 系统全不选
        this.checkSysAll = false;
        this.queryForm.checkTaskTypeId = [];
        this.checkType[0].checkArrLen = 0;
        this.isIndeterminateSys = false;
        // 自定义全不选
        this.checkOurAll = false;
        this.queryForm.checkTaskId = [];
        this.checkType[1].checkArrLen = 0;
        this.isIndeterminateOur = false;
        // 网站||媒体全部选
        this.secondAct = false;
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
            item.single = [];
          }
          this.checkTaskIds = [];
          this.queryForm.assignIds = this.checkTaskIds;
          this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        });
        this.$forceUpdate();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.checkTypeUl {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
  margin-top: 4px;
  position: relative;

  li {
    margin-right: 16px;
    list-style: none;
    color: #606266;
    cursor: pointer;
    height: 28px;
    line-height: 28px;
    padding: 0 6px;
    border-radius: 3px;
    border: solid 1px transparent;
  }

  li.active {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }

  li.activeErrorLi {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }
}
.showCheckTypeOne {
  max-height: 400px;
  overflow-y: auto;
  width: 500px;
  position: absolute;
  background: #fff;
  border: solid 1px #ccc;
  z-index: 10;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 5px;
  margin-top: 10px;
}
</style>
