<template>
  <div class="home">
    <div class="homeBox" v-loading="loading">
      <el-table
        :data="detailDataList.wordList"
        border=""
        :row-style="{ height: 10 + 'px' }"
        :cell-style="{ padding: 6 + 'px' }"
        :header-cell-style="{ height: '10px', padding: '6px' }"
      >
        <el-table-column prop="mistakeWord" label="问题词语" align="center">
          <template slot-scope="scope">
            <div class="mistakeWord" @click="() => goMistake(scope.row)">
              {{ scope.row.mistakeWord }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="correctWord" label="建议词语" align="center">
          <template slot-scope="scope">
            <div class="successWord" @click="() => goMistake(scope.row)">
              {{ scope.row.correctWord }}
            </div>
          </template>
        </el-table-column>
      </el-table>

      <div class="homeLeftUl" id="articleCont">
        <v-touch v-on:doubletap="returnTop">
          <h2>
            <p v-html="detailDataList.info.title" class="emRed"></p>
          </h2>
          <ul>
            <li>{{ detailDataList.info.siteName }}</li>
            <li>{{ detailDataList.info.time }}</li>
          </ul>
          <ul style="margin-top: 0.5rem" v-show="detailDataList.info.url">
            <li style="cursor: pointer; color: #409eff">
              <span v-if="detailDataList.info.sector">
                <a
                  :href="detailDataList.info.sector"
                  referrerpolicy="no-referrer"
                  target="_blank"
                >
                  查看原文
                </a>
                <a
                  style="margin-left: 10px"
                  :href="detailDataList.info.url"
                  referrerpolicy="no-referrer"
                >
                  下载附件
                </a>
              </span>
              <a
                v-else
                :href="detailDataList.info.url"
                referrerpolicy="no-referrer"
                target="_blank"
              >
                查看原文
              </a>
              <span @click.stop="copyContent(detailDataList.info)" class="title-copy"
              >
                复制
              </span>
            </li>
          </ul>
          <p class="textCont emRed" v-html="detailDataList.info.content"></p>
        </v-touch>
      </div>
    </div>
  </div>
</template>

<script>
import { getWechatDetailDataApi, getSignature } from "@/api/conentReview/detailPage";
import {replaceHtml,copyAritical} from "@/utils/index"
import { mount } from "sortablejs";
export default {
  name: "wechatListDetail",
  data() {
    return {
      tableData: [{ name: "", nameTwo: "" }],
      detailDataList: { info: {} },
      loading: false,
      replaceHtml,
    };
  },
  created() {
  },
 async mounted() {
   await this.getDetailData();
   await this.getWeixin()
  },
  methods: {
    // 微信卡片分享
   async getWeixin() {
      var url = location.href.split('#')[0];
      let res = await getSignature({ url: url })
        let mistakeWord = this.detailDataList.wordList.map(item => item.mistakeWord).join('、 ');
          wx.config({
              debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
              appId: res.data.appId, // 必填，公众号的唯一标识
              timestamp: res.data.timestamp, // 必填，生成签名的时间戳
              nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
              signature: res.data.signature,// 必填，签名，见附录1
              jsApiList: [
              // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
              // 'checkJsApi',//checkJsApi接口是客户端6.0.2新引入的一个预留接口，第一期开放的接口均可不使用checkJsApi来检测。
              'onMenuShareAppMessage', //获取“分享给朋友”按钮点击状态及自定义分享内容接口（即将废弃）
              'onMenuShareTimeline', //获取“分享到朋友圈”按钮点击状态及自定义分享内容接口（即将废弃）
              // 'onMenuShareQQ',//获取“分享到QQ”按钮点击状态及自定义分享内容接口（即将废弃）
              'updateAppMessageShareData',//自定义“分享给朋友”及“分享到QQ”按钮的分享内容（1.4.0）
              'updateTimelineShareData'//自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容（1.4.0）
            ] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
          })
        let shareConfig = {
            imgUrl: "https://iapi.boryou.com:33003/smartcheck-prod/public/logo/7d502abe5b783ec9c8afac4675af95f6.png", //分享图，默认当相对路径处理，所以使用绝对路径的的话，“http://”协议前缀必须在。
            desc: mistakeWord||'', //摘要,如果分享到朋友圈的话，不显示摘要。
            title: this.replaceHtml(this.detailDataList.info.title), //分享卡片标题
            link: res.data.url, //分享出去后的链接，这里可以将链接设置为另一个页面。
            success: function () { //分享成功后的回调函数
              // alert('cg')
            },
            cancel: function() {
              // 用户取消分享后执行的回调函数
              // alert('sb')
            }
          }
          wx.ready(function () {
            //自定义“分享到朋友圈”及“分享到 QQ 空间”按钮的分享内容
            wx.updateTimelineShareData(shareConfig);
            //自定义分享给朋友、以及分享给qq好友
            wx.updateAppMessageShareData(shareConfig);
            // alert('3')
            // 获取“分享给朋友”按钮点击状态及自定义分享内容接口（即将废弃）
            wx.onMenuShareAppMessage(shareConfig);
            // 获取“分享到朋友圈”按钮点击状态及自定义分享内容接口（即将废弃）
            wx.onMenuShareTimeline(shareConfig)
            //分享到微博
            // wx.onMenuShareWeibo(shareConfig)
          })
    },
    copyContent(item) {
      copyAritical(item)
    },
    //   获取详情页数据
    async getDetailData() {
      this.loading = true;
      let params = {
        id: this.$route.query.id,
        time: this.$route.query.time,
        solrId: this.$route.query.solrId,
        checkTaskId: this.$route.query.checkTaskId,
        userName: this.$route.query.userName,
        p: this.$route.query.p
      };
      let res = await getWechatDetailDataApi(params)
      this.loading = false;
      let { data } = JSON.parse(JSON.stringify(res));
      if (data) {
        this.detailDataList = data;
      }
    },
    goMistake(item) {
      const elements = document.querySelectorAll("#articleCont em");
      if (elements.length == 0) return;
      for (let i = 0; i < elements.length; i++) {
        if (elements[i].innerText === item.mistakeWord) {
          document.querySelector("#articleCont").scrollTop =
            elements[i].offsetTop -
            document.querySelector("#articleCont").offsetTop -
            10;
          break;
        }
      }
    },
    returnTop() {
      document.querySelector("#articleCont").scrollTop = 0;
    },
  },
};
</script>

<style scoped lang="scss">
.title-copy{
  margin-left: 10px;
}
.ellipsis {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.el-popover {
  height: 150px;
  overflow: auto;
}
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  box-sizing: border-box;
  .homeBox {
    width: 100%;
    background: #fff;
    overflow: hidden;
    padding: 20px;
    box-sizing: border-box;

    .mistakeWord {
      cursor: pointer;
      color: #d13e3b;
    }
    .successWord {
      color: #409eff;
    }
    .homeLeftUl {
      width: 100%;
      height: 80vh;
      overflow-y: scroll;
      scroll-behavior: smooth;
      // border: solid 1px #ccc;
      // padding: 0 20px;
      margin-top: 20px;
      h2 {
        text-align: center;
        p {
          display: inline-block;
          font-weight: normal;
          font-size: 22px;
          text-align: left;
          margin: 0;
        }
      }
      ul {
        display: flex;
        flex-direction: row;
        // justify-content: space-between;
        //   flex-wrap: wrap;
        margin: 0;
        padding: 0;
        width: 100%;
        overflow: hidden;
        color: #666666;
        li {
          font-size: 13px;
          list-style: none;
          margin-right: 10px;
          line-height: 1rem;
          a {
            display: inline-block;
            white-space: nowrap;
          }
          span {
            display: inline-block;
            text-align: center;
            margin-right: 1rem;
          }
          span:last-child {
            margin-right: 0;
          }
        }
      }
      p.textCont {
        white-space: pre-wrap;
        line-height: 28px;
        // border: solid 1px #ccc;
        // padding: 20px;
        box-sizing: border-box;
        // height: 500px;
        // overflow-y: scroll;
        // scroll-behavior: smooth;
      }
    }
  }
}
</style>
