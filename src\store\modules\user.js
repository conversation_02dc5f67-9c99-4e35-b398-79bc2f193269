import { login, singleLogin, logout, getInfo, checkLogin<PERSON><PERSON> } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { getSystemLogoApi } from "@/api/login"
import Cookies from 'js-cookie'
const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    logoImg: "",
    backImage: "",
    sysName: "",
    expireTime: "",
    // loginWay: ""
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_IMG: (state, logoImg) => {
      state.logoImg = logoImg
    },
    SET_BGIMG: (state, backImage) => {
      state.backImage = backImage
    },
    SET_SYSNAME: (state, sysName) => {
      state.sysName = sysName
    },
    SET_EXPIRETIME: (state, expireTime) => {
      state.expireTime = expireTime
    },
    // SET_LOGINWAY: (state, loginWay) => {
    //   state.loginWay = loginWay
    // }

  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      const rejectNum = userInfo.rejectNum
      const wechatQRCodeId = userInfo.wechatQRCodeId
      const context = userInfo.context
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid, rejectNum, wechatQRCodeId, context).then(res => {
          if (res.code == 200) {
            setToken(res.token)
            commit('SET_TOKEN', res.token)
            Cookies.set('loginWay', res.loginWay)
            // sessionStorage.setItem("setloginway", res.loginWay)
            // commit('SET_LOGINWAY', res.loginWay)
            resolve(res)
          } else {
            resolve(res)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 单点登录
    LoginBySingle({ commit }, params) {
      return new Promise((resolve, reject) => {
        // ApiSingleLogin(params).then(res => {
        setToken(params.token)
        commit('SET_TOKEN', params.token)
        Cookies.set('loginWay', params.loginWay)
        resolve()
        // }).catch(error => {
        //   reject(error)
        // })
      })
    },
    // 登录
    singleLogin({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        singleLogin(username, password, code, uuid).then(res => {
          if (res.code == 200) {
            setToken(res.token)
            commit('SET_TOKEN', res.token)
            Cookies.set('loginWay', res.loginWay)
            // sessionStorage.setItem("setloginway", res.loginWay)
            // commit('SET_LOGINWAY', res.loginWay)
            resolve(res)
          } else {
            resolve(res)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 微信登录
    weChatLogin({ commit }, userInfo) {
      let params = {
        wechatQRCodeId: userInfo.wechatQRCodeId
      }
      return new Promise((resolve, reject) => {
        checkLoginApi(params).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo(state.token).then(res => {
          const user = res.user
          sessionStorage.setItem("setloginway", res.loginWay)
          const avatar = user.avatar == "" ? require("@/assets/images/profile.jpg") : process.env.VUE_APP_BASE_API + user.avatar;
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
            commit('SET_EXPIRETIME', res.user.expireTime)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.userName)
          commit('SET_AVATAR', avatar)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then((res) => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          Cookies.remove('loginWay')
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        Cookies.remove('loginWay')
        resolve()
      })
    },
    // 获取登录logo
    getSysLogo({ commit }, sysInfo) {
      return new Promise((resolve, reject) => {
        var url = document.location.pathname;
        var index = url.substr(url.indexOf('/', url.indexOf('/') - 1) + 1).substring(0, url.substr(url.indexOf('/', url.indexOf('/') - 1) + 1).indexOf('/'));
        if (index) {
          getSystemLogoApi(index).then(res => {
            // debugger
            if (res.data.backImage) {
              sessionStorage.setItem("idUrl", index)
              commit('SET_IMG', process.env.VUE_APP_BASE_API + res.data.logo)
              commit('SET_SYSNAME', res.data.name)
              commit('SET_BGIMG', process.env.VUE_APP_BASE_API + res.data.backImage)
            }
            // else {
            //     commit('SET_IMG', process.env.VUE_APP_BASE_API + res.data.logo)
            //     commit('SET_SYSNAME', res.data.name)
            //     console.log("2")
            //     commit('SET_BGIMG', require("@/assets/login/loginBg.png"))

            // }
            resolve(res)
          }).catch(error => {
            reject(error)
          })

        }
      })
    }
  }
}

export default user
