import request from '@/utils/request'

// 新增定时任务调度
export function nowSearchApi(data) {
    return request({
        url: '/search/query',
        method: 'post',
        data: data
    })
}

// 导出Html
export function handleExportHtmlAllApiOne(data) {
    return request({
        url: '/search/exportHtml',
        method: 'post',
        data: data
    })
}

// 导出excel
export function handleExportExcelAllApi(data) {
    return request({
        url: '/search/exportExcel',
        method: 'post',
        data: data
    })
}
// 发文统计
export function documentStatistic(data) {
    return request({
        url: '/search/documentStatistic',
        method: 'post',
        data: data
    })
}
// 导出发文统计 
export function exportStatistic(data) {
    return request({
        url: '/search/exportStatistic',
        method: 'post',
        data: data
    })
}