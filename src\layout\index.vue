<template>
  <div
    :class="classObj"
    class="app-wrapper"
    :style="{ '--current-color': theme }"
  >
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <sidebar
      v-if="menuView == 0"
      class="sidebar-container"
      :style="{
        backgroundColor:
          sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg,
      }"
    />
    <div
      :class="{ hasTagsView: needTagsView }"
      class="main-container"
      v-if="menuView == 0"
    >
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <right-panel v-if="showSettings">
        <settings />
      </right-panel>
    </div>

    <div
      :class="{ hasTagsView: needTagsView }"
      class="main-container"
      style="margin-left: 0px"
      v-else
    >
      <div :class="{ 'fixed-header': fixedHeader }" style="width: 100%">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main v-if="true" />
      <right-panel v-if="showSettings">
        <settings />
      </right-panel>
    </div>
    
    <AddWords ref="addWord" :dialogVisible="dialogVisible" @addWord="() => { }" @closeDialog="closeDialog" />
  </div>
</template>

<script>
import RightPanel from "@/components/RightPanel";
import { AppMain, Navbar, Settings, Sidebar, TagsView } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";
import variables from "@/assets/styles/variables.scss";
import AddWords from "@/views/components/AddWords";
import { addTrueWord } from "@/api/system/autodict";

export default {
  name: "Layout",
  components: {
    AppMain,
    Navbar,
    RightPanel,
    Settings,
    Sidebar,
    TagsView,
    AddWords
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme,
      sideTheme: (state) => state.settings.sideTheme,
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      showSettings: (state) => state.settings.showSettings,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
    variables() {
      return variables;
    },
    menuView() {
      return this.$store.state.settings.menuView;
    },
  },
  provide() {
    //父组件中通过provide来提供变量，在子组件中通过inject来注入变量。
    return {
      contextmenu: this.contextmenu,
    };
  },
  data() {
    return {
      dialogVisible: false,
    };
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
    contextmenu(event) {
      const content =  window.getSelection().toString();
      if(content){
        event.preventDefault();
      }else{
        return
      }
      this.$contextmenu({
        items: [
          {
            label: "添加正词",
            disabled: !content,
            onClick: async () => {
              if(content.length>=500){
                this.$message({ showClose: true, message: '选中文本不得大于500字', type: "warning" });
                return
              }
              addTrueWord({ properWord: content }).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增正词成功");
                } else {
                  this.msgError(res.msg);
                }
              });
            }
          },
          {
            label: "添加敏感词",
            disabled: !content,
            onClick: () => {
              const param = { sensitiveWord: content, wordType: 2 }
              this.dialogVisible = true
              this.$refs.addWord.defaultType(param)
            }
          },
          {
            label: "添加错误词", disabled: !content,
            onClick: () => {
              const param = { errorWord: content, wordType: 3 }
              this.dialogVisible = true
              this.$refs.addWord.defaultType(param)
            }
          }
        ],
        event,
        zIndex: 3,
        minWidth: 140
      });
    },
    closeDialog() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
@import "~@/assets/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
