<template>
  <div class="home">
    <div class="homeFrom">
      <el-form :model="queryForm" :inline="true">
        <el-form-item prop="mistakeWord" label="错误词：">
          <el-input
            v-model.trim="queryForm.mistakeWord"
            placeholder="请输入错误词"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button type="primary" @click="addList" class="mb8" size="mini"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            @click="delList"
            class="mb8"
            size="mini"
            :disabled="multiple"
            >批量删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            >批量导入</el-button
          >
        </el-col>
      </el-row>

      <el-table :data="themList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="mistakeWord" label="错误词"> </el-table-column>
        <el-table-column prop="correctWord" label="正确词"> </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="seeDialog(scope.row)"
              >修改</el-button
            >
            <el-button type="text" @click="delList(scope.row)" size="small"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="handleQuery"
      />
    </div>

    <!-- 新增修改弹框 -->
    <el-dialog :title="title" width="600px" :visible.sync="dialogFormVisible">
      <el-form :model="form" label-width="80px" :rules="rules" ref="formRef">
        <el-form-item label="错误词：" prop="mistakeWord">
          <el-input v-model.trim="form.mistakeWord"></el-input>
        </el-form-item>
        <el-form-item label="正确词：" prop="correctWord">
          <el-input v-model.trim="form.correctWord"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="sureAdd" size="small"
          >确 定</el-button
        >
        <el-button @click="closeAdd" size="small">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 批量导入 -->
    <el-dialog title="批量导入正误词" :visible.sync="dialogVisible" width="30%">
      <div style="text-align: center">
        <el-upload
          class="upload-demo"
          :action="uploadFileUrl"
          :on-preview="handlePreview"
          :on-success="handleUploadSuccess"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          :limit="1"
          :headers="headers"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <el-button size="small">点击上传</el-button>
          <!-- <div slot="tip"
               class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
        </el-upload>
        <div style="text-align: center; margin-top: 40px">
          <el-button type="primary" @click="fileWebDown" size="small"
            >正误词模板下载
            <!-- <el-tooltip
              class="item"
              effect="light"
              content="用户名称是登录账号名，如果是多个用户的话，用英文逗号隔开"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip> -->
          </el-button>
          <el-button type="primary" size="small" @click="importTemp"
            >确定</el-button
          >
          <el-button type="success" size="small" @click="ClosedialogVisible"
            >取消</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  handleTFApi,
  AddTFApi,
  editTFApi,
  delTFApi,
  seeTFApi,
  saveImportTFApi,
  fileTFDownApi,
} from "@/api/system/trueFaseWord";
import { getToken } from "@/utils/auth";
export default {
  data() {
    return {
      fileList: [],
      uploadFileUrl:
        process.env.VUE_APP_BASE_API + "/task/correctMistakeWord/import", // 上传的文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      dialogVisible: false,
      queryForm: {
        mistakeWord: "",
      },
      pageNum: 1,
      pageSize: 10,
      themList: [],
      total: 0,
      dialogFormVisible: false,
      form: {
        mistakeWord: "",
        correctWord: "",
        id: "",
      },
      title: "新增正误词",
      ids: [],
      single: true,
      // 非多个禁用
      multiple: true,
      rules: {
        mistakeWord: [
          { required: true, trigger: "blur", message: "错误词不能为空" },
        ],
        correctWord: [
          { required: true, trigger: "blur", message: "正确词不能为空" },
        ],
      },
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    // 批量导入按钮
    handleExport() {
      this.dialogVisible = true;
    },
    handlePreview(file) {
      console.log(file);
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      this.resCode = res.code;
      if (res.code == 200) {
        this.$message.success(res.msg);
      } else {
        this.$message.error(res.msg);
      }
      this.$emit("input", res.url);
    },
    //   文件上传
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    //   导入
    async importTemp() {
      if (this.resCode == 200) {
        let res = await saveImportTFApi();
        if (res.code == 200) {
          this.$message.success("导入正确词成功");
        } else {
          this.$message.error("导入正确词失败");
        }
      }
      this.fileList = [];
      this.dialogVisible = false;
      this.handleQuery();
    },
    ClosedialogVisible() {
      this.fileList = [];
      this.dialogVisible = false;
    },
    // 正确词模板下载
    fileWebDown() {
      fileTFDownApi();
    },
    //   搜索
    async handleQuery() {
      let params = {
        mistakeWord: this.queryForm.mistakeWord,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      let res = await handleTFApi(params);
      this.themList = res.rows;
      this.total = res.total;
    },
    // 重置
    resetQuery() {
      this.queryForm.mistakeWord = "";
    },
    // 点击新增弹框
    addList() {
      this.dialogFormVisible = true;
      this.title = "新增正误词";
      this.form.mistakeWord = "";
      this.form.correctWord = "";
    },
    // 新增确定按钮
    sureAdd() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.form.id) {
            // 修改
            let params = {
              mistakeWord: this.form.mistakeWord,
              correctWord: this.form.correctWord,
              id: this.form.id,
            };
            editTFApi(params).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.dialogFormVisible = false;
                this.handleQuery();
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
              }
            });
          } else {
            //   新增
            let params = {
              mistakeWord: this.form.mistakeWord,
              correctWord: this.form.correctWord,
            };
            AddTFApi(params).then((res) => {
              if (res.code == 200) {
                this.dialogFormVisible = false;
                this.$message.success("新增成功");
                this.handleQuery();
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
              }
            });
          }
        } else {
          this.$message.error("请输入正确信息");
        }
      });
    },
    // 取消
    closeAdd() {
      this.dialogFormVisible = false;
    },
    // 查看
    async seeDialog(row) {
      this.dialogFormVisible = true;
      this.title = "修改正误词";
      let res = await seeTFApi(row.id);
      this.form = res.data;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 刪除
    delList(row) {
      const ids = row.id || this.ids;
      console.log("ids", ids);
      this.$confirm("是否确认删除该数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delTFApi(ids);
        })
        .then((res) => {
          if (res.code == 200) {
            this.handleQuery();
            this.msgSuccess("删除成功");
          } else {
            this.$message.error(res.msg);
          }
        });
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: 889px;
  .homeFrom {
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
  }
}
::v-deep .upload-demo {
  display: flex;
  width: 49%;
  margin: 0 auto;
  .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;
    border: solid 1px #ccc;
    height: 32px;
    line-height: 32px;
    margin-left: 6px;
    border-radius: 5px;
    width: 200px;
    overflow: hidden;
  }
  .el-upload-list__item:first-child {
    margin-top: 0;
    height: 32px;
    line-height: 32px;
  }
}
</style>