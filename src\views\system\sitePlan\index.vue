<template>
  <div class="home">
    <div class="homeBox">
      <el-form :model="queryParams" label-width="100px" :inline="true">
        <el-form-item label="分组名称：" prop="typeName">
          <el-input v-model.trim="queryParams.typeName" size="small"></el-input>
        </el-form-item>
        <el-form-item label="所属用户：" prop="customer" v-hasPermi="['system:sitePlan:ownUser']">
          <el-select v-model="queryParams.userId" placeholder="请选择所属用户" clearable size="small" filterable
            @change="getgroupOption">
            <el-option v-for="(dict, index) in userList" :key="index" :label="`${dict.userName}(${dict.nickName})`"
              :value="dict.userId">{{
                dict.userName }} ({{ dict.nickName }})</el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:sitePlan:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:sitePlan:del']">批量删除</el-button>
        </el-col>
      </el-row>

      <el-table :data="sitePlanList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" :index="getIndex" width="80"></el-table-column>
        <el-table-column label="分组名称" align="center" prop="typeName" />
        <el-table-column label="所属用户" align="center" prop="userName" v-if="checkPermi(['system:sitePlan:ownUser'])">
          <template slot-scope="scope" v-if="scope.row.nickName">
            {{ scope.row.userName }}({{ scope.row.nickName }})
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="createUserName"
          v-if="checkPermi(['system:sitePlan:ownUser'])" />
        <el-table-column label="创建时间" align="center" prop="createTime" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width"
          v-if="checkPermi(['system:sitePlan:operate'])">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleUpdate(scope.row)">{{ scope.row.typeName == "未分组" ? "查看" :
              "修改" }}</el-button>
            <el-button size="mini" type="text" @click="handleDelete(scope.row)"
              v-show="scope.row.typeName !== '未分组'">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="pageNum" :limit.sync="pageSize"
        @pagination="handleQuery" />
    </div>
    <!-- 新增修改弹框 -->
    <el-dialog :title="title" width="600px" :visible.sync="dialogFormVisible">
      <el-form :model="form" label-width="120px" :rules="rules" ref="formRef">
        <el-form-item label="分组名称：" prop="typeName">
          <el-input v-model.trim="form.typeName" style="width: 80%" :disabled="bindDis" maxlength="20" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="所属用户：" :prop="
          roles.includes('admin') || roles.includes('yunying')
            ? 'customer'
            : ''
        " v-hasPermi="['system:sitePlan:ownUser']">
          <el-select v-model="form.userId" placeholder="请选择所属用户" clearable filterable style="width: 80%"
            @change="getgroupOptionTwo" :disabled="bindDis">
            <el-option v-for="(dict, index) in userList" :key="index" :label="`${dict.userName}(${dict.nickName})`"
              :value="dict.userId">{{ dict.userName }} ({{ dict.nickName }})</el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="addSitePlan">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  handleQueryApi,
  addSitePlanApi,
  handleDeleteApi,
  editSitePlanApi,
  userTypeApi,
  handleUpdateApi,
  checkExistApi,
} from "@/api/system/sitePlan";
import { listUser, listUserNew } from "@/api/system/user";
import { listWebsite } from "@/api/platManage/filWeb";
import { listMediacount } from "@/api/platManage/medMang";
import { checkPermi } from "@/utils/permission.js";
import { getInfo } from "@/api/login";
export default {
  data () {
    return {
      curcustomer: '',
      bindDis: false,
      userList: [],
      title: "新增分组",
      queryParams: {
        typeName: "",
        customer: "",
        userId: ""
      },
      form: {
        typeName: "",
        customer: "",
        id: "",
        userId: ''
      },
      sitePlanList: [],
      pageNum: 1,
      pageSize: 10,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 选中数组
      idsTwo: ["10", "11"],
      // 非单个禁用
      singleTwo: true,
      // 非多个禁用
      multipleTwo: true,
      total: 0,
      dialogFormVisible: false,
      medType: [
        { name: "网站", value: "1" },
        { name: "媒体账号", value: "2" },
      ],
      siteList: [],
      totalTwo: 0,
      pageNumTwo: 1,
      pageSizeTwo: 8,
      rules: {
        typeName: [
          { required: true, message: "分组名称不能为空", trigger: "blur" },
        ],
        customer: [
          { required: true, message: "所属用户不能为空", trigger: "blur" },
        ],
      },
      userCustomer: "",
      roles: [],
      localUserId: ''
    };
  },
  async created () {
    await this.getInfoData();
    this.handleQuery();
  },
  methods: {
    checkPermi,
    // 所属分组切换
    async getgroupOption (val) {
      console.log(val, 'val')
      if(!val){
        this.queryParams.customer = ''
        this.queryParams.userId = ''
        return
      }
      this.userList.map((item, index) => {
        if (item.userId == val) {
          this.queryParams.customer = item.customer
          this.queryParams.userId = item.userId
        }
      })
    },
    async getgroupOptionTwo (val) {
      this.userList.map((item, index) => {
        if (item.userId == val) {
          this.form.customer = item.customer
          this.form.userId = item.userId
        }
      })

    },
    async getInfoData () {
      let res = await getInfo();
      this.roles = res.roles;
      this.userCustomer = res.user.customer;
      this.queryParams.customer = res.user.customer
      if(res.user.userId!='1'){
        this.queryParams.userId = res.user.userId
      }
      this.localUserId = res.user.userId
      this.getUserList(res.user.customer);

    },
    // 获取所属用户
    async getUserList (resCustomer) {
      let params = {
        customer: resCustomer
      }
      let res = await listUserNew(params);
      this.userList = res.rows;
    },
    getIndex (index) {
      return (this.pageNum - 1) * this.pageSize + index + 1;
    },
    //   关闭新增修改弹窗
    closeDialog () {
      this.dialogFormVisible = false;
      this.$refs.formRef.clearValidate();
      // this.form.typeName = "";
      // this.form.customer = "";
      // this.form.id = "";
    },
    //   获取列表数据
    async handleQuery () {
      let params = {
        customer: this.queryParams.customer,
        typeName: this.queryParams.typeName,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      let res = await handleQueryApi(params);
      this.sitePlanList = res.rows;
      this.total = res.total;
    },
    //   重置
    resetQuery () {
      this.queryParams.typeName = "";
      this.queryParams.customer = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleSelectionChangeTwo (selection) {
      this.idsTwo = selection.map((item) => item.id);
      this.singleTwo = selection.length !== 1;
      this.multipleTwo = !selection.length;
    },
    // 删除
    async handleDelete (row) {
      // 查询分组下是否有站点
      let hasData = "";
      const ids = row.id || this.ids;
      let params = {
        siteId: row.id || this.ids.toString(),
      };
      let res = await checkExistApi(params);
      if (res.code == 200) {
        this.hasData = res.data;
        // this.hasData 1代表分组下有站点，0代表没有
      } else {
        this.msgError(res.msg);
      }
      this.$confirm(
        `${this.hasData == 1
          ? "选中的分组下绑有备案网站/媒体账号，是否确认删除？"
          : "是否确认删除该数据项?"
        }`,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return handleDeleteApi(ids);
        })
        .then(() => {
          this.handleQuery();
          this.msgSuccess("删除成功");
        });
    },
    // 修改
    async handleUpdate (row) {
      let res = await handleUpdateApi(row.id);
      if (res.code == 200) {
        this.dialogFormVisible = true;
        this.form = res.data;
        this.form.customer = parseInt(res.data.customer);
        if (res.data.typeName == "未分组") {
          this.bindDis = true;
          this.title = "查看分组";
        } else {
          this.bindDis = false;
          this.title = "修改分组";
        }
      }
    },
    // 打开新增弹框
    handleAdd () {
      this.dialogFormVisible = true;
      this.title = "新增分组";
      this.form.typeName = "";
      this.form.customer = "";
      this.form.id = "";
      this.bindDis = false;
      this.form.userId = this.localUserId;

    },
    // 新增修改确定
    addSitePlan () {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          if (this.form.id) {
            if (this.title == "查看分组") {
              this.dialogFormVisible = false;
              this.$refs.formRef.clearValidate();
            } else if (this.title == "修改分组") {
              //   修改
              let params = {
                typeName: this.form.typeName,
                customer: this.form.customer || this.userCustomer,
                id: this.form.id,
              };
              let res = await editSitePlanApi(params);
              if (res.code == 200) {
                this.handleQuery();
                this.dialogFormVisible = false;
                this.msgSuccess("修改成功");
                this.$refs.formRef.clearValidate();
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
              } else {
                this.msgError(res.msg);
              }
            }
          } else {
            //   新增
            let params = {
              typeName: this.form.typeName,
              customer: this.form.customer || this.userCustomer,
            };
            let res = await addSitePlanApi(params);
            if (res.code == 200) {
              this.msgSuccess("新增成功");
              this.handleQuery();
              this.dialogFormVisible = false;
              for (let key in this.form) {
                this.$set(this.form, key, "");
              }
              this.$refs.formRef.clearValidate();
            } else {
              this.msgError(res.msg);
            }
          }
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.home {
  width: 100%;
  overflow: hidden;
  padding: 20px 5%;
  box-sizing: border-box;
  background: #f4f7fb;
  min-height: calc(100vh - 60px);

  .homeBox {
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
  }
}
</style>
