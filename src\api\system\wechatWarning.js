import request from '@/utils/request'
// 搜索
export function handleQueryApi (data) {
  return request({
      url: '/wechatWarn/selectWarn',
      method: 'post',
      // params: query
      data: data
  })
}
// 新增
export function sureAddApi (data) {
  return request({
      url: '/wechatWarn/insertWarn',
      method: 'post',
      data: data
  })
}

// 获取新增表单的id
export function getNewformIdApi () {
  return request({
      url: '/wechatWarn/selectId',
      method: 'post',
  })
}

// 修改
export function editAddApi (data) {
  return request({
      url: '/wechatWarn/updateWarn',
      method: 'post',
      data: data
  })
}
// 删除
export function delListApi (data) {
  return request({
      url: '/wechatWarn/batchDeleteWarn',
      method: 'post',
      data: data
  })
}
// 查看
export function seeListApi (data) {
  return request({
      url: '/wechatWarn/selectWarnId',
      method: 'post',
      data: data
  })
}




// 获取微信二维码的链接
export function getMaPicUrlApi (data) {
  return request({
    url: '/wechat/getWarnCode',
    method: 'post',
    data: data
  })
}


// 扫码状态新接口
export function checkScanStatus (data) {
  return request({
    url: '/wechat/checkScanStatus',
    method: 'post',
    data: data
  })
}



// 获取微信二维码
export function getMaPicApi (query) {
  return request({
    url: '/system/wechatQRCode/getWechatQRCode',
    method: 'get',
    params: query
  })
}


// 获取微信是否已扫码
export function getWechatUseApi (params) {
  return request({
    url: '/system/wechatQRCode/checkWarnQRCode',
    method: 'get',
    params:params
  })
}
// 预警绑定
export function bindWarningApi (data) {
  return request({
    url: '/system/wechatQRCode/bindWarning',
    method: 'post',
    data:data
  })
}

// 获取用户微信预警绑定列表
// export function getUserWeChatApi (data) {
//   return request({
//     url: '/wechatWarn/selectWechat',
//     method: 'post',
//     data:data
//   })
// }
// export function changeRowApi (data) {
//   return request({
//     url: '/system/wechatQRCode/update/userWechat',
//     method: 'post',
//     data:data
//   })
// }
// // 删除
// export function delRowApi (params) {
//   return request({
//     url: '/system/wechatQRCode/delete/userWechat',
//     method: 'get',
//     params:params
//   })
// }

// 删除未保存的新建
export function deleteNosavedApi (data) {
  return request({
    url: '/wechatWarn/deleteWarnId',
    method: 'post',
    data:data
  })
}

// 微信联系人查询
export function getContactApi (data) {
  return request({
    url: '/contact/get',
    method: 'post',
    data:data
  })
}
// 微信联系人绑定
export function bindContactApi (data) {
  return request({
    url: '/contact/bind',
    method: 'post',
    data:data
  })
}
// 预警绑定微信
export function bindContactToWarnApi (data) {
  return request({
    url: '/wechatWarn/bindWechat',
    method: 'post',
    data:data
  })
}