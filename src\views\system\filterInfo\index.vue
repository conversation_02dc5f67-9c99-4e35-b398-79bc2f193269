<template>
  <div class="home">
    <div class="filterBox">
      <el-form v-model="queryParams" :inline="true">
        <el-form-item label="文章标题：" prop="title">
          <el-input
            v-model.trim="queryParams.title"
            placeholder="请输入文章标题"
            clearable
            size="small"
          />
        </el-form-item>
        <el-form-item label="信源类型：" prop="type">
          <el-select
            v-model="queryParams.type"
            placeholder="请选择信源类型"
            clearable
            size="small"
            @change="changeType"
          >
            <el-option
              v-for="dict in medType"
              :key="dict.value"
              :label="dict.name"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="网站/媒体名称：" prop="assignIds">
          <el-select
            v-model="queryParams.assignIds"
            placeholder="请选择网站/媒体名称"
            clearable multiple filterable collapse-tags
            size="small"
          >
            <el-option
              v-for="dict in assignList"
              :key="dict.value"
              :label="dict.name"
              :value="dict.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="过滤时间：" prop>
          <!-- <el-date-picker
            v-model="queryParams.startTime"
            size="small"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择开始时间"
          ></el-date-picker
          >-
          <el-date-picker
            v-model="queryParams.endTime"
            size="small"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择结束时间"
          ></el-date-picker> -->

          <el-date-picker
            v-model="dataTime"
            @change="handleDateChange"
            size="small"
            unlink-panels
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" @click="searchFilter"
            >搜索</el-button
          >
          <el-button size="mini" type @click="cancelsearchFilter"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="cancelFilter"
            >取消过滤</el-button
          >
        </el-col>
      </el-row>
      <el-table v-loading="tableLoading" :data="liData" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          label="序号"
          :index="getIndex"
          width="80"
        ></el-table-column>
        <el-table-column
          label="文章标题"
          prop="title"
          align="center"
          width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="信源名称"
          prop="siteName"
          align="center"
        ></el-table-column>
        <el-table-column
          label="信源类型"
          prop="type"
          align="center"
          :formatter="formatEmployment"
        ></el-table-column>
        <el-table-column label="问题词语" align="center" prop="wrongWord" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="建议词语" prop="suggestWord" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link v-if="checkSuggestion(scope.row.suggestWord)" class="suggest-word" style="font-weight: 400;" :underline="false" type="primary" @click="goBaidu(scope.row.suggestWord)">{{ scope.row.suggestWord }}</el-link>
            <span v-else>{{ scope.row.suggestWord }}</span>
          </template>  
        </el-table-column>
        <el-table-column label="发文时间" prop="time" align="center"></el-table-column>
        <el-table-column
          label="过滤时间"
          prop="filterTime"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="cancelFilter(scope.row)"
              >取消过滤</el-button
            >
            <el-button type="text" @click="showDialog(scope.row)"
              >备注信息</el-button
            >
            <el-button type="text" @click="goDetails(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        style="margin: 20px 0px; float: right"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParams.pageNum"
        :page-sizes="[10, 15, 20]"
        :page-size="queryParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
      <!-- <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getInfoData"
      />-->
    </div>
    <el-dialog title="备注信息" :visible.sync="dialogFormVisible" width="30%">
      <el-form :model="form" :inline="true" class="formEl">
        <el-form-item
          label="备注:"
          prop="remark"
          label-width="120px"
          style="width: 100%"
        >
          <el-input
            v-model.trim="form.remark"
            type="textarea"
            style="width: 100%"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="sureRemark">确 定</el-button>
        <el-button @click="cancelsureRemark">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getInfoListApi,
  cancelFilterApi,
  seeInfoApi,
  sureRemarkApi,websiteList,mediaList
} from "@/api/conentReview/filterInfo";
import {checkSuggestion} from "@/utils/index"
export default {
  data() {
    return {
      checkSuggestion,
      liData: [],
      total: 0,
      dataTime:[],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: "",
        endTime: "",
        title: "",
        type: "",
      },
      medType: [
        { name: "网站", value: "0" },
        { name: "媒体账号", value: "1" },
      ],
      dialogFormVisible: false,
      form: {
        remark: "",
        id: "",
      },
      typeOptions: [],
      ids: [],
      // 非多个禁用
      multiple: true,
      assignList:[],
      websiteData:[],
      mediaData:[],
      tableLoading: false
    };
  },
  created() {
    this.getInfoData();
    this.queryAccoutList()
    // 获取类型
    this.getDicts("by_source_type").then((response) => {
      this.typeOptions = response.data;
    });
  },
  watch: {
    // "queryParams.endTime": {
    //   handler(newv, oldv) {
    //     if (newv < this.queryParams.startTime) {
    //       this.$message.error("请输入正确时间范围");
    //     }
    //   },
    // },
  },
  methods: {
    goBaidu(word) {
      if (checkSuggestion(word)) {
        window.open(`https://www.baidu.com/s?wd=${word}`, '_blank')
      }
    },
    handleDateChange(){
      if(this.dataTime && this.dataTime.length>0){
        this.queryParams.startTime = this.dataTime[0]
        this.queryParams.endTime = this.dataTime[1]
      }else{
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
    },
    // 获取账号名称
    async queryAccoutList(){
      let res = await websiteList()
      this.websiteData = res.data
      let resm = await mediaList()
      this.mediaData = resm.data
    },
    // 信源类型切换
    changeType(){
      if(this.queryParams.type=='0'){
        this.assignList = this.websiteData
      }else if(this.queryParams.type=='1'){
        this.assignList = this.mediaData
    }else{
      this.assignList = []
    }
    },
    formatEmployment(row) {
      return this.selectDictLabel(this.typeOptions, row.type);
    },
    async showDialog(row) {
      this.dialogFormVisible = true;
      let res = await seeInfoApi(row.id);
      this.form.remark = res.data.remark;
      this.form.id = res.data.id;
    },
    // 搜索
    searchFilter() {
      if (
        this.queryParams.startTime &&
        this.queryParams.endTime &&
        this.queryParams.startTime < this.queryParams.endTime
      ) {
        this.getInfoData();
      } else if (!this.queryParams.startTime && !this.queryParams.endTime) {
        this.getInfoData();
      } else {
        this.msgError("请输入正确时间范围");
      }
    },
    cancelsearchFilter() {
      this.dataTime=[];
      for (let key in this.queryParams) {
        this.$set(this.queryParams, key, "");
      }
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.getInfoData();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.multiple = !selection.length;
    },
    // 取消过滤
    cancelFilter(row) {
      let params = {
        infoIds: this.ids.length ? this.ids : "" || [row.id],
      };
      this.$confirm("是否确认取消过滤该数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let res = await cancelFilterApi(params);
        if (res.code == 200) {
          this.msgSuccess("取消成功");
          this.getInfoData();
        } else {
          this.msgError(res.msg);
        }
      });
    },
    // 备注信息查看和修改
    async sureRemark() {
      let params = {
        id: this.form.id,
        remark: this.form.remark,
      };
      let res = await sureRemarkApi(params);
      if (res.code == 200) {
        this.dialogFormVisible = false;
        this.msgSuccess("修改成功");
        this.getInfoData();
        this.form.id = "";
        this.form.remark = "";
      } else {
        this.dialogFormVisible = false;
        this.msgError(res.msg);
      }
    },
    // 取消
    cancelsureRemark() {
      for (let key in this.form) {
        this.dialogFormVisible = false;
        this.$set(this.form, key, "");
      }
    },

    getIndex(index) {
      return (
        (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
      );
    },
    //   分页
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getInfoData();
    },
    handleCurrentChange(val) {
      this.queryParams.pageNum = val;
      this.getInfoData();
    },
     //   跳转到详情页
     async goDetails (item) {
      console.log('item',item);
      const newPage = this.$router.resolve({
        path: "/conentReview/detailPage/index",
        query: {
          id: item.id,
          itemTime: item.time,
          itemSolrId: item.solrId,
          itemCheckTaskId: item.checkTaskId,
          questionTab:'12'
        },
      });
      window.open(newPage.href, "_blank");
    },
    //   详情页
    goDetail(item) {
      this.$router.push({
        path: "/conentReview/detailPage/index",
        query: {
          id: item.id,
          itemTime: item.time,
          itemSolrId: item.solrId,
          itemCheckTaskId: item.checkTaskId,
        },
      });
    },
    //   获取数据
    async getInfoData() {
      let params = {
        state: 2,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        title: this.queryParams.title,
        type: this.queryParams.type,
        startTime: this.queryParams.startTime,
        endTime: this.queryParams.endTime,
        assignIds:this.queryParams.assignIds?this.queryParams.assignIds.join(','):null
      };
      this.tableLoading = true
      let res = await getInfoListApi(params);
      this.tableLoading = false
      this.liData = res.rows;
      this.total = res.total;
    },
  },
};
</script>

<style scoped lang="scss">
.formEl ::v-deep.el-form-item--medium .el-form-item__content {
  width: 60%;
}
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: calc(100vh - 60px);
  .filterBox {
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    h2.filH2 {
      width: 100%;
      overflow: hidden;
      background: #f5f5f5;
      margin: 0;
      padding: 0;
      span {
        display: inline-block;
        color: #fff;
        background: #ed9e2f;
        height: 38px;
        line-height: 38px;
        padding: 0 10px;
        font-size: 14px;
      }
    }
    ul {
      margin: 0;
      padding: 0;
      width: 100%;
      overflow: hidden;
      li {
        list-style: none;
        height: 36px;
        line-height: 36px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-bottom: dotted 1px #ccc;
        font-size: 14px;
        cursor: pointer;
        padding: 0 10px;
        box-sizing: border-box;
        p {
          margin: 0;
          padding: 0;
          display: inline-block;
          width: 60%;
          text-indent: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      li:hover {
        background-color: #e9f7ff;
        border-left: 2px solid #2899dd;
        p {
          color: #1870c2;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
.suggest-word{
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  ::v-deep .el-link--inner{
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}
</style>
