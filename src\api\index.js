import request from '@/utils/request'

// 获取问题汇总数据
export function getQueDateApi (data) {
    return request({
        url: '/home/<USER>/list',
        method: 'post',
        data: data
    })
}
// 获取审校专题二级数据
export function getReviewTwoDataApi () {
    return request({
        url: '/task/checkTaskType/list',
        method: 'get',
    })
}
// 获取审校专题三层数据
export function getCheckLiThreeDataApi (query) {
    return request({
        url: '/task/checkTask/list',
        method: 'get',
        params: query
    })
}
// 获取网站数据
export function getWebDataApi (query) {
    return request({
        url: '/platform/website/list',
        method: 'get',
        params: query
    })
}
// 获取媒体数据
export function getMedDataApi (query) {
    return request({
        url: '/platform/mediacount/list',
        method: 'get',
        params: query
    })
}
// 当前问题-获取总体概况数据
export function getTotalDataApi (data) {
    return request({
        url: '/home/<USER>/getInfoCount',
        method: 'post',
        data: data
    })
}
// 历史问题-获取总体概况数据
export function getHistoryCount (data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data: data
    })
}
// 获取图片音频视频总体概况数据
export function getMediaCount (data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data: data
    })
}
// 查询
export function searchDataApi (data) {
    return request({
        url: '/home/<USER>/list',
        method: 'post',
        data: data
    })
}
export function checkTaskTree () {
    return request({
        url: '/task/checkTaskType/checkTaskTree',
        method: 'get'
    })
}
export function suredialogFormVisibleApi (data) {
    return request({
        url: '/task/checkTask/defined',
        method: 'post',
        data: data
    })
}
export function siteTypeTree (data) {
    return request({
        url: '/manage/siteType/siteTypeTree',
        method: 'post',
        data: data
    })
}
// 扫描问题
export function refresh (data) {
    return request({
        url: '/task/checkTaskInfo/refresh',
        method: 'post',
        data: data
    })
}
// 扫描问题
export function refreshUser (data) {
    return request({
        url: '/task/checkTaskInfo/refreshUserDefined',
        method: 'post',
        data: data
    })
}
// 复检问题
export function recheck (data) {
    return request({
        url: '/filterTaskInfoForCustomer',
        method: 'post',
        data: data
    })
}
// 获取刷新按钮后数据
export function getreashPApi () {
    return request({
        url: '/task/checkTaskInfo/getRefreshStatus',
        method: 'get',
    })
}

// 获取媒体一级数据 type:0-网站 1-媒体
export function checkMedTree (data) {
    return request({
        url: '/manage/siteType/siteTypeTreeByName',
        method: 'post',
        data: data
    })
}
export function siteTypeTreeStatistic (data) {
    return request({
        url: '/manage/siteType/siteTypeTreeStatistic',
        method: 'post',
        data: data
    })
}
// 获取历史问题汇总数据
export function searchHistory (data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data: data
    })
}
// 获取图片音频视频问题汇总数据
export function mediaSearchDataApi (data) {
    return request({
        url: '/query/home',
        method: 'post',
        data: data
    })
}
// http://localhost:8327/stage-api/manage/siteType/siteTypeTreeByName

// 导出数据前刷新
export function refreshApi () {
    return request({
        url: '/task/checkTaskInfo/exportVerify',
        method: 'get',
    })
}
// 获取导出数据总量
export function refTotal (data) {
    return request({
        url: '/task/checkTaskInfo/getExportRows',
        method: 'post',
        data
    })
}
// 获取导出完成百分比
export function refPercent () {
    return request({
        url: '/task/checkTaskInfo/getExportPercent',
        method: 'get',
    })
}

// 获取历史问题 - 导出数据总量
export function historyExport (data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data
    })
}
// 导出历史问题
export function historyExportnew (data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data
    })
}

//获取系统专题列表
export function getSystemTopicList (data) {
    return request({
        url: '/query/label',
        method: 'post',
        data
    })
}

// 数据检测列表数据导出
export function exportCheckDataApi (data) {
    return request({
        url: '/query/export',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}

// 首页问题汇总导出
export function exportSummaryApi (data) {
    return request({
        url: '/query/export/summary',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}

// 获取导出完成百分比
export function getExportPercent (id) {
    return request({
        url: '/task/checkTaskInfo/getExportPercent/'+ id,
        method: 'get',
    })
}
// 获取习惯
export function getUserExportHabits() {
    return request({
        url: `/export/habit/get`,
        method: 'post'
    })
}
// 保存习惯
export function saveUserExportHabits(data) {
    return request({
        url: `/export/habit/save`,
        method: 'post',
        data
    })
}