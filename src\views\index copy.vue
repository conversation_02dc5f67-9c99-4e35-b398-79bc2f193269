<template>
  <div class="app-container home">
    <div class="homeBox">
      <el-form
        ref="form"
        class="queryForm"
        :model="queryForm"
        label-width="100px"
        :inline="true"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="媒体类型：">
              <el-radio-group v-model="queryForm.type" @change="changeMedType">
                <el-radio
                  v-for="(item, index) in medType"
                  :key="index"
                  :value="item.value"
                  :label="item.value"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其他筛选项：">
              <!-- 一级分类 -->
              <ul class="checkTypeUl">
                <li
                  v-for="(item, index) in checkType"
                  @click="clickLiTwo(index)"
                  :class="curLiTwo == index ? 'active' : ''"
                  :key="index"
                >
                  {{ item.name }}
                  <i v-show="curLiTwo == index" class="el-icon-arrow-down"></i>
                  <p
                    style="margin: 0; padding: 0; text-align: center"
                    v-show="item.checkArrLen"
                  >
                    {{ item.checkArrLen }}
                  </p>
                </li>
                <li>
                  <i
                    class="el-icon-setting"
                    @click="drawerOpen"
                    style="font-size: 16px; vertical-align: middle"
                  ></i>
                </li>
              </ul>
              <!-- 审校专题 -->
              <div class="showCheckTypeOne" v-show="showReview">
                <div
                  class="second-check"
                  @click="secondCheck"
                  :class="secondAct ? 'active' : ''"
                >
                  全选
                </div>
                <!-- 二级分类 -->
                <ul class="showTypeOne">
                  <li
                    v-for="(itema, index) in filterDataTree"
                    ref="dateTree"
                    @click="checkLiThrees(index, itema)"
                    :class="
                      itema.checkTaskList.length == 0
                        ? itema.tag
                          ? 'active'
                          : ''
                        : itema.single.length > 0
                        ? 'active'
                        : ''
                    "
                    :key="itema.id"
                  >
                    <el-popover
                      placement="bottom-start"
                      width="400"
                      trigger="hover"
                    >
                      <!-- 三级分类 -->
                      <div class="third-kinds">
                        <div>
                          <span
                            @click="checkAllThird(itema, index)"
                            :class="itema.checkAll ? 'active' : ''"
                            >全选</span
                          >
                        </div>
                        <div
                          v-for="(itemb, indexb) in itema.checkTaskList"
                          :key="indexb"
                          class="third-item"
                        >
                          <span
                            :class="itemb.tag ? 'active' : ''"
                            @click="checkSingle(itemb.id, index, indexb)"
                            >{{ itemb.name }}</span
                          >
                        </div>
                      </div>
                      <span
                        slot="reference"
                        v-if="itema.checkTaskList.length > 0"
                        >{{ itema.name }} <i class="el-icon-arrow-down"></i
                      ></span>
                    </el-popover>
                    <span v-if="itema.checkTaskList.length == 0">{{
                      itema.name
                    }}</span>
                  </li>
                </ul>
                <div style="text-align: right">
                  <el-button
                    type="primary"
                    @click="closeShowReview"
                    size="small"
                    >确定</el-button
                  >
                  <el-button size="small" @click="cancelShowReview"
                    >取消</el-button
                  >
                </div>
              </div>
              <!-- 媒体 -->
              <div class="showCheckTypeOne" v-show="showMed">
                <!-- <div class="second-check" @click="secondCheckMedia" :class="secondActMedia?'active':''">全选</div> -->
                <!-- 媒体二级分类 -->
                <!-- <ul class="showTypeOne">
                  <li v-for="(itema,index) in mediaDataTree" ref="dateTree"
                      @click="checkLiThreesMedia(index,itema)"
                      :class="itema.mediaAccountList.length==0?itema.tag?'active':'':itema.single.length>0?'active':''"
                      :key="itema.id">
                       <el-popover
                        placement="top-start"
                        width="400"
                        trigger="click">
                        <div class="third-kinds">
                          <div><span @click="checkAllThirdMedia(itema,index)" :class="itema.checkAll?'active':''">全选</span></div>
                          <div v-for="(itemb,indexb) in itema.mediaAccountList" :key="indexb" class="third-item">
                            <span :class="itemb.tag?'active':''" @click="checkSingleMedia(itemb.id,index,indexb)">{{itemb.name}}</span>
                          </div>
                        </div>
                      <span slot="reference" v-if="itema.mediaAccountList.length>0">{{itema.name}} ^</span>
                      </el-popover>
                      <span v-if="itema.mediaAccountList.length==0">{{itema.name}}</span>
                      
                      </li>
                </ul>   -->
                <el-form :model="formMedSearch">
                  <el-form-item label="媒体名称:">
                    <el-input
                      v-model.trim="formMedSearch.name"
                      placeholder="请输入媒体名称"
                      size="small"
                      clearable=""
                      @clear="getDiffChange"
                    ></el-input>
                  </el-form-item>
                  <el-button size="mini" type="primary" @click="getMedDataFunc"
                    >确定</el-button
                  >
                </el-form>
                <el-checkbox
                  :indeterminate="isIndeterminate"
                  v-model="checkAll"
                  @change="handleCheckAllChange"
                  >全选</el-checkbox
                >
                <el-checkbox-group
                  v-model="queryForm.assignIds"
                  @change="changeCheckTypeTwo"
                >
                  <el-checkbox
                    v-for="(item, index) in getWebData"
                    :label="item.id"
                    :key="index"
                    >{{ item.name }}</el-checkbox
                  >
                </el-checkbox-group>
                <div style="text-align: right">
                  <el-button type="primary" @click="closeShowMed" size="small"
                    >确定</el-button
                  >
                  <el-button size="small" @click="cancelShowMed"
                    >取消</el-button
                  >
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="20">
            <el-form-item label="时间范围：">
              <ul class="timeUl">
                <li
                  v-for="(item, index) in timelist"
                  :key="index"
                  @click="clickLi(index, item)"
                  :class="curLi == index ? 'active' : ''"
                >
                  {{ item.name }}
                </li>
                <el-date-picker
                  v-model="timeOne"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="mini"
                  v-if="curLi == 5"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                >
                </el-date-picker>
              </ul>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: center; margin-top: 10px">
            <el-button
              v-for="(item, index) in btnList"
              :key="index"
              size="mini"
              :loading="item.name == '导出' ? exportLoading : false"
              @click="btnListClick(index)"
              :type="currtIndex == index ? 'primary' : ''"
              >{{ item.name }}</el-button
            >
          </el-col>
          <el-col :span="24" style="text-align: left; margin: 10px 0 10px 20px">
            <el-button
              size="mini"
              type="primary"
              @click="refreshClick"
              :loading="freshLoading"
              >刷新</el-button
            >
            <p class="pData" v-show="reashP">{{ reashP }}</p>
          </el-col>
        </el-row>
      </el-form>
      <div class="totalView" v-loading="loadingTwo">
        <h2>总体概况</h2>
        <div class="totalBox">
          <div class="boxOne">
            <span>{{ totalDataList.searchsiteCount }}<em>个</em></span>
            <p>检测网站数</p>
          </div>
          <div class="boxTwo">
            <span>{{ totalDataList.searchInfoCount }}<em>个</em></span>
            <p>检测内容数</p>
          </div>
          <div class="boxThree">
            <span>{{ totalDataError }}<em>个</em></span>
            <p>全部疑似错误数</p>
          </div>
          <div class="boxFour">
            <span>{{ totalDataList.severeInfoCount }}<em>个</em></span>
            <p>严重错误数</p>
          </div>
          <div class="boxFive">
            <span>{{ totalDataList.generalInfoCount }}<em>个</em></span>
            <p>一般错误数</p>
          </div>
          <div class="boxSix">
            <span>{{ totalDataList.weakInfoCount }}<em>个</em></span>
            <p>自定义错误数</p>
          </div>
        </div>
      </div>
    </div>
    <div class="homeBoxTwo">
      <div class="totalView">
        <h2>问题汇总</h2>
      </div>
      <div class="homeTable">
        <el-table
          :data="tableData"
          stripe
          style="width: 100%"
          v-loading="loading"
        >
          <el-table-column
            type="index"
            label="序号"
            :index="getIndex"
            width="80"
          ></el-table-column>
          <el-table-column prop="siteName" label="网站名称"> </el-table-column>
          <el-table-column prop="host" label="网站地址"> </el-table-column>
          <el-table-column prop="infoCount" label="文章数"> </el-table-column>
          <el-table-column prop="severeCount" label="严重错误数">
          </el-table-column>
          <el-table-column prop="generalCount" label="一般错误数">
          </el-table-column>
          <el-table-column prop="weakCount" label="自定义错误数">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="goDetail(scope.row)"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          style="margin: 20px 0px; float: right"
          :total="total"
          :page.sync="pageNum"
          :limit.sync="pageSize"
          @pagination="btnListClick(0)"
        />
      </div>
    </div>
    <div class="customThem" @click="customClick">
      <p>自定义</p>
      <p>专题</p>
    </div>
    <!-- 自定义专题弹框 -->
    <el-dialog
      title="自定义专题"
      width="500px"
      :visible.sync="dialogFormVisible"
    >
      <div slot="title" class="header-title">
        <span class="title-age">自定义专题</span>
        <el-tooltip effect="light" placement="right-start">
          <div slot="content">添加自定义专题<br />的关键词</div>
          <span class="title-name"
            ><i
              class="el-icon-question icon-report"
              style="color: #e6a23c; margin-left: 10px; cursor: pointer"
            ></i
          ></span>
        </el-tooltip>
      </div>
      <el-form
        :model="formCustom"
        label-width="100px"
        :rules="rules"
        ref="form"
      >
        <el-form-item label="自定义专题：">
          <el-input v-model.trim="formCustom.name" disabled></el-input>
        </el-form-item>
        <el-form-item label="自定义词：" prop="keyword1">
          <el-input v-model.trim="formCustom.keyword1" autocomplete="off"></el-input>
          <p style="margin: 0; padding: 0; color: #ccc">
            (可快捷添加自定义专题的关键词，多个词用空格隔开)
          </p>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="canceldialogFormVisible">取 消</el-button>
        <el-button type="primary" @click="suredialogFormVisible"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-drawer
      title="其他筛选设置"
      :visible.sync="drawer"
      :with-header="true"
      @closed="cancelShowReviewTwo"
    >
      <div class="showCheckTypeOne addshowCheckTypeOne">
        <div class="titleBox">
          <h3 class="elasticTitle">审校专题</h3>
          <div
            class="second-check"
            @click="secondCheck"
            :class="secondAct ? 'active' : ''"
          >
            全选
          </div>
        </div>
        <!-- 二级分类 -->
        <ul class="showTypeOne">
          <li
            v-for="(itema, index) in filterDataTree"
            ref="dateTree"
            @click="checkLiThrees(index, itema)"
            :class="
              itema.checkTaskList.length == 0
                ? itema.tag
                  ? 'active'
                  : ''
                : itema.single.length > 0
                ? 'active'
                : ''
            "
            :key="itema.id"
          >
            <el-popover placement="bottom-start" width="400" trigger="hover">
              <!-- 三级分类 -->
              <div class="third-kinds">
                <div>
                  <span
                    @click="checkAllThird(itema, index)"
                    :class="itema.checkAll ? 'active' : ''"
                    >全选</span
                  >
                </div>
                <div
                  v-for="(itemb, indexb) in itema.checkTaskList"
                  :key="indexb"
                  class="third-item"
                >
                  <span
                    :class="itemb.tag ? 'active' : ''"
                    @click="checkSingle(itemb.id, index, indexb)"
                    >{{ itemb.name }}</span
                  >
                </div>
              </div>
              <span slot="reference" v-if="itema.checkTaskList.length > 0"
                >{{ itema.name }} <i class="el-icon-arrow-down"></i
              ></span>
            </el-popover>
            <span v-if="itema.checkTaskList.length == 0">{{ itema.name }}</span>
          </li>
        </ul>
      </div>
      <div class="showCheckTypeOne addshowCheckTypeOne">
        <!-- <div class="second-check" @click="secondCheckMedia" :class="secondActMedia?'active':''">全选</div> -->
        <!-- 媒体二级分类 -->
        <!-- <ul class="showTypeOne">
                  <li v-for="(itema,index) in mediaDataTree" ref="dateTree"
                      @click="checkLiThreesMedia(index,itema)"
                      :class="itema.mediaAccountList.length==0?itema.tag?'active':'':itema.single.length>0?'active':''"
                      :key="itema.id">
                       <el-popover
                        placement="top-start"
                        width="400"
                        trigger="click">
                        <div class="third-kinds">
                          <div><span @click="checkAllThirdMedia(itema,index)" :class="itema.checkAll?'active':''">全选</span></div>
                          <div v-for="(itemb,indexb) in itema.mediaAccountList" :key="indexb" class="third-item">
                            <span :class="itemb.tag?'active':''" @click="checkSingleMedia(itemb.id,index,indexb)">{{itemb.name}}</span>
                          </div>
                        </div>
                      <span slot="reference" v-if="itema.mediaAccountList.length>0">{{itema.name}} ^</span>
                      </el-popover>
                      <span v-if="itema.mediaAccountList.length==0">{{itema.name}}</span>
                      
                      </li>
                </ul>   -->

        <h3 class="elasticTitle addelasticTitle">媒体</h3>
        <el-form :model="formMedSearch" :inline="true">
          <el-form-item label="媒体名称:">
            <el-input
              v-model.trim="formMedSearch.name"
              placeholder="请输入媒体名称"
              size="small"
              clearable=""
              @clear="getDiffChange"
            ></el-input>
          </el-form-item>
          <el-button
            size="mini"
            type="primary"
            @click="getMedDataFunc"
            class="butDif"
            >确定</el-button
          >
        </el-form>
        <el-checkbox
          :indeterminate="isIndeterminate"
          v-model="checkAll"
          @change="handleCheckAllChange"
          class="checkMoreDialog"
          >全选</el-checkbox
        >
        <el-checkbox-group
          v-model="queryForm.assignIds"
          @change="changeCheckTypeTwo"
          class="checkMoreDialog"
        >
          <el-checkbox
            v-for="(item, index) in getWebData"
            :label="item.id"
            :key="index"
            >{{ item.name }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
      <div style="text-align: center">
        <el-button type="primary" @click="closeShowReviewTwo" size="small"
          >确定</el-button
        >
        <el-button size="small" @click="cancelShowReviewTwo">取消</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  getReviewTwoDataApi,
  getCheckLiThreeDataApi,
  getWebDataApi,
  getMedDataApi,
  getTotalDataApi,
  searchDataApi,
  suredialogFormVisibleApi,
  checkTaskTree,
  siteTypeTree,
  refresh,
  getreashPApi,
} from "@/api/index";
import { exportAllApi } from "@/api/conentReview/themMangeChase";
import { timeJson } from "@/utils/time.js";
export default {
  name: "index",
  data() {
    return {
      reashP: "",
      drawer: false,
      loadingTwo: false,
      loading: false,
      formMedSearch: {},
      freshLoading: false,
      mediaDataTree: [],
      secondActMedia: false,
      websiteDataTree: [],
      exportLoading: false,
      secondAct: false,
      filterDataTree: [],
      checkAllTwo: false,
      isIndeterminateTwo: false,
      checkAll: false,
      isIndeterminate: false,
      reviewTwoData: [],
      checkTaskIds: [],
      mediaAssignIds: [],
      queryForm: {
        type: "0",
        assignIds: [],
        checkTaskId: [],
        timeRound: 1,
      },
      timeOne: timeJson.threeMonth,
      medType: [
        { name: "网站", value: "0" },
        { name: "媒体账号", value: "1" },
      ],
      btnList: [{ name: "查询" }, { name: "导出" }, { name: "报告预览" }],
      currtIndex: 0,
      tableData: [],
      timelist: [
        { name: "今天", value: "0" },
        { name: "24小时", value: "1" },
        // { name: '二天', value: '2' },
        { name: "三天", value: "2" },
        { name: "七天", value: "3" },
        { name: "三十天", value: "4" },
        { name: "自定义", value: "5" },
      ],
      errorList: [
        { name: "全选" },
        { name: "严重错误" },
        { name: "一般错误" },
        { name: "疑似错误" },
      ],
      checkType: [
        { name: "审校专题", value: "1" },
        { name: "媒体", value: "2" },
      ],
      curLi: 1,
      curLiTwo: 0,
      curLiThree: 0,
      errorLi: 1,
      pageSize: 10,
      pageNum: 1,
      total: 0,
      getCheckLiThreeData: [],
      getWebData: [],
      showReview: false,
      showMed: false,
      totalDataList: [],
      totalDataError: "",
      dialogFormVisible: false,
      formCustom: {
        name: "自定义专题",
        keyword1: "",
      },
      rules: {
        keyword1: [
          {
            required: true,
            message: "请输入自定义词",
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    checkTaskIds: {
      handler(newv, oldv) {
        this.queryForm.checkTaskId = newv;
      },
    },
  },
  created() {
    // this.getReviewTwoData()
    this.getTreeData();
    this.getTotalData();
    // this.getSiteTypeTree()
    this.btnListClick(0);
    this.getreashP();
  },
  methods: {
    // 获取刷新按钮后面数据
    async getreashP() {
      let res = await getreashPApi();
      this.reashP = res.msg;
    },
    drawerOpen() {
      this.drawer = true;
      this.showReview = false;
      this.showMed = false;
      this.getDiffChange();
    },
    // 刷新
    async refreshClick() {
      try {
        this.freshLoading = true;
        let res = await refresh({ id: null });
        if (res.code == 200) {
          this.freshLoading = false;
          this.msgSuccess(res.msg);
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 获取媒体树
    getSiteTypeTree() {
      // type:1 网站；2 媒体
      siteTypeTree({ type: 1 }).then((res) => {
        this.$nextTick(() => {
          this.websiteDataTree = res.data;
          this.websiteDataTree.map((item) => {
            item.tag = false;
            item.checkAll = false;
            item.single = [];
            if (item.websiteList.length != 0) {
              item.websiteList.map((itemb) => {
                itemb.tag = false;
              });
            }
          });
        });
      });
      siteTypeTree({ type: 2 }).then((res) => {
        this.$nextTick(() => {
          this.mediaDataTree = res.data;
          this.mediaDataTree.map((item) => {
            item.tag = false;
            item.checkAll = false;
            item.single = [];
            if (item.mediaAccountList.length != 0) {
              item.mediaAccountList.map((itemb) => {
                itemb.tag = false;
              });
            }
          });
        });
      });
    },
    // 二级全选
    secondCheck() {
      this.secondAct = !this.secondAct;
      if (this.secondAct) {
        this.filterDataTree.map((item) => {
          item.tag = true;
          item.checkAll = true;
          if (item.checkTaskList.length > 0) {
            item.checkTaskList.map((itemb) => (itemb.tag = true));
            let id = item.checkTaskList.map((itemb) => itemb.id);
            item.single = id;
            this.checkTaskIds.push(...id);
          } else {
            this.checkTaskIds.push(item.id);
          }
        });
        this.$forceUpdate();
      } else {
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          if (item.checkTaskList.length > 0) {
            item.checkTaskList.map((itemb) => (itemb.tag = false));
            item.single = [];
          }
          this.checkTaskIds = [];
        });
        this.$forceUpdate();
      }
    },
    // 三级单选
    checkSingle(item, index, indexb) {
      this.filterDataTree[index].checkTaskList[indexb].tag =
        !this.filterDataTree[index].checkTaskList[indexb].tag;
      if (this.filterDataTree[index].checkTaskList[indexb].tag) {
        this.filterDataTree[index].single.push(item);
        this.checkTaskIds.push(item);
      } else {
        if (this.checkTaskIds.indexOf(item) != -1) {
          this.checkTaskIds.splice(this.checkTaskIds.indexOf(item), 1);
        }
        if (this.filterDataTree[index].single.indexOf(item) != -1) {
          this.filterDataTree[index].single.splice(
            this.filterDataTree[index].single.indexOf(item),
            1
          );
        }
      }
      if (
        this.filterDataTree[index].single.length ==
        this.filterDataTree[index].checkTaskList.length
      ) {
        this.filterDataTree[index].checkAll = true;
      } else {
        this.filterDataTree[index].checkAll = false;
      }
      this.$forceUpdate();
    },
    // 全选三级
    checkAllThird(item, index) {
      this.filterDataTree[index].checkAll =
        !this.filterDataTree[index].checkAll;
      let id = this.filterDataTree[index].checkTaskList.map((item) => {
        return item.id;
      });
      if (this.filterDataTree[index].checkAll) {
        // 全选
        this.checkTaskIds.push(...id);
        this.filterDataTree[index].single = id;
        this.filterDataTree[index].checkTaskList.map(
          (item) => (item.tag = true)
        );
      } else {
        // 全不选
        this.filterDataTree[index].checkTaskList.map(
          (item) => (item.tag = false)
        );
        this.filterDataTree[index].single = [];
        id.map((item) => {
          if (this.checkTaskIds.indexOf(item) != -1) {
            this.checkTaskIds.splice(this.checkTaskIds.indexOf(item), 1);
          }
        });
      }
      this.$forceUpdate();
    },
    customClick() {
      this.dialogFormVisible = true;
    },
    // 自定义专题确定按钮
    suredialogFormVisible() {
      let params = {
        name: this.formCustom.name,
        keyword1: this.formCustom.keyword1,
      };
      this.$refs["form"].validate((valid) => {
        if (valid) {
          suredialogFormVisibleApi(params).then((res) => {
            if (res.code == 200) {
              this.$message.success("新增自定义专题成功");
              this.dialogFormVisible = false;
              this.formCustom.name = "自定义专题";
              this.formCustom.keyword1 = "";
            }
          });
        }
      });
    },
    // 取消自定义专题按钮
    canceldialogFormVisible() {
      this.formCustom.name = "自定义专题";
      this.formCustom.keyword1 = "";
      this.dialogFormVisible = false;
      this.$refs["form"].clearValidate("keyword1");
    },
    //   三级全选
    handleCheckAllChangeTwo(e) {
      if (e == true) {
        let aaa = [];
        for (let i = 0; i < this.getCheckLiThreeData.length; i++) {
          aaa.push(this.getCheckLiThreeData[i].id);
        }
        this.queryForm.checkTaskId = aaa;
        this.isIndeterminateTwo = false;
      } else {
        this.queryForm.checkTaskId = [];
        this.isIndeterminateTwo = false;
      }
    },
    //   全选
    handleCheckAllChange(val) {
      if (val == true) {
        let aaa = [];
        for (let i = 0; i < this.getWebData.length; i++) {
          aaa.push(this.getWebData[i].id);
        }
        this.queryForm.assignIds = aaa;
        this.checkType[1].checkArrLen = this.queryForm.assignIds.length;
        this.isIndeterminate = false;
      } else {
        this.queryForm.assignIds = [];
        this.checkType[1].checkArrLen = 0;
        this.isIndeterminate = false;
      }
    },
    closeShowReview() {
      this.queryForm.checkTaskId = this.checkTaskIds;
      this.checkType[0].checkArrLen = this.queryForm.checkTaskId.length;

      this.showReview = false;
    },
    closeShowReviewTwo() {
      this.queryForm.checkTaskId = this.checkTaskIds;
      this.checkType[0].checkArrLen = this.queryForm.checkTaskId.length;
      this.checkType[1].checkArrLen = this.queryForm.assignIds.length;
      this.drawer = false;
    },
    closeShowMed() {
      this.showMed = false;
    },
    cancelShowReview() {
      this.showReview = false;
      this.filterDataTree.map((item) => {
        item.tag = false;
        item.checkAll = false;
        if (item.checkTaskList.length > 0) {
          item.checkTaskList.map((itemb) => (itemb.tag = false));
          item.single = [];
        }
        this.checkTaskIds = [];
        this.secondAct = false;
      });
      this.$forceUpdate();
      this.queryForm.checkTaskId = [];
      this.checkAllTwo = false;
    },
    cancelShowReviewTwo() {
      this.queryForm.checkTaskId = this.checkTaskIds;
      this.checkType[0].checkArrLen = this.queryForm.checkTaskId.length;
      this.drawer = false;
    },
    cancelShowMed() {
      this.checkAll = false;
      this.isIndeterminate = false;
      this.queryForm.assignIds = [];
      this.checkType[1].checkArrLen = 0;
      this.showMed = false;
    },
    changeMedType(val) {
      this.getDiffChange();
    },
    //   点击打开审校和媒体弹框
    changeCheckTypeOne(value) {
      let checkedCount = value.length;
      this.checkAllTwo = checkedCount === this.getCheckLiThreeData.length;
      this.isIndeterminateTwo =
        checkedCount > 0 && checkedCount < this.getCheckLiThreeData.length;
      this.queryForm.checkTaskId = value;
    },
    changeCheckTypeTwo(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.getWebData.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.getWebData.length;
      this.queryForm.assignIds = value;
      this.checkType[1].checkArrLen = this.queryForm.assignIds.length;
    },
    // 获取审校专题二级数据
    async getTreeData() {
      let resTree = await checkTaskTree();
      this.$nextTick(() => {
        this.filterDataTree = resTree.data;
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          item.single = [];
          if (item.checkTaskList.length != 0) {
            item.checkTaskList.map((itemb) => {
              itemb.tag = false;
            });
          }
        });
      });
    },
    // 获取审校专题二级数据
    async getReviewTwoData() {
      let res = await getReviewTwoDataApi();
      this.reviewTwoData = res.rows;
      this.reviewTwoData.map((item) => {
        item.tag = false;
      });
      this.checkLiThree(0, this.reviewTwoData[0].id);
      //   this.checkLiThree()
    },
    //   点击切换按钮颜色
    async btnListClick(index) {
      this.currtIndex = index;
      if (index == 0) {
        //   查询
        this.loading = true;
        if (this.queryForm.timeRound == 5 && !this.timeOne) {
          this.$message.error("请选择自定义时间范围");
        } else {
          this.getTotalData();
          this.showReview = false;
          this.showMed = false;
          let params = {
            type: this.queryForm.type,
            startTime: this.timeOne[0],
            endTime: this.timeOne[1],
            checkTaskId: this.queryForm.checkTaskId.join(),
            pageIndex: this.pageNum,
            pageSize: this.pageSize,
            assignIds: this.queryForm.assignIds.join(),
            timeRound: this.queryForm.timeRound,
          };
          sessionStorage.setItem("queryForm", JSON.stringify(params));
          let res = await searchDataApi(params);
          // 问题汇总数据
          this.tableData = res.rows;
          if (this.tableData) {
            this.loading = false;
          }
          this.total = res.total;
        }
      } else if (index == 1) {
        if (this.queryForm.timeRound == 5 && !this.timeOne) {
          this.$message.error("请选择自定义时间范围");
        } else {
          //   导出
          let params = {
            type: this.queryForm.type,
            startTime: this.timeOne[0],
            endTime: this.timeOne[1],
            checkTaskId: this.queryForm.checkTaskId.join(),
            assignIds: this.queryForm.assignIds.join(),
            timeRound: this.queryForm.timeRound,
          };

          this.$confirm("您确定要导出数据吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
          })
            .then(() => {
              this.exportLoading = true;
              return exportAllApi(params);
            })
            .then((response) => {
              let that = this;
              that.exportLoading = false;
              this.download(response.msg);
            });
        }
      } else if (index == 2) {
        //   报告预览
        if (this.queryForm.timeRound == 5 && !this.timeOne) {
          this.$message.error("请选择自定义时间范围");
        } else {
          let params = {
            type: this.queryForm.type,
            startTime: this.timeOne[0],
            endTime: this.timeOne[1],
            checkTaskId: this.queryForm.checkTaskId.join(),
            pageIndex: this.pageNum,
            pageSize: this.pageSize,
            assignIds: this.queryForm.assignIds.join(),
            timeRound: this.queryForm.timeRound,
          };
          sessionStorage.setItem("reportQueryForm", JSON.stringify(params));
          const newRoute = this.$router.resolve({
            path: "/indexPageSee",
          });
          window.open(newRoute.href, "_blank");
        }
      }
    },
    // 序号连续
    getIndex(index) {
      return (this.pageNum - 1) * this.pageSize + index + 1;
    },
    // 时间选择
    clickLi(index, item) {
      this.curLi = index;
      this.queryForm.timeRound = item.value;
    },
    // 类型选择
    clickLiTwo(index) {
      this.curLiTwo = index;
      //   this.curLiThree = 0
      if (index == 0) {
        this.showReview = true;
        this.showMed = false;
        // this.getReviewTwoData()
      } else if (index == 1) {
        this.showMed = true;
        this.showReview = false;
        this.getDiffChange();
      }
    },
    async getDiffChange() {
      if (this.queryForm.type == 0) {
        // 网站
        let params = {
          pageNum: 1,
          pageSize: 50,
        };
        let res = await getWebDataApi(params);
        this.getWebData = res.rows;
      } else if (this.queryForm.type == 1) {
        // 媒体账号
        this.getMedDataFunc();
      }
    },
    // 获取媒体账号数据
    async getMedDataFunc() {
      let params = {
        pageNum: 1,
        pageSize: 50,
        name: this.formMedSearch.name,
      };
      if (this.queryForm.type == 0) {
        // 网站
        let res = await getWebDataApi(params);
        this.getWebData = res.rows;
      } else {
        // 媒体账号
        let res = await getMedDataApi(params);
        this.getWebData = res.rows;
      }
    },

    async checkLiThree(index, id) {
      this.curLiThree = index;
      this.isIndeterminateTwo = false;
      //   获取三层数据
      let params = {
        checkTaskTypeId: id,
      };
      let res = await getCheckLiThreeDataApi(params);
      this.getCheckLiThreeData = res.rows;
      let aa = [];
      for (let i = 0; i < this.getCheckLiThreeData.length; i++) {
        aa.push(this.getCheckLiThreeData[i].id);
      }
      if (this.isContained(this.queryForm.checkTaskId, aa)) {
        this.checkAllTwo = true;
      } else {
        this.checkAllTwo = false;
      }
      if (this.getCheckLiThreeData.length == 0) {
        this.checkAllTwo = false;
      }
    },
    checkLiThrees(index, item) {
      this.filterDataTree[index].tag = !this.filterDataTree[index].tag;
      if (item.checkTaskList.length == 0) {
        if (this.filterDataTree[index].tag) {
          this.checkTaskIds.push(item.id);
        } else {
          if (this.checkTaskIds.indexOf(item.id) != -1) {
            this.checkTaskIds.splice(this.checkTaskIds.indexOf(item.id), 1);
          }
        }
      }
      this.$forceUpdate();
    },
    //是否被包含,是返回true,不是返回false a包含b
    isContained(a, b) {
      if (!(a instanceof Array) || !(b instanceof Array)) return false;
      if (a.length < b.length) return false;
      var aStr = a.toString();
      console.info(aStr);
      for (var i = 0, len = b.length; i < len; i++) {
        console.info(aStr.indexOf(b[i]));
        if (aStr.indexOf(b[i]) == -1) return false;
      }
      return true;
    },
    // 点击切换错误类型
    clickerrorLi(index) {
      this.errorLi = index;
    },
    goDetail(row) {
      this.$router.push({
        path: "/conentReview/indexDetail/index",
        query: { titName: row.siteName, assId: row.siteId },
      });
    },
    // 获取总体概况数据
    async getTotalData() {
      this.loadingTwo = true;
      if (this.curLi == 5) {
        let params = {
          type: this.queryForm.type,
          startTime: this.timeOne[0],
          endTime: this.timeOne[1],
          timeRound: this.queryForm.timeRound,
          checkTaskId: this.queryForm.checkTaskId.join(),
          assignIds: this.queryForm.assignIds.join(),
        };
        let res = await getTotalDataApi(params);
        if (res) {
          this.loadingTwo = false;
        }
        this.totalDataList = res;
        this.totalDataError =
          this.totalDataList.severeInfoCount +
          this.totalDataList.generalInfoCount +
          this.totalDataList.weakInfoCount;
      } else {
        let params = {
          type: this.queryForm.type,
          timeRound: this.queryForm.timeRound,
          checkTaskId: this.queryForm.checkTaskId.join(),
          assignIds: this.queryForm.assignIds.join(),
        };
        let res = await getTotalDataApi(params);
        if (res) {
          this.loadingTwo = false;
        }
        this.totalDataList = res;
        this.totalDataError =
          this.totalDataList.severeInfoCount +
          this.totalDataList.generalInfoCount +
          this.totalDataList.weakInfoCount;
      }
    },
  },
};
</script>
<style >
.el-message-box__header {
  border-bottom: solid 1px #ccc;
}
</style>

<style scoped lang="scss">
.pData {
  margin: 0;
  padding: 0;
  display: inline-block;
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  font-size: 12px;
  color: #999;
}
.checkMoreDialog {
  ::v-deep .el-checkbox__label {
    font-size: 16px;
    color: #333;
  }
}
.butDif {
  margin-top: 4px;
}
::v-deep .el-checkbox {
  height: 36px;
  line-height: 36px;
}
.titleBox {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.elasticTitle {
  display: inline-block;
  padding: 0;
  margin: 0;
}
.addelasticTitle {
  margin-bottom: 30px !important;
}
.third-kinds {
  span {
    display: inline-block;
    padding: 2px 10px;
    border: 1px solid #f7f7f7;
    margin-bottom: 10px;
    margin-right: 5px;
    cursor: pointer;
    &.active {
      border: 1px solid #3d9ffe;
    }
  }
  .third-item {
    display: inline-block;
  }
}
::v-deep .el-form--inline .el-form-item {
  margin-bottom: 0px;
}
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: 889px;
  position: relative;
  .homeBox {
    width: 100%;
    background: #fff;
    border-top: solid 3px #3d9ffe;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    padding: 20px 0px;
    box-sizing: border-box;
    margin-bottom: 20px;
    .queryForm {
      padding: 0px 10px;
      box-sizing: border-box;
    }
    .timeUl {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin: 0;
      padding: 0;
      margin-top: 4px;
      li {
        margin-right: 16px;
        list-style: none;
        color: #606266;
        cursor: pointer;
        height: 28px;
        line-height: 28px;
        padding: 0 6px;
        border-radius: 3px;
      }
      li.active {
        border: solid 1px #3d9ffe;
        color: #3d9ffe;
      }
      li.activeErrorLi {
        border: solid 1px #3d9ffe;
        color: #3d9ffe;
      }
    }
    .checkTypeUl {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin: 0;
      padding: 0;
      margin-top: 4px;
      position: relative;
      li {
        margin-right: 16px;
        list-style: none;
        color: #606266;
        cursor: pointer;
        height: 28px;
        line-height: 28px;
        padding: 0 6px;
        border-radius: 3px;
      }
      li.active {
        border: solid 1px #3d9ffe;
        color: #3d9ffe;
      }
      li.activeErrorLi {
        border: solid 1px #3d9ffe;
        color: #3d9ffe;
      }
    }
  }
}
.totalView {
  width: 100%;
  overflow: hidden;
  h2 {
    font-size: 18px;
    border-left: solid 3px #3d9ffe;
    margin-left: 25px;
    font-weight: normal;
    padding-left: 10px;
  }
  .totalBox {
    width: 100%;
    padding: 0 25px;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    div {
      height: 115px;
      width: 18%;
      margin-right: 0.6%;
      text-align: center;
      color: #fff;
      padding: 20px 60px 20px 0px;
      box-sizing: border-box;
      text-align: right;
      cursor: pointer;
      &:last-child {
        margin-right: 0%;
      }
      span {
        display: block;
        font-size: 26px;
      }
      em {
        font-style: normal;
        font-size: 14px;
      }
      p {
        font-size: 14px;
      }
    }
    div.boxOne {
      background: url("./../assets/images/boxOne.png") no-repeat center center;
      background-size: 100%;
    }
    div.boxTwo {
      background: url("./../assets/images/boxTwo.png") no-repeat center center;
      background-size: 100%;
    }
    div.boxThree {
      background: url("./../assets/images/boxThree.png") no-repeat center center;
      background-size: 100%;
    }
    div.boxFour {
      background: url("./../assets/images/boxFour.png") no-repeat center center;
      background-size: 100%;
    }
    div.boxFive {
      background: url("./../assets/images/boxFive.png") no-repeat center center;
      background-size: 100%;
    }
    div.boxSix {
      background: url("./../assets/images/boxSix.png") no-repeat center center;
    }
  }
}
.homeBoxTwo {
  width: 100%;
  overflow: hidden;
  background: #fff;
  .homeTable {
    width: 100%;
    overflow: hidden;
    padding: 10px 20px;
    box-sizing: border-box;
  }
}
.addshowCheckTypeOne {
  border: none !important;
  padding: 0 20px !important;
  position: unset !important;
  margin-bottom: 40px;
}
.showCheckTypeOne {
  max-height: 400px;
  overflow-y: auto;
  width: 500px;
  position: absolute;
  background: #fff;
  border: solid 1px #ccc;
  z-index: 10;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 5px;
  margin-top: 10px;
  .second-check {
    display: inline-block;
    padding: 2px 10px;
    border: 1px solid #f7f7f7;
    margin-bottom: 30px;
    line-height: 24px;
    cursor: pointer;
    &.active {
      color: #3d9ffe;
      border: 1px solid #3d9ffe;
    }
  }
  ul.showTypeOne {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    li {
      list-style: none;
      margin-right: 10px;
      padding: 0 6px;
      border-radius: 3px;
      height: 28px;
      line-height: 28px;
      border: solid 1px #ccc;
      cursor: pointer;
      margin-bottom: 10px;
    }
    li.active {
      border-color: #3d9ffe;
      color: #3d9ffe;
    }
  }
}
.customThem {
  position: absolute;
  left: 7px;
  bottom: 45%;
  width: 80px;
  background: #fff;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  padding: 8px 0px;
  box-sizing: border-box;
  p {
    display: block;
    line-height: 26px;
    margin: 0;
    padding: 0;
  }
}
.customThem:hover {
  background: #3d9ffe;
  p {
    color: #fff;
  }
}
</style>

