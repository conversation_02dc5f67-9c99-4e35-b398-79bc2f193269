import Vue from 'vue'

import Cookies from 'js-cookie'

import Element from 'element-ui'
import './assets/styles/element-variables.scss'

import '@/assets/styles/index.scss' // global css
import '@/assets/styles/boryou.scss' // boryou css
import '@/assets/styles/weight.scss' // weight css
import App from './App'
import store from './store'
import router from './router'
import permission from './directive/permission'
import VueUeditorWrap from 'vue-ueditor-wrap';

import './assets/icons' // icon
import './permission' // permission control
import { getDicts } from "@/api/system/dict/data";
import { getConfigKey } from "@/api/system/config";
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, download, handleTree,selectDictName,selectDictId } from "@/utils/boryou";
import Pagination from "@/components/Pagination";
import SelectAdd from "@/components/SelectAdd";
import VueTouch from 'vue-touch'
// 自定义表格工具扩展
import RightToolbar from "@/components/RightToolbar"
import * as echarts from 'echarts'
import moment from 'moment'
import md5 from 'js-md5'
import Contextmenu from "vue-contextmenujs"
import Clipboard from 'clipboard'

Vue.prototype.Clipboard = Clipboard
Vue.prototype.$md5 = md5
// moment().format();
Vue.prototype.$echarts = echarts;
//全局修改默认配置，点击空白处不能关闭弹窗
Element.Dialog.props.closeOnClickModal.default = false

// 全局方法挂载
Vue.prototype.$getBase64Image = img => {
    const canvas = document.createElement('canvas')
    canvas.width = img.width
    canvas.height = img.height
    const ctx = canvas.getContext('2d')
    ctx.drawImage(img, 0, 0, img.width, img.height)
    let ext = img.src.substring(img.src.lastIndexOf('.') + 1).toLowerCase()
    if (ext === 'jpg') {
        ext = 'jpeg' //这个地方是由于如果是jpg, 他会识别成image/png
    }
    const dataURL = canvas.toDataURL('image/' + ext)
    return dataURL
}
Vue.prototype.getDicts = getDicts
Vue.prototype.getConfigKey = getConfigKey
Vue.prototype.parseTime = parseTime
Vue.prototype.resetForm = resetForm
Vue.prototype.selectDictName = selectDictName
Vue.prototype.selectDictId = selectDictId
Vue.prototype.addDateRange = addDateRange
Vue.prototype.selectDictLabel = selectDictLabel
Vue.prototype.selectDictLabels = selectDictLabels
Vue.prototype.download = download
Vue.prototype.handleTree = handleTree
Vue.prototype.moment = moment

Vue.prototype.msgSuccess = function (msg) {
    this.$message({ showClose: true, message: msg, type: "success" });
}

Vue.prototype.msgError = function (msg) {
    this.$message({ showClose: true, message: msg, type: "error" });
}

Vue.prototype.msgInfo = function (msg) {
    this.$message.info(msg);
}

// 全局组件挂载
Vue.component('Pagination', Pagination)
Vue.component('RightToolbar', RightToolbar)
Vue.component('SelectAdd', SelectAdd)
Vue.use(permission)
Vue.component('vue-ueditor-wrap', VueUeditorWrap);
Vue.use(Contextmenu);

//注册vue-touch的双击事件
VueTouch.registerCustomEvent('doubletap', {
    type: 'tap',
    taps: 2
  })
Vue.use(VueTouch, {name: 'v-touch'})

/**
 * If you don't want to use mock-server
 * you want to use MockJs for mock api
 * you can execute: mockXHR()
 *
 * Currently MockJs will be used in the production environment,
 * please remove it before going online! ! !
 */

Vue.use(Element, {
    size: Cookies.get('size') || 'medium' // set element-ui default size
})

Vue.config.productionTip = false
new Vue({
    el: '#app',
    router,
    store,
    render: h => h(App)
})