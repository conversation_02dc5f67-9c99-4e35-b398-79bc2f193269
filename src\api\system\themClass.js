import request from '@/utils/request'

// 搜索
export function handleQueryApi (query) {
    return request({
        url: '/task/checkTaskType/list',
        method: 'get',
        params: query
    })
}
// 新增
export function sureAddApi (data) {
    return request({
        url: '/task/checkTaskType',
        method: 'post',
        data: data
    })
}
// 修改
export function editAddApi (data) {
    return request({
        url: '/task/checkTaskType',
        method: 'put',
        data: data
    })
}
// 删除

export function delListApi (ids) {
    return request({
        url: '/task/checkTaskType/' + ids,
        method: 'delete',
    })
}
// 查看
export function seeListApi (id) {
    return request({
        url: '/task/checkTaskType/' + id,
        method: 'get',
    })
}
