import request from '@/utils/request'

// 登录方法
export function login (username, password, code, uuid, rejectNum, wechatQRCodeId,context) {
  const data = {
    username,
    password,
    code,
    uuid,
    rejectNum,
    wechatQRCodeId
  }
  return request({
    url: `/login/${context}`,
    method: 'post',
    data: data
  })
}
// 获取外部链接
export function singleLogin (username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid,
  }
  return request({
    url: '/singleLogin',
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo () {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout () {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg () {
  return request({
    url: '/captchaImage',
    method: 'get'
  })
}
// 获取登录用户的图片
export function getSystemLogoApi (query) {
  return request({
    url: '/project/manage/select/' + query,
    method: 'get',
  })
}
// 获取微信二维码
export function getWechatlogoApi (query) {
  return request({
    url: '/system/wechatQRCode/getWechatQRCode',
    method: 'get',
    params: query
  })
}
// 获取二维码状态
export function getCodeStatusApi (query) {
  return request({
    url: '/system/wechatQRCode/checkQRCode',
    method: 'get',
    params: query
  })
}
// 判断二维码状态确定登录
export function checkLoginApi (data) {
  return request({
    url: '/wechatLogin',
    method: 'post',
    data: data
  })
}
// 个人中心绑定微信
export function checkpersonLoginApi (data) {
  return request({
    url: '/system/user/profile/bindWechat',
    method: 'post',
    data: data
  })
}



