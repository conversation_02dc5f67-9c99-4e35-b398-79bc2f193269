<template>
    <div class="home">
      <div class="homeBox">
        <el-form :model="queryParams" label-width="120px" :inline="true">
          <el-form-item
            label="文稿审校接口:"
            prop="usedIfly"
          >
            <el-select
              v-model="queryParams.usedIfly"
              clearable
              size="small"
            >
              <el-option
                v-for="(item, index) in userList"
                :key="index"
                :label="item.name"
                :value="parseInt(item.value)"
                ></el-option
              >
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['system:sitePlan:add']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              >批量删除</el-button
            >
          </el-col>
        </el-row>
  
        <el-table :data="textList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            type="index"
            label="序号"
            :index="getIndex"
            width="80"
          ></el-table-column>
          <el-table-column label="每日查询字数" align="center" prop="limitedWordsNum" />
          <el-table-column
            label="排序"
            align="center"
            prop="memberLevel"
          />
          <el-table-column label="文稿审校接口" align="center" prop="createTime" >
            <template slot-scope="scope">
                {{ scope.row.usedIfly==true?'讯飞接口&系统接口':'系统接口' }}
            </template>
            </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
                >{{ scope.row.typeName == "未分组" ? "查看" : "修改" }}</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="handleDelete(scope.row)"
                v-show="scope.row.typeName !== '未分组'"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
  
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="pageNum"
          :limit.sync="pageSize"
          @pagination="handleQuery"
        />
      </div>
      <!-- 新增修改弹框 -->
      <el-dialog :title="title" width="600px" :visible.sync="dialogFormVisible" @close="closeDialog">
        <el-form :model="form" label-width="160px" :rules="rules" ref="formRef">
          <el-form-item label="文稿审校接口：" prop="usedIfly">
            <el-select
              v-model="form.usedIfly"
              clearable
              size="small"
            >
              <el-option
                v-for="(item, index) in userList"
                :key="index"
                :label="item.name"
                :value="parseInt(item.value)"
                ></el-option
              >
            </el-select>
          </el-form-item>
          <el-form-item
            label="每日查询字数:"
            prop="limitedWordsNum"
          >
          <el-input  v-model.number="form.limitedWordsNum" style="width:55%" ></el-input>
          </el-form-item>
          <el-form-item
            label="排序:"
            prop="memberLevel"
          >
          <el-input  v-model.number="form.memberLevel" style="width:55%"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center">
          <el-button type="primary" @click="addSitePlan">确 定</el-button>
          <el-button @click="closeDialog">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </template>
  
  <script>
  import {
    handleQueryApi,
    addSetApi,
    handleDeleteApi,
    editSetApi,
    handleUpdateApi,
  } from "@/api/system/textreviewSet";
export default {
    data() {
        var numbervalid = (rule, value, callback) => {
                if( Number(value) > 1000000 ||  Number(value) < 0 ){
                    callback(new Error('请输入0-1000000的数值'))
                } else {
                  callback()
                 }   
            };
      return {
        userList: [
        { name: "讯飞接口&系统接口", value: 1 },
        { name: "系统接口", value: 0 },
        ],
        title: "新增文稿审校配置",
        queryParams: {
            usedIfly: "",
        },
        form: {
            usedIfly: "",
            limitedWordsNum: null,
            memberLevel: null,
        },
        textList: [],
        pageNum: 1,
        pageSize: 10,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        total: 0,
        dialogFormVisible: false,
        rules: {
            usedIfly: [
            { required: true, message: "文稿审校接口不能为空", trigger: "change" },
          ],
          limitedWordsNum: [
            { required: true, message: "请输入每日查询字数", trigger: "blur",type:"number" },
            { validator: numbervalid, trigger: 'blur' }
          ],
          memberLevel: [
            { required: true, message: "请输入排序", trigger: "blur",type:"number"},
            { validator: numbervalid, trigger: 'blur' }
          ],
        },
      };
    },
    created() {
      this.handleQuery();
    },
    methods: {
      getIndex(index) {
        return (this.pageNum - 1) * this.pageSize + index + 1;
      },
      //   关闭新增修改弹窗
      closeDialog() {
        this.dialogFormVisible = false;
        this.$refs.formRef.resetFields();
      },
      //   获取列表数据
      async handleQuery() {
        let params = {
            usedIfly: this.queryParams.usedIfly,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        };
        let res = await handleQueryApi(params);
        this.textList = res.rows;
        this.total = res.total;
      },
      //   重置
      resetQuery() {
        this.queryParams.usedIfly = "";
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map((item) => item.id);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      },
      // 删除
      async handleDelete(row) {
        // 查询分组下是否有站点
        const ids = row.id || this.ids;
        this.$confirm(
               "是否确认删除该数据项?" ,
          "警告",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(function () {
            return handleDeleteApi(ids);
          })
          .then(() => {
            this.handleQuery();
            this.msgSuccess("删除成功");
          });
      },
      // 修改
        async handleUpdate (row) {
        this.title="修改文稿审校配置"
        let res = await handleUpdateApi(row.id);
        if (res.code == 200) {
          this.dialogFormVisible = true;
            this.form = res.data;
            this.form.usedIfly=res.data.usedIfly==true?1:0
        }
      },
      // 打开新增弹框
      handleAdd() {
        this.dialogFormVisible = true;
        this.title = "新增文稿审校配置";
        this.form.usedIfly = "";
        this.form.limitedWordsNum = "";
        this.form.memberLevel = "";
      },
      // 新增修改确定
      addSitePlan() {
        this.$refs.formRef.validate(async (valid) => {
          if (valid) {
            if (this.form.id) {
              if (this.title == "新增文稿审校配置") {
                this.dialogFormVisible = false;
                this.$refs.formRef.clearValidate();
              } else if (this.title == "修改文稿审校配置") {
                //   修改
                let params = {
                    usedIfly: this.form.usedIfly,
                    limitedWordsNum: this.form.limitedWordsNum,
                    memberLevel:this.form.memberLevel,
                    id: this.form.id,
                };
                let res = await editSetApi(params);
                if (res.code == 200) {
                  this.handleQuery();
                  this.dialogFormVisible = false;
                  this.msgSuccess("修改成功");
                  this.$refs.formRef.resetFields();
                  for (let key in this.form) {
                    this.$set(this.form, key, "");
                  }
                } else {
                  this.msgError(res.msg);
                }
              }
            } else {
              //   新增
              let params = {
                    usedIfly: this.form.usedIfly,
                    limitedWordsNum: this.form.limitedWordsNum,
                    memberLevel:this.form.memberLevel,
              };
              let res = await addSetApi(params);
              if (res.code == 200) {
                this.msgSuccess("新增成功");
                this.handleQuery();
                this.dialogFormVisible = false;
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
                this.$refs.formRef.clearValidate();
              } else {
                this.msgError(res.msg);
              }
            }
          }
        });
      },
    },
  };
  </script>
  <style scoped lang="scss">
  .home {
    width: 100%;
    overflow: hidden;
    padding: 20px 5%;
    box-sizing: border-box;
    background: #f4f7fb;
    min-height: calc(100vh - 60px);
    .homeBox {
      width: 100%;
      overflow: hidden;
      background: #fff;
      padding: 20px;
      box-sizing: border-box;
    }
  }
  </style>