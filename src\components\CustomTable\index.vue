<template>
  <div>
    <el-dialog
    :visible.sync="dialogVisible"
      title="自定义导出字段"
      width="800"
      :before-close="handleClose"
    >
    <!-- <div class="fixed-field">
      锁定前 <el-input-number :max="5" :min="0" style="width: 120px;" v-model="fixedField"></el-input-number> 列
    </div> -->
      <div class="field-title">
        已选择字段（可以拖动排序）(已选择<span>{{ list.length }}</span>/{{ list.length + unselectList.length }}字段)
      </div>
      <draggable class="list-group" :list="list">
        <div v-for="(element,index) in list" :key="element.name" :class="element.disable?'list-group-item':'dymatic-item'"
        >
          {{ element.name }}
          <!-- <el-icon v-if="!element.disable" class="list-icon" @click="delItem(element,index)"><Close /></el-icon> -->
          <i v-if="!element.disable" class="list-icon el-icon-close" @click="delItem(element,index)"></i>
        </div>
      </draggable>
      <div class="field-title">
        未选字段
      </div>
      <div class="unselected-group">
        <div v-for="(item,index) in unselectList" :key="index" class="unselected-list">
          {{ item.name }}
          <div class="select-mask">
            <div class="select-content" @click="addList(item,index)">
              + 添加
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="subLoading" @click="submitField">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import draggable from "vuedraggable"
export default {
  components: {
     draggable
  },
  props: {
    // fixed: {
    //   type: Number,
    //   default:0
    // },
    list: {
      type: Array,
      default: () => []
    },
    unselectList: {
      type: Array,
      default: () => []
    },
    visible: {
      type: Boolean,
      default: true
    },
    loading:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  computed: {
    dialogVisible:{
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    currentList:{
      get() {
        return this.list
      },
      set(val) {
        this.$emit('update:list', val)
      }
    },
    currentUnselect:{
      get() {
        return this.unselectList
      },
      set(val) {
        this.$emit('update:unselectList', val)
      }
    },
    // fixedField: {
    //   get() {
    //     return this.fixed
    //   },
    //   set(val) {
    //     this.$emit('update:fixed', val)
    //   }
    // },
    subLoading: {
      get() {
        return this.loading
      },
      set(val) {
        this.$emit('update:loading', val)
      }
    },
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
    },
    // 提交设置
    submitField() {
      this.$emit('submits')
      this.subLoading = true
    },
    // 去除已选字段
    delItem(item, index) {
      this.currentList.splice(index, 1)
      this.currentUnselect.push(item)
    },
    // 新增字段
    addList(item, index) {
      this.currentUnselect.splice(index, 1)
      this.currentList.push(item)
    }
  }
}
</script>
<style lang="scss" scoped>
.fixed-field{
  margin-bottom: 15px;
}
.field-title{
  width: calc(100% - 20px);
  display: flex;
  align-items: center;
  background: #f2f2f2;
  padding:10px;
  span{
    color: #409eff;
  }
}
.list-group{
  display: flex;
  flex-wrap: wrap;
  margin: 20px 0;
  .list-group-item{
    margin-bottom: 5px;
    padding: 5px 10px;
    margin-right: 10px;
    border: 1px solid #aaa;
    cursor: pointer;
    color: #333;
    background: #d7d7d7;
  }
  .list-icon{
    display: block;
    font-size: 16px;
    margin-left: 5px;
  }
  .dymatic-item{
    margin-bottom: 5px;
    padding: 5px 10px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    border: 1px solid #409eff;
    color: #409eff;
    cursor: pointer;
  }
}
.unselected-group{
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
  .select-mask{
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: #fff;
    text-align: center;
    background: rgba($color: #000000, $alpha: 0.3);
    .select-content{
      font-size: 14px;
      z-index: 100;
      margin-top: 4px;
    }
  }
  .unselected-list{
    position: relative;
    padding: 5px 10px;
    margin-bottom: 5px;
    margin-right: 10px;
    border: 1px solid #d7d7d7;
    color: #aaa;
    cursor: pointer;
    &:hover{
      .select-mask{
        display: block;
      }
    }
  }
  
}
</style>
