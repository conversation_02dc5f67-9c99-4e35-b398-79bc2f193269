<template>
  <div v-loading="loadingTwo">
    <div v-if="OverviewFlag" class="totalBox">
      <div class="boxOne">
        <p>
          {{ this.queryForm.type == 0 ? "检测网站数" : "检测媒体账号数" }}
        </p>
        <span>
          <div>{{ totalDataList.searchsiteCount || 0 }}</div>
          <em>个</em>
        </span>
      </div>
      <div class="boxTwo">
        <p>检测内容数</p>
        <span>
          <div>{{ totalDataList.searchInfoCount || 0 }}</div>
          <em>个</em>
        </span>
      </div>
      <div class="boxThree">
        <p>全部疑似错误数</p>
        <span>
          <div>{{ totalDataError }}</div>
          <em>个</em>
        </span>
      </div>
      <div class="boxFour">
        <p>严重错误数</p>
        <span>
          <div>{{ totalDataList.severeInfoCount || 0 }}</div>
          <em>个</em>
        </span>
        <img
          v-if="totalDataList.severeInfoCount > 0"
          class="seeLink"
          @click="goSeeDetail(3)"
          src="@/assets/images/seeLink.png"
          alt=""
        />
      </div>
      <div class="boxFive">
        <p>一般错误数</p>
        <span>
          <div>{{ totalDataList.generalInfoCount || 0 }}</div>
          <em>个</em>
        </span>
        <img
          v-if="totalDataList.generalInfoCount > 0"
          class="seeLink"
          @click="goSeeDetail(2)"
          src="@/assets/images/seeLink.png"
          alt=""
        />
      </div>
      <div class="boxSix">
        <p>自定义错误数</p>
        <span>
          <div>{{ totalDataList.weakInfoCount || 0 }}</div>
          <em>个</em>
        </span>
        <img
          v-if="totalDataList.weakInfoCount > 0"
          class="seeLink"
          @click="goSeeDetail(1)"
          src="@/assets/images/seeLink.png"
          alt=""
        />
      </div>
    </div>

    <div v-else class="totalBox">
      <div class="boxOne">
        <p>检测网站数</p>
        <span>
          <div>{{ totalDataList.siteCount || 0 }}</div>
          <em>个</em>
        </span>
      </div>
      <div class="boxTwo">
        <p>检测内容数</p>
        <span>
          <div>{{ totalDataList.solrCount || 0 }}</div>
          <em>个</em>
        </span>
      </div>
      <div class="boxThree">
        <p>{{ this.switchErrorTitle() }}</p>
        <span>
          <div>{{ totalDataList.errorTotalCount || 0 }}</div>
          <em>个</em>
        </span>
        <img
          v-if="totalDataList.errorTotalCount > 0"
          class="seeLink"
          @click="goSeeMediaDetail([])"
          src="@/assets/images/seeLink.png"
          alt=""
        />
      </div>
      <div class="boxFive">
        <p>嫌疑数</p>
        <span>
          <div>{{ totalDataList.suspectTotalCount || 0 }}</div>
          <em>个</em>
        </span>
        <img
          v-if="totalDataList.suspectTotalCount > 0"
          class="seeLink"
          @click="goSeeMediaDetail([1])"
          src="@/assets/images/seeLink.png"
          alt=""
        />
      </div>
      <div class="boxFour">
        <p>不通过数</p>
        <span>
          <div>{{ totalDataList.notPassTotalCount || 0 }}</div>
          <em>个</em>
        </span>
        <img
          v-if="totalDataList.notPassTotalCount > 0"
          class="seeLink"
          @click="goSeeMediaDetail([2])"
          src="@/assets/images/seeLink.png"
          alt=""
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // totalDataList: [],
      // totalDataError: 0,
      // loadingTwo: false,
      // checkwebName: ''
    };
  },
  props: {
    totalDataList: {
      type: Object,
      default: function () {
        return {};
      },
    },
    totalDataError: {
      type: Number,
      default: 0,
    },
    checkwebName: {
      type: String,
      default: "",
    },
    loadingTwo: {
      type: Boolean,
      default: false,
    },
    queryForm: {
      type: Object,
      default: function () {
        return {};
      },
    },
    questionTab: {
      type: String,
      default: "",
    },
    OverviewFlag: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    goSeeDetail(val) {
      const newPage = this.$router.resolve({
        path: "/conentReview/indexList/index",
        query: {
          wrongType: val,
          questionTab: this.questionTab,
          infoType: this.queryForm.infoType.join(","),
        },
      });
      window.open(newPage.href, "_blank");
    },
    goSeeMediaDetail(val) {
      let query = {
        dataType: this.queryForm.infoType.join(","), //当前
      };
      if (val.length>0) {
        query.suggestion = JSON.stringify(val);
      }
      const newPage = this.$router.resolve({
        path: "/conentReview/indexDetail2/index",
        query,
      });
      window.open(newPage.href, "_blank");
    },
    switchErrorTitle() {
      // switch (this.queryForm.infoType.join()) {
      switch (JSON.parse(sessionStorage.getItem("queryForm")).infoType.join()) {
        case "3":
          return "敏感音频数";
        case "4":
          return "敏感图片数";
        case "5":
          return "敏感视频数";
        default:
          return "敏感视频数";
      }
    },
  },
};
</script>

<style scoped lang="scss">
.totalBox {
  width: 100%;
  padding: 0 25px;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: space-around;

  > div {
    width: 15%;
    aspect-ratio: 244/148;
    // margin-right: 0.6%;
    color: #fff;
    box-sizing: border-box;
    // text-align: center;
    cursor: pointer;
    position: relative;
    padding: 2.2vw 0 0 1.5vw;

    // &:last-child {
    //   margin-right: 0%;
    // }

    p {
      font-size: 0.85vw;
      margin: 0;
      padding: 0;
    }

    span {
      display: block;
      div,
      em {
        display: inline-block;
        vertical-align: middle;
      }
      div {
        font-size: 1.5vw;
        margin-right: 0.3vw;
      }
      em {
        font-style: normal;
        font-size: 0.85vw;
      }
    }

    .seeLink {
      display: block;
      margin-top: 1vw;
      color: #1178ff;
    }
  }

  div.boxOne {
    background: url("../../../assets/images/boxOne.png") no-repeat center center;
    background-size: 100%;
  }

  div.boxTwo {
    background: url("../../../assets/images/boxTwo.png") no-repeat center center;
    background-size: 100%;
  }

  div.boxThree {
    background: url("../../../assets/images/boxThree.png") no-repeat center
      center;
    background-size: 100%;
  }

  div.boxFour {
    background: url("../../../assets/images/boxFour.png") no-repeat center
      center;
    background-size: 100%;
  }

  div.boxFive {
    background: url("../../../assets/images/boxFive.png") no-repeat center
      center;
    background-size: 100%;
  }

  div.boxSix {
    background: url("../../../assets/images/boxSix.png") no-repeat center center;
    background-size: 100%;
  }
}
</style>