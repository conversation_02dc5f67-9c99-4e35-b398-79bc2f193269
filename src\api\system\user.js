import request from '@/utils/request'
import { praseStrEmpty } from "@/utils/boryou";

// 查询用户列表
export function listUser (query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query
  })
}
export function listUserNew (query) {
  return request({
    url: '/system/user/listBelong',
    method: 'get',
    params: query
  })
}

// 查询用户详细
export function getUser (userId) {
  return request({
    url: '/system/user/' + praseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser (data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser (data) {
  return request({
    url: '/system/user',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser (userId) {
  return request({
    url: '/system/user/' + userId,
    method: 'delete'
  })
}

// 导出用户
export function exportUser (query) {
  return request({
    url: '/system/user/export',
    method: 'get',
    params: query
  })
}

// 用户密码重置
export function resetUserPwd (userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus (userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile () {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile (data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd (oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar (data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}

// 下载用户导入模板
export function importTemplate () {
  return request({
    url: '/system/user/importTemplate',
    method: 'get'
  })
}
// 个性化设置
export function closePersonDialogApi (data) {
  return request({
    url: '/project/manage',
    method: 'post',
    data: data
  })
}
// 根据用户获取个性化配置
export function getPersonSystemaApi (query) {
  return request({
    url: '/project/manage/selectByUser',
    method: 'get',
    params: query
  })
}
// 修改个性化配置
export function editPersonDialogApi (data) {
  return request({
    url: '/project/manage',
    method: 'put',
    data: data
  })
}
// 解绑微信用户
export function cancelWechatUserApi () {
  return request({
    url: '/system/wechatQRCode',
    method: 'delete',
  })
}
// 每天字数接口
export function getdayNumApi () {
  return request({
    url: '/system/user/getLimitedWordsNum',
    method: 'get',
  })
}
// 获取表格设置数据
export function getTabeSetApi (data) {
  return request({
    url: '/personalSet/get',
    method: 'post',
    data: data
  })
}
// 表格设置编辑
export function sureSetApi (data) {
  return request({
    url: '/personalSet/add',
    method: 'post',
    data: data
  })
}
// 获取所属saas系统
export function getSaasSysApi (data) {
  return request({
    url: '/sass/selectProjectList',
    method: 'post',
    data: data
  })
}

//
export function getallRefreshStatus () {
  return request({
    url: '/sysUser/allRefreshStatus',
    method: 'get',
  })
}
