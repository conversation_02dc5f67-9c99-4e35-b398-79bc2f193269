<template>
    <div class="app-container home">
      <div class="homeBox">
        <el-form ref="form" class="queryForm" :model="queryForm" label-width="100px" :inline="true" v-if="isRouterAlive">
          <el-row :gutter="20" style="margin-bottom: 10px">
            <el-col :span="12">
              <el-form-item label="信源类型：">
                <el-radio-group v-model="queryForm.type" @change="changeMedType">
                  <el-radio v-for="(item, index) in medType" :key="index" :value="item.value" :label="item.value">{{
                    item.name
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="其他筛选项：">
                <!-- 一级分类 -->
                <ul class="checkTypeUl">
                  <li v-for="(item, index) in checkType" @click="clickLiTwo(index)"
                    :class="curLiTwo == index ? 'active' : ''" :key="index">
                    {{ item.name }}
                    <i v-show="curLiTwo == index" class="el-icon-arrow-down"></i>
                    <p style="margin: 0; padding: 0; text-align: center" v-if="item.checkArrLen">
                      {{ item.checkArrLen }}
                    </p>
                  </li>
                  <li>
                    <i class="el-icon-setting" @click="drawerOpen" style="font-size: 16px; vertical-align: middle"></i>
                  </li>
                </ul>
                <!-- 系统 -->
                <div class="showCheckTypeOne" v-show="showSystem" ref="treeWrapOne">
                  <el-checkbox :indeterminate="isIndeterminateSys" v-model="checkSysAll"
                    @change="handleSystemAllChange">全选</el-checkbox>
                  <el-checkbox-group v-model="queryForm.checkTaskTypeId" @change="changeSystemTypeTwo">
                    <el-checkbox v-for="(item, index) in getSysData" :label="item.id" :key="index">{{ item.name
                    }}</el-checkbox>
                  </el-checkbox-group>
                </div>
                <!-- 自定义 -->
                <div class="showCheckTypeOne" v-show="showReview" ref="treeWrapTwo">
                  <el-checkbox :indeterminate="isIndeterminateOur" v-model="checkOurAll"
                    @change="handleOurAllChange">全选</el-checkbox>
                  <el-checkbox-group v-model="queryForm.checkTaskId" @change="changeOurTypeTwo">
                    <el-checkbox v-for="(item, index) in ourData" :label="item.id" :key="index">{{ item.name
                    }}</el-checkbox>
                  </el-checkbox-group>
                  <!-- <div style="text-align: right">
                    <el-button type="primary" @click="closeShowOur" size="small"
                      >确定</el-button
                    >
                    <el-button size="small" @click="cancelShowOur"
                      >取消</el-button
                    >
                  </div>-->
                </div>
                <!-- 备案网站||媒体账号 -->
                <div class="showCheckTypeOne" v-show="showMed" ref="treeWrapThree">
                  <!-- append-to-body -->
                  <div class="second-check" @click="secondCheck" :class="secondAct ? 'active' : ''">
                    全选
                  </div>
                  <ul class="showTypeOne">
                    <li v-for="(itema, index) in filterDataTree" ref="dateTree" @click="checkLiThrees(index, itema)" :class="
                      itema.webSiteAndMediaAccountList.length == 0
                        ? itema.tag
                          ? 'active'
                          : ''
                        : itema.single.length > 0
                          ? 'active'
                          : ''
                    " :key="itema.id">
                      <el-popover placement="bottom-start" width="400" trigger="click">
                        <!-- 三级分类 -->
                        <div class="third-kinds">
                          <div>
                            <span @click="checkAllThird(itema, index)" :class="itema.checkAll ? 'active' : ''">全选</span>
                          </div>
                          <div v-for="(
                                        itemb, indexb
                                      ) in itema.webSiteAndMediaAccountList" :key="indexb" class="third-item">
                            <span :class="itemb.tag ? 'active' : ''" @click="checkSingle(itemb.id, index, indexb)">{{
                              itemb.name
                            }}</span>
                          </div>
                        </div>
                        <span slot="reference" class="showTypeSpan" v-if="itema.webSiteAndMediaAccountList.length > 0">
                          {{ itema.name }}
                          <i class="el-icon-arrow-down"></i>
                        </span>
                      </el-popover>
                      <span v-if="itema.webSiteAndMediaAccountList.length == 0">
                        {{ itema.name }}
                      </span>
                    </li>
                  </ul>
                  <!-- <div style="text-align: right">
                    <el-button
                      type="primary"
                      @click="closeShowReview"
                      size="small"
                      >确定</el-button
                    >
                    <el-button size="small" @click="cancelShowReview"
                      >取消</el-button
                    >
                  </div>-->
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-bottom: 10px">
            <el-col :span="20">
              <el-form-item label="时间范围：">
                <timePick :timeRound.sync="queryForm.timeRound" :startTime.sync="queryForm.startTime"
                  :endTime.sync="queryForm.endTime" :curLi.sync="curLi"></timePick>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="20">
              <el-form-item label="信息类型：">
                <ul class="checkTypeUl">
                  <li v-for="item in carrierData" :key="item.value"
                      :class="queryForm.infoType.includes(item.value) ? 'active' : ''"
                          @click="changeNature(item.value)">
                        {{ item.label }}
                  </li>
                </ul>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" style="text-align: center; margin-top: 10px">
              <el-button v-for="(item, index) in btnList" :key="index" size="mini"
                :loading="item.name == '导出' ? exportLoading : false" @click="btnListClick(index)"
                :type="currtIndex == index ? 'primary' : ''">{{ item.name }}</el-button>
              <el-button size="mini" @click="btnListClick(2)" :type="currtIndex == 2 ? 'primary' : ''"
                v-show="showReportView" v-hasPermi="['index:reportView']">报告预览</el-button>
            </el-col>
            <el-col :span="24" style="text-align: left; margin: 10px 0 10px 20px" v-show="false">
              <el-button size="mini" type="primary" @click="refreshClick" :loading="freshLoading">刷新</el-button>
              <p class="pData" v-show="reashP">{{ reashP }}</p>
            </el-col>
          </el-row>
        </el-form>
        <div class="totalView">
          <h2>总体概况</h2>
          <div class="totalBox">
            <div class="boxOne" v-loading="loadingTwo">
              <span>
                {{ totalDataList.searchsiteCount }}
                <em>个</em>
              </span>
              <p>{{ checkwebName }}</p>
            </div>
            <div class="boxTwo" v-loading="loadingTwo">
              <span>
                {{ totalDataList.searchInfoCount }}
                <em>个</em>
              </span>
              <p>检测内容数</p>
            </div>
            <div class="boxThree" v-loading="loadingTwo">
              <span>
                {{ totalDataError }}
                <em>个</em>
              </span>
              <p>全部疑似错误数</p>
            </div>
            <div class="boxFour" v-loading="loadingTwo">
              <span>
                {{ totalDataList.severeInfoCount }}
                <em>个</em>
              </span>
              <p>严重错误数</p>
              <em class="seeLink" @click="goSeeDetail(3)" v-if="totalDataList.severeInfoCount > 0">点击查看</em>
              <em class="seeLink" style="color: #ccc" v-else>点击查看</em>
            </div>
            <div class="boxFive" v-loading="loadingTwo">
              <span>
                {{ totalDataList.generalInfoCount }}
                <em>个</em>
              </span>
              <p>一般错误数</p>
              <em class="seeLink" @click="goSeeDetail(2)" v-if="totalDataList.generalInfoCount > 0">点击查看</em>
              <em class="seeLink" style="color: #ccc" v-else>点击查看</em>
            </div>
            <div class="boxSix" v-loading="loadingTwo">
              <span>
                {{ totalDataList.weakInfoCount }}
                <em>个</em>
              </span>
              <p>自定义错误数</p>
              <em class="seeLink" @click="goSeeDetail(1)" v-if="totalDataList.weakInfoCount > 0">点击查看</em>
              <em class="seeLink" style="color: #ccc" v-else>点击查看</em>
            </div>
          </div>
        </div>
      </div>
      <div class="homeBoxTwo" v-loading="loading">
        <div class="totalView">
          <el-radio-group v-model="questionTab" @change="changeques" style="margin: 30px 0px 10px 20px">
            <el-radio-button :label="11">当前问题</el-radio-button>
            <el-radio-button :label="12" v-if="checkPermi(['index:historyquestion'])">历史问题</el-radio-button>
          </el-radio-group>
          <!-- <ul class="quesUl">
            <li v-for="item in questionList" :key="item.value" @click="changeques(item)"
              :class="{ activeLi: questionTab == item.value }">
               <span v-if="item.name=='历史问题'?checkPermi(['index:historyquestion']):''"></span>
               <span v-else>{{ item.name }}</span>
            </li>
          </ul> -->
        </div>
        <div class="homeTable">
          <el-table :data="tableData" stripe style="width: 100%">
            <el-table-column type="index" label="序号" :index="getIndex" align="center" width="80"></el-table-column>
            <el-table-column v-if="questionTab == '11'" :key="1" prop="siteName" align="center" :label="tableSiteName">
              <!-- :label="queryForm.type == 0 ? '备案网站名称' : '媒体账号名称'" -->
            </el-table-column>
            <el-table-column prop="host" label="备案域名" align="center" :key="2"
              v-if="showwebcloumn && questionTab == '11'"></el-table-column>
            <el-table-column prop="infoCount" label="全部错误数" align="center" :key="3" v-if="questionTab == '11'">
            </el-table-column>
            <el-table-column prop="severeCount" label="严重错误数" align="center" :key="4"
              v-if="questionTab == '11'"></el-table-column>
            <el-table-column prop="generalCount" label="一般错误数" align="center" :key="5"
              v-if="questionTab == '11'"></el-table-column>
            <el-table-column prop="weakCount" label="自定义错误数" align="center" :key="6"
              v-if="questionTab == '11'"></el-table-column>
            <el-table-column prop="siteName" align="center" :label="tableSiteName" :key="7"
              v-if="questionTab == '12'"></el-table-column>
            <el-table-column label="备案域名" align="center" prop="host" :key="8" :show-overflow-tooltip="true"
              v-if="showwebcloumn && questionTab == '12'"></el-table-column>
            <el-table-column label="全部错误数" align="center" prop="infoCount" :key="9" v-if="questionTab == '12'"
              :show-overflow-tooltip="true"></el-table-column>
            <el-table-column label="严重错误数" align="center" prop="severeCount" :key="10" v-if="questionTab == '12'"
              :show-overflow-tooltip="true"></el-table-column>
            <el-table-column prop="generalCount" label="一般错误数" align="center" v-if="questionTab == '12'" :key="11">
            </el-table-column>
            <el-table-column label="自定义错误数" align="center" prop="weakCount" :key="12"
              v-if="questionTab == '12'"></el-table-column>
  
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="goDetail(scope.row)" v-if="questionTab == '11'">查看详情</el-button>
                <el-button type="text" @click="goDetailHistory(scope.row)" v-if="questionTab == '12'">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total > 0" style="margin: 20px 0px; float: right" :total="total" :page.sync="pageNum"
            :limit.sync="pageSize" @pagination="getListData()" />
        </div>
      </div>
      <div class="customThem" @click="customClick" v-if="checkPermi(['index:customword'])">
        <p>添加自定</p>
        <p>义词</p>
      </div>
      <!-- 自定义专题弹框 -->
      <el-dialog title="添加自定义词" width="600px" :visible.sync="dialogFormVisible">
        <div slot="title" class="header-title">
          <span class="title-age">添加自定义词</span>
          <el-tooltip class="item" effect="light" placement="top-start" popper-class="tip-class">
            <div slot="content" style="line-height: 24px; font-size: 12px">
              用户可自定义添加正确词、敏感词或者错
              <br />误词并进行应用监测；添加敏感词和错
              <br />误词时需提前维护对应的自定义专题
            </div>
            <span class="title-name">
              <i class="el-icon-question icon-report" style="color: #e6a23c; margin-left: 10px; cursor: pointer"></i>
            </span>
          </el-tooltip>
        </div>
        <el-form :model="formCustom" label-width="120px" :rules="rules" ref="formCustomRules">
          <el-form-item label-width="0" style="text-align: center">
            <el-radio-group v-model="tabPosition" @change="changeRadio">
              <el-radio-button label="1">添加正词</el-radio-button>
              <el-radio-button label="2">添加敏感词</el-radio-button>
              <el-radio-button label="3">添加错误词</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label-width="0">
            <p v-show="tabPosition == 1">
              说明：将词语添加为正词后，再出现该词语时，首页问题汇总、审校专题、
              文稿审校版块都将不会提示错误。
            </p>
            <p v-show="tabPosition == 2">
              说明：将词语添加为敏感词后，再出现该词语时，首页问题汇总、审校专题板块将会提示敏感，文稿审校版块将会提示错误但不会建议正确词语。
            </p>
            <p v-show="tabPosition == 3">
              说明：将未提示错误的词语添加入错误词库后，再出现该词语时，首页问题汇总、审校专题
              版块将会提示错误，文稿审校版块将会提示错误并给出建议正确词。
            </p>
          </el-form-item>
          <el-form-item label="正词：" :prop="tabPosition == 1 ? 'properWord' : ''" v-show="tabPosition == 1">
            <el-input type="textarea" placeholder="请输入正词,多个请用空格隔开" v-model="formCustom.properWord"></el-input>
          </el-form-item>
          <el-form-item label="敏感词：" :prop="tabPosition == 2 ? 'sensitiveWord' : ''" v-show="tabPosition == 2">
            <el-input type="textarea" placeholder="请输入敏感词,多个请用空格隔开" v-model="formCustom.sensitiveWord"></el-input>
          </el-form-item>
          <el-form-item label="错误词" :prop="tabPosition == 3 ? 'errorWord' : ''" v-show="tabPosition == 3">
            <el-input type="textarea" v-model="formCustom.errorWord" placeholder="请输入错误词,多个请用空格隔开；错误词与正确词需要一一对应" />
          </el-form-item>
          <el-form-item label="建议词" :prop="tabPosition == 3 ? 'suggestWord' : ''" v-show="tabPosition == 3">
            <el-input type="textarea" v-model="formCustom.suggestWord" placeholder="请输入建议词，多个请用空格隔开；建议词和错误词需要一一对应" />
          </el-form-item>
          <el-form-item :label="`${wordType}专题`" v-show="tabPosition == 2 || tabPosition == 3" :prop="
            (tabPosition == 2 ? 'checkTaskId' : '') ||
            (tabPosition == 3 ? 'checkTaskId' : '')
          ">
            <el-select v-model="formCustom.checkTaskId" :placeholder="`请选择${wordType}专题`" style="width: 100%"
              @mouseleave="leaveOption">
              <!-- <div class="addType" @click="addMoreType" v-if="showAdd">
                <i class="el-icon-plus"></i> 添加分类
              </div>
              <div class="addTopName" v-else>
                <el-form :model="topicForm" :inline="true">
                  <el-form-item prop="name">
                    <el-input
                      placeholder="请输入专题"
                      v-model="topicForm.name"
                      size="small"
                    ></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" size="small">保存</el-button>
                    <el-button size="small" @click="closeAddmoreType"
                      >取消</el-button
                    >
                  </el-form-item>
                </el-form>
              </div>-->
              <el-option v-for="item in tasksenDataOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: center">
          <el-button type="primary" @click="suredialogFormVisible">确 定</el-button>
          <el-button @click="canceldialogFormVisible">取 消</el-button>
        </div>
      </el-dialog>
      <el-drawer title="其他筛选设置" :visible.sync="drawer" :with-header="true">
        <!-- @closed="cancelShowReviewDialog" -->
        <!-- 系统专题 -->
        <div class="showCheckTypeOne addshowCheckTypeOne">
          <div class="titleBox">
            <h3 class="elasticTitle">系统专题</h3>
            <!-- <div
              class="second-check"
              :class="secondAct ? 'active' : ''"
              @click="secondCheckAll"
            >
              全选
            </div>-->
            <el-checkbox v-model="chooseMe" @change="chooseMeEvent">全选</el-checkbox>
          </div>
          <el-checkbox :indeterminate="isIndeterminateSys" v-model="checkSysAll"
            @change="handleSystemAllChange">全选</el-checkbox>
          <el-checkbox-group v-model="queryForm.checkTaskTypeId" @change="changeSystemTypeTwo">
            <el-checkbox v-for="(item, index) in getSysData" :label="item.id" :key="index">{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </div>
        <!-- 自定义专题 -->
        <div class="showCheckTypeOne addshowCheckTypeOne">
          <div class="titleBox">
            <h3 class="elasticTitle">自定义专题</h3>
          </div>
          <!-- 二级分类 -->
          <el-checkbox :indeterminate="isIndeterminateOur" v-model="checkOurAll"
            @change="handleOurAllChange">全选</el-checkbox>
          <el-checkbox-group v-model="queryForm.checkTaskId" @change="changeOurTypeTwo">
            <el-checkbox v-for="(item, index) in ourData" :label="item.id" :key="index">{{ item.name }}</el-checkbox>
          </el-checkbox-group>
          <!-- <div style="text-align: right">
            <el-button type="primary" @click="closeShowOur" size="small"
              >确定</el-button
            >
            <el-button size="small" @click="cancelShowOur">取消</el-button>
          </div>-->
        </div>
        <!-- 备案网站||媒体账号 -->
        <div class="showCheckTypeOne addshowCheckTypeOne" style="height: 200px; overflow: scorll">
          <div class="titleBox">
            <h3 class="elasticTitle addelasticTitle">
              {{ queryForm.type == 0 ? "备案网站" : "媒体账号" }}
            </h3>
          </div>
          <div class="second-check" @click="secondCheck" :class="secondAct ? 'active' : ''">
            全选
          </div>
          <ul class="showTypeOne">
            <li v-for="(itema, index) in filterDataTree" ref="dateTree" @click="checkLiThrees(index, itema)" :class="
              itema.webSiteAndMediaAccountList.length == 0
                ? itema.tag
                  ? 'active'
                  : ''
                : itema.single.length > 0
                  ? 'active'
                  : ''
            " :key="itema.id">
              <el-popover placement="bottom-start" width="400" trigger="hover">
                <!-- 三级分类 -->
                <div class="third-kinds">
                  <div>
                    <span @click="checkAllThird(itema, index)" :class="itema.checkAll ? 'active' : ''">全选</span>
                  </div>
                  <div v-for="(itemb, indexb) in itema.webSiteAndMediaAccountList" :key="indexb" class="third-item">
                    <span :class="itemb.tag ? 'active' : ''" @click="checkSingle(itemb.id, index, indexb)">{{ itemb.name
                    }}</span>
                  </div>
                </div>
                <span slot="reference" class="showTypeSpan" v-if="itema.webSiteAndMediaAccountList.length > 0">
                  {{ itema.name }}
                  <i class="el-icon-arrow-down"></i>
                </span>
              </el-popover>
              <span v-if="itema.webSiteAndMediaAccountList.length == 0">
                {{ itema.name }}
              </span>
            </li>
          </ul>
        </div>
        <div style="text-align: center">
          <el-button type="primary" @click="closeShowReviewTwo" size="small">确定</el-button>
          <el-button size="small" @click="cancelShowReviewTwo">取消</el-button>
        </div>
      </el-drawer>
  
      <el-card v-show="progressFlag" class="processbox">
        <span class="progressSpan">导出进度：</span>
        <el-progress :text-inside="true" :stroke-width="17" status="warning" :percentage="progressPercent">
        </el-progress>
      </el-card>
    </div>
  </template>
  
  <script>
  import {
    getReviewTwoDataApi,
    getCheckLiThreeDataApi,
    getWebDataApi,
    getMedDataApi,
    getTotalDataApi,
    getHistoryCount,
    searchDataApi,
    // 获取审校专题二级数据
    checkTaskTree,
    // 获取媒体树数据
    checkMedTree,
    siteTypeTree,
    refresh,
    getreashPApi,
    searchHistory,
    refreshApi,
    refTotal,
    refPercent,
    historyExport,
    historyExportnew
  } from "@/api/index";
  import {
    getSensitiveClassApi,
    getSensitiveApi,
    addsensitiveWord,
    addTrueWord,
  } from "@/api/system/autodict";
  import { exportAllApi } from "@/api/conentReview/themMangeChase";
  import { timeJson } from "@/utils/time.js";
  import { checkPermi } from "@/utils/permission.js";
  import { getBigTitleTwoApi } from "@/api/conentReview/themManTopic";
  import { getBigTitleThreeApi } from "@/api/conentReview/themManTopic";
  import { debounce } from "@/utils/boryou";
  import timePick from "@/views/components/timePick";
  // import exportloading from "@/components/exportloading ";
  export default {
    name: "index",
    // components: {
    //   exportloading,
    // },
    components: {
      timePick,
    },
    data () {
      return {
        debounceAgain: debounce,
        progressPercent: 0,
        progressFlag: false,
        questionList: [
          { name: "当前问题", value: 11, loading: false },
          { name: "历史问题", value: 12, loading: false },
        ],
        queVal: "",
        questionTab: "11",
        isRouterAlive: true,
        showAdd: true,
        topicForm: {
          name: "",
        },
        // 试用时间范围(1为三个月，2为六个月，3为一年)
        probationPeriod: "",
        isProbation: "",
        // 错误词分类数据
        taskTypeNameOption: [],
        // 错误词专题数据
        tasksenDataOption: [],
        ourData: [],
        tabPosition: 1,
        reashP: "",
        // 错误类型
        wrongType: "",
        drawer: false,
        loadingTwo: false,
        loading: false,
        formMedSearch: {},
        freshLoading: false,
        mediaDataTree: [],
        secondActMedia: false,
        websiteDataTree: [],
        exportLoading: false,
        secondAct: false,
        filterDataTree: [],
        checkAllTwo: false,
        isIndeterminateTwo: false,
        checkAll: false,
        checkSysAll: true,
        checkOurAll: true,
        isIndeterminate: false,
        isIndeterminateSys: false,
        isIndeterminateOur: false,
        reviewTwoData: [],
        checkTaskIds: [],
        mediaAssignIds: [],
        chooseMe: false,
        queryForm: {
          type: "0",
          // 媒体
          assignIds: [],
          // 自定义专题
          checkTaskId: [],
          startTime: "",
          endTime: "",
          // 系统专题
          checkTaskTypeId: [],
          timeRound: 1,
          //信息类型
          infoType:[1]
        },
        getSysData: [],
        // timeOne: timeJson.threeMonth,
        medType: [
          { name: "备案网站", value: "0" },
          { name: "媒体账号", value: "1" },
        ],
        btnList: [
          { name: "查询" },
          { name: "导出" },
          //  { name: "报告预览" }
        ],
        // viewDisabled:false,
        currtIndex: 0,
        tableData: [],
        timelist: [
          { name: "今天", value: "0" },
          { name: "24小时", value: "1" },
          { name: "三天", value: "2" },
          { name: "七天", value: "3" },
          { name: "三十天", value: "4" },
          { name: "自定义", value: "5" },
        ],
        errorList: [
          { name: "全选" },
          { name: "严重错误" },
          { name: "一般错误" },
          { name: "疑似错误" },
        ],
        checkType: [
          { name: "系统专题", value: "0" },
          { name: "自定义专题", value: "1" },
          { name: "备案网站", value: "2" },
        ],
        curLi: 1,
        curLiTwo: 0,
        curLiThree: 0,
        errorLi: 1,
        pageSize: 10,
        pageNum: 1,
        total: 0,
        getCheckLiThreeData: [],
        getWebData: [],
        showReview: false,
        showMed: false,
        totalDataList: [],
        totalDataError: "",
        dialogFormVisible: false,
        formCustom: {
          // 正词
          properWord: "",
          // 敏感词
          sensitiveWord: "",
          // 错误词
          errorWord: "",
          // 建议词
          suggestWord: "",
          // 错误词分类
          // taskTypeId: "",
          // 错误词专题
          checkTaskId: "",
        },
        wordType: "敏感词",
        // 表单校验
        rules: {
          properWord: [
            { required: true, message: "正词不能为空", trigger: "blur" },
            {
              max: 500,
              message: "正词长度不能超过500个字符",
              trigger: "blur",
            },
          ],
          sensitiveWord: [
            { required: true, message: "敏感词不能为空", trigger: "blur" },
            {
              max: 500,
              message: "敏感词长度不能超过500个字符",
              trigger: "blur",
            },
          ],
          errorWord: [
            { required: true, message: "错误词不能为空", trigger: "blur" },
            {
              max: 500,
              message: "错误词长度不能超过500个字符",
              trigger: "blur",
            },
          ],
          suggestWord: [
            { required: true, message: "建议词不能为空", trigger: "blur" },
          ],
          // taskTypeId: [
          //   { required: true, message: "错误词分类不能为空", trigger: "blur" },
          // ],
          checkTaskId: [
            { required: true, message: "错误词专题不能为空", trigger: "blur" },
          ],
        },
        showSystem: false,
        tableSiteName: "备案网站名称",
        checkwebName: "检测网站数",
        showwebcloumn: true,
        // 系统专题全部选中的id
        checkIdAllSys: [],
        // 自定义专题全部选中的id
        checkOurAllId: [],
        showReportView: true,
        carrierData: [
          {label:'文章', value:1},
          {label:'附件', value:2}
        ]
      };
    },
    watch: {
      checkTaskIds: {
        handler (newv, oldv) {
          this.queryForm.assignIds = newv;
        },
      },
      checkSysAll: {
        handler (newval, oldval) {
          if (newval == true) {
            this.queryForm.checkTaskTypeId = this.checkIdAllSys;
            this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
            this.isIndeterminateSys = false;
          } else {
            this.queryForm.checkTaskTypeId = [];
            this.checkType[0].checkArrLen = 0;
            this.isIndeterminateSys = false;
          }
        },
        immediate: true,
      },
      checkOurAll: {
        handler (newval, oldval) {
          if (newval == true) {
            this.queryForm.checkTaskId = this.checkOurAllId;
            this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
            this.isIndeterminateOur = false;
          } else {
            this.queryForm.checkTaskId = [];
            this.checkType[1].checkArrLen = 0;
            this.isIndeterminateOur = false;
          }
        },
        immediate: true,
      },
  
      // 详情页新开窗口，用不到了
      $route: {
        handler: function (newval, oldVal) {
          if (
            oldVal.name == "indexList" ||
            oldVal.name == "detailPage" ||
            oldVal.name == "Index"
          ) {
            this.isRouterAlive = true;
          } else {
            this.queryForm.type = "0";
            this.curLiTwo = 0;
            this.curLi = 1;
            this.queryForm.checkTaskTypeId = [];
            this.queryForm.assignIds = [];
            this.queryForm.checkTaskId = [];
            this.checkType[0].checkArrLen = 0;
            this.checkType[1].checkArrLen = 0;
            this.checkType[2].checkArrLen = 0;
          }
        },
        // 深度观察监听
        deep: true,
      },
    },
    created () {
      // this.getReviewTwoData()
      // this.getSiteTypeTree()
      setTimeout(() => {
        this.btnListClick(0);
      }, 500);
      this.getreashP();
      this.getSensitiveClass();
      this.getTopdata();
      this.getSystemData();
      this.getOurData();
      this.getTreeData();
      this.getTime();
  
    },
    mounted () {
      // 全局点击事件
      document.addEventListener("mouseup", (e) => {
        let treesys = this.$refs.treeWrapOne;
        let treeself = this.$refs.treeWrapTwo;
        let treeweb = this.$refs.treeWrapThree;
  
        if (treesys) {
          // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
          if (!treesys.contains(e.target)) {
            this.showSystem = false;
          }
        }
        if (treeself) {
          // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
          if (!treeself.contains(e.target)) {
            this.showReview = false;
          }
        }
        if (treeweb) {
          // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
          if (!treeweb.contains(e.target)) {
            this.showMed = false;
          }
        }
      });
  
      if (window.history && window.history.pushState) {
        // 向历史记录中插入了当前页
        history.pushState(null, null, document.URL);
        window.addEventListener('popstate', this.goBack, false);
      }
    },
  
    beforeRouteLeave (to, from, next) {
      if (to.path == '/404') {
        console.log(12);
        return false
      }
      next()
    },
    // mounted () {
    //     if (window.history && window.history.pushState) {
    //         // 向历史记录中插入了当前页
    //         history.pushState(null, null, document.URL);
    //         window.addEventListener('popstate', this.goBack, false);
    //     }
    // },
    destroyed () {
      window.removeEventListener('popstate', this.goBack, false);
    },
    methods: {
      goBack () {
        // console.log("点击了浏览器的返回按钮");
        history.pushState(null, null, document.URL);
      },
      checkPermi,
      // tab切换问题汇总||历史问题汇总
      async changeques () {
        if (
          (this.queryForm.timeRound == 5 && !this.queryForm.startTime) ||
          (this.queryForm.timeRound == 5 && !this.queryForm.endTime) ||
          (this.queryForm.timeRound == 5 &&
            this.queryForm.startTime > this.queryForm.endTime)
        ) {
          this.$message.error("请选择自定义时间范围");
          this.loading = false;
        } else {
          this.getTotalData();
          this.loading = true;
          if (this.questionTab == "11") {
            this.showReportView = true
            // 当前问题
            this.pageNum = 1;
            let params = {
              type: this.queryForm.type,
              startTime:
                this.queryForm.timeRound == 5 ? this.queryForm.startTime : "",
              endTime:
                this.queryForm.timeRound == 5 ? this.queryForm.endTime : "",
              checkTaskId: this.queryForm.checkTaskId.join(),
              pageIndex: this.pageNum,
              pageSize: this.pageSize,
              assignIds: this.queryForm.assignIds.join(),
              timeRound: this.queryForm.timeRound,
              // wrongType: val,
              checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
              infoType: this.queryForm.infoType,
            };
            sessionStorage.setItem("queryForm", JSON.stringify(params));
            let res = await searchDataApi(params);
            // 问题汇总数据
            this.tableData = res.rows;
            this.total = res.total;
            this.loading = false;
          } else if (this.questionTab == "12") {
            // 历史问题
            this.showReportView = true
            this.pageNum = 1;
            let hisparams = {
              type: this.queryForm.type,
              startTime:
                this.queryForm.timeRound == 5 ? this.queryForm.startTime : "",
              endTime:
                this.queryForm.timeRound == 5 ? this.queryForm.endTime : "",
              checkTaskId: this.queryForm.checkTaskId.join(),
              pageIndex: this.pageNum,
              pageSize: this.pageSize,
              assignIds: this.queryForm.assignIds.join(),
              timeRound: this.queryForm.timeRound,
              // wrongType: val,
              checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
              infoType: this.queryForm.infoType,
            };
            sessionStorage.setItem("queryForm", JSON.stringify(hisparams));
            let res = await searchHistory(hisparams);
            // 历史问题汇总数据
            this.tableData = res.rows;
            this.total = res.total;
            this.loading = false;
            // item.loading = false;
          }
        }
      },
      addMoreType () {
        this.showAdd = false;
      },
      closeAddmoreType () {
        this.topicForm.name = "";
        this.showAdd = true;
      },
      leaveOption () { },
      // 获取系统专题数据
      async getSystemData () {
        let res = await getBigTitleTwoApi();
        this.getSysData = res.data;
        for (let i = 0; i < this.getSysData.length; i++) {
          this.checkIdAllSys.push(this.getSysData[i].id);
        }
      },
      getTime () {
        this.$store.dispatch("GetInfo").then((res) => {
          let expireTime = res.user.expireTime;
          // 试用时间范围
          this.probationPeriod = res.user.probationPeriod;
          this.isProbation = res.user.isProbation;
          if (expireTime) {
            let OldTime = /\d{4}-\d{1,2}-\d{1,2}/g.exec(expireTime);
            const myDate = new Date();
            const Y = myDate.getFullYear();
            const M = myDate.getMonth() + 1;
            const D = myDate.getDate();
            const curDay = Y + "-" + M + "-" + D;
            let timeResult = this.dateDiff(OldTime[0], curDay);
            if (timeResult > 0 && timeResult < 7) {
              // this.$message.warning(`账号还有${timeResult}天过期`);
              this.$notify({
                title: "到期提醒",
                message: `账号还有${timeResult}天过期`,
                type: "warning",
                offset: 50,
              });
            }
          }
        });
      },
      //日期1减去日期2的天数.
      dateDiff (d1, d2) {
        var day = 24 * 60 * 60 * 1000;
        try {
          var dateArr = d1.split("-");
          var checkDate = new Date();
          checkDate.setFullYear(dateArr[0], dateArr[1] - 1, dateArr[2]);
          var checkTime = checkDate.getTime();
  
          var dateArr2 = d2.split("-");
          var checkDate2 = new Date();
          checkDate2.setFullYear(dateArr2[0], dateArr2[1] - 1, dateArr2[2]);
          var checkTime2 = checkDate2.getTime();
  
          var cha = (checkTime - checkTime2) / day;
          return cha;
        } catch (e) {
          return false;
        }
      },
      // 获取错误词分类数据
      async getSensitiveClass () {
        let res = await getSensitiveClassApi();
        this.taskTypeNameOption = res.rows;
      },
      // 获取敏感词和错误词专题数据
      async getTopdata () {
        let res = await getSensitiveApi();
        this.tasksenDataOption = res.rows;
      },
      changeRadio (val) {
        if (val == 2) {
          this.wordType = "敏感词";
        } else if (val == 3) {
          this.wordType = "错误词";
        }
      },
      // 获取刷新按钮后面数据
      async getreashP () {
        let res = await getreashPApi();
        this.reashP = res.msg;
      },
      drawerOpen () {
        this.drawer = true;
        this.showReview = false;
        this.showMed = false;
        this.getDiffChange();
      },
      // 刷新
      async refreshClick () {
        try {
          this.freshLoading = true;
          let res = await refresh({ id: null });
          if (res.code == 1000) {
            this.freshLoading = false;
            this.msgSuccess(res.msg);
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 获取媒体树
      getSiteTypeTree () {
        // type:1 网站；2 媒体
        siteTypeTree({ type: 1 }).then((res) => {
          this.$nextTick(() => {
            this.websiteDataTree = res.data;
            this.websiteDataTree.map((item) => {
              item.tag = false;
              item.checkAll = false;
              item.single = [];
              if (item.webSiteAndMediaAccountList.length != 0) {
                item.webSiteAndMediaAccountList.map((itemb) => {
                  itemb.tag = false;
                });
              }
            });
          });
        });
        siteTypeTree({ type: 2 }).then((res) => {
          this.$nextTick(() => {
            this.mediaDataTree = res.data;
            this.mediaDataTree.map((item) => {
              item.tag = false;
              item.checkAll = false;
              item.single = [];
              if (item.mediaAccountList.length != 0) {
                item.mediaAccountList.map((itemb) => {
                  itemb.tag = false;
                });
              }
            });
          });
        });
      },
      // 二级全选
      secondCheck () {
        this.secondAct = !this.secondAct;
        if (this.secondAct) {
          this.checkTaskIds = [];
          this.filterDataTree.map((item) => {
            item.tag = true;
            item.checkAll = true;
            if (item.webSiteAndMediaAccountList.length > 0) {
              item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = true));
              let id = item.webSiteAndMediaAccountList.map((itemb) => itemb.id);
              item.single = id;
              this.checkTaskIds.push(...id);
              this.queryForm.assignIds = this.checkTaskIds;
              this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
            }
            // else {
            //   this.checkTaskIds.push(item.id);
            //   this.queryForm.assignIds = this.checkTaskIds;
            //   this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
            // }
          });
          this.$forceUpdate();
        } else {
          this.filterDataTree.map((item) => {
            item.tag = false;
            item.checkAll = false;
            if (item.webSiteAndMediaAccountList.length > 0) {
              item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
              item.single = [];
            }
            this.checkTaskIds = [];
            this.queryForm.assignIds = this.checkTaskIds;
            this.checkType[2].checkArrLen = "";
          });
          this.$forceUpdate();
        }
      },
      //  draw全选（弃用）
      secondCheckAll (val) {
        // 系统全选
        // debugger;
        this.checkSysAll = !this.checkSysAll;
        if (this.checkSysAll) {
          this.checkSysAll = true;
          let aaa = [];
          for (let i = 0; i < this.getSysData.length; i++) {
            aaa.push(this.getSysData[i].id);
          }
          this.queryForm.checkTaskTypeId = aaa;
          this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
          this.isIndeterminateSys = false;
        } else {
          this.checkSysAll = false;
          this.queryForm.checkTaskTypeId = [];
          this.checkType[0].checkArrLen = 0;
          this.isIndeterminateSys = false;
        }
        // 自定义全选
        this.checkOurAll = !this.checkOurAll;
        if (this.checkOurAll) {
          let aaa = [];
          for (let i = 0; i < this.ourData.length; i++) {
            aaa.push(this.ourData[i].id);
          }
          this.queryForm.checkTaskId = aaa;
          this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
          this.isIndeterminateOur = false;
        } else {
          this.queryForm.checkTaskId = [];
          this.checkType[1].checkArrLen = 0;
          this.isIndeterminateOur = false;
        }
        // 网站||媒体全选
        this.secondAct = !this.secondAct;
        if (this.secondAct) {
          this.filterDataTree.map((item) => {
            item.tag = true;
            item.checkAll = true;
            if (item.webSiteAndMediaAccountList.length > 0) {
              item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = true));
              let id = item.webSiteAndMediaAccountList.map((itemb) => itemb.id);
              item.single = id;
              this.checkTaskIds.push(...id);
              this.queryForm.assignIds = this.checkTaskIds;
              this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
            } else {
              this.checkTaskIds.push(item.id);
              this.queryForm.assignIds = this.checkTaskIds;
              this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
            }
          });
          this.$forceUpdate();
        } else {
          this.filterDataTree.map((item) => {
            item.tag = false;
            item.checkAll = false;
            if (item.webSiteAndMediaAccountList.length > 0) {
              item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
              item.single = [];
            }
            this.checkTaskIds = [];
            this.queryForm.assignIds = this.checkTaskIds;
            this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          });
          this.$forceUpdate();
        }
      },
      // draw全选
      chooseMeEvent () {
        if (this.chooseMe == true) {
          // 系统全选
          this.checkSysAll = true;
          if (this.checkSysAll == true) {
            let aaa = [];
            for (let i = 0; i < this.getSysData.length; i++) {
              aaa.push(this.getSysData[i].id);
            }
            this.queryForm.checkTaskTypeId = aaa;
            this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
            this.isIndeterminateSys = false;
          }
          // 自定义全选
          this.checkOurAll = true;
          if (this.checkOurAll == true) {
            let aaa = [];
            for (let i = 0; i < this.ourData.length; i++) {
              aaa.push(this.ourData[i].id);
            }
            this.queryForm.checkTaskId = aaa;
            this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
            this.isIndeterminateOur = false;
          }
          // 网站||媒体全选
          this.secondAct = true;
          if (this.secondAct == true) {
            this.filterDataTree.map((item) => {
              item.tag = true;
              item.checkAll = true;
              if (item.webSiteAndMediaAccountList.length > 0) {
                item.webSiteAndMediaAccountList.map(
                  (itemb) => (itemb.tag = true)
                );
                let id = item.webSiteAndMediaAccountList.map((itemb) => itemb.id);
                item.single = id;
                this.checkTaskIds.push(...id);
                this.queryForm.assignIds = this.checkTaskIds;
                this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
              } else {
                this.checkTaskIds.push(item.id);
                this.queryForm.assignIds = this.checkTaskIds;
                this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
              }
            });
          }
          this.$forceUpdate();
        } else {
          // 系统全不选
          this.checkSysAll = false;
          this.queryForm.checkTaskTypeId = [];
          this.checkType[0].checkArrLen = 0;
          this.isIndeterminateSys = false;
          // 自定义全不选
          this.checkOurAll = false;
          this.queryForm.checkTaskId = [];
          this.checkType[1].checkArrLen = 0;
          this.isIndeterminateOur = false;
          // 网站||媒体全部选
          this.secondAct = false;
          this.filterDataTree.map((item) => {
            item.tag = false;
            item.checkAll = false;
            if (item.webSiteAndMediaAccountList.length > 0) {
              item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
              item.single = [];
            }
            this.checkTaskIds = [];
            this.queryForm.assignIds = this.checkTaskIds;
            this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          });
          this.$forceUpdate();
        }
      },
      // 三级单选
      checkSingle (item, index, indexb) {
        this.showMed = true;
        this.filterDataTree[index].webSiteAndMediaAccountList[indexb].tag =
          !this.filterDataTree[index].webSiteAndMediaAccountList[indexb].tag;
        if (this.filterDataTree[index].webSiteAndMediaAccountList[indexb].tag) {
          this.filterDataTree[index].single.push(item);
          this.checkTaskIds.push(item);
          this.queryForm.assignIds = this.checkTaskIds;
          this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        } else {
          if (this.checkTaskIds.indexOf(item) != -1) {
            this.checkTaskIds.splice(this.checkTaskIds.indexOf(item), 1);
            this.queryForm.assignIds = this.checkTaskIds;
            this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          }
          if (this.filterDataTree[index].single.indexOf(item) != -1) {
            this.filterDataTree[index].single.splice(
              this.filterDataTree[index].single.indexOf(item),
              1
            );
          }
        }
        if (
          this.filterDataTree[index].single.length ==
          this.filterDataTree[index].webSiteAndMediaAccountList.length
        ) {
          this.filterDataTree[index].checkAll = true;
        } else {
          this.filterDataTree[index].checkAll = false;
        }
        this.$forceUpdate();
      },
      // 全选三级
      checkAllThird (item, index) {
        this.showMed = true;
        this.filterDataTree[index].checkAll =
          !this.filterDataTree[index].checkAll;
        let id = this.filterDataTree[index].webSiteAndMediaAccountList.map(
          (item) => {
            return item.id;
          }
        );
        if (this.filterDataTree[index].checkAll) {
          // 全选
          this.checkTaskIds.push(...id);
          this.checkTaskIds = [...new Set(this.checkTaskIds)];
          this.queryForm.assignIds = this.checkTaskIds;
          this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          this.filterDataTree[index].single = id;
          this.filterDataTree[index].webSiteAndMediaAccountList.map(
            (item) => (item.tag = true)
          );
        } else {
          // 全不选
          this.filterDataTree[index].webSiteAndMediaAccountList.map(
            (item) => (item.tag = false)
          );
          this.filterDataTree[index].single = [];
          id.map((item) => {
            if (this.checkTaskIds.indexOf(item) != -1) {
              this.checkTaskIds.splice(this.checkTaskIds.indexOf(item), 1);
            }
          });
          this.queryForm.assignIds = this.checkTaskIds;
          // debugger;
          this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        }
        this.$forceUpdate();
      },
      customClick () {
        for (let key in this.formCustom) {
          this.$set(this.formCustom, key, "");
        }
        this.dialogFormVisible = true;
        this.tabPosition = 1;
      },
      // 自定义专题确定按钮
      suredialogFormVisible () {
        this.$refs["formCustomRules"].validate((valid) => {
          if (valid) {
            if (this.tabPosition == 1) {
              // 添加正词
              let params = {
                properWord: this.formCustom.properWord,
              };
              addTrueWord(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增正词成功");
                  this.dialogFormVisible = false;
                } else if (res.code == 500) {
                  this.msgError(res.msg);
                  this.dialogFormVisible = true;
                }
              });
            } else if (this.tabPosition == 2) {
              // 添加敏感词
              if (this.formCustom.sensitiveWord && this.formCustom.checkTaskId) {
                let params = {
                  problemType: 1,
                  problemWord: this.formCustom.sensitiveWord,
                  checkTaskId: this.formCustom.checkTaskId,
                };
                addsensitiveWord(params).then((res) => {
                  if (res.code == 200) {
                    this.msgSuccess("新增敏感词成功");
                    this.dialogFormVisible = false;
                  } else if (res.code == 500) {
                    this.msgError(res.msg);
                    this.dialogFormVisible = true;
                  }
                });
              } else {
                this.$message.error("请输入完整信息");
              }
            } else {
              // 添加错误词
              if (
                this.formCustom.errorWord &&
                this.formCustom.suggestWord &&
                this.formCustom.checkTaskId
              ) {
                let params = {
                  problemType: 2,
                  problemWord: this.formCustom.errorWord,
                  suggestWord: this.formCustom.suggestWord,
                  checkTaskId: this.formCustom.checkTaskId,
                };
                addsensitiveWord(params).then((res) => {
                  if (res.code == 200) {
                    this.msgSuccess("新增错误词成功");
                    this.dialogFormVisible = false;
                  } else if (res.code == 500) {
                    this.msgError(res.msg);
                    this.dialogFormVisible = true;
                  }
                });
              } else {
                this.$message.error("请输入完整信息");
              }
            }
          } else {
            this.$message.error("请输入完整信息");
          }
        });
        // this.$refs["form"].validate((valid) => {
        //   if (valid) {
        //   }
        // });
      },
      // 取消自定义专题按钮
      canceldialogFormVisible () {
        for (let key in this.formCustom) {
          this.$set(this.formCustom, key, "");
        }
        this.dialogFormVisible = false;
      },
      //   三级全选
      handleCheckAllChangeTwo (e) {
        if (e == true) {
          let aaa = [];
          for (let i = 0; i < this.getCheckLiThreeData.length; i++) {
            aaa.push(this.getCheckLiThreeData[i].id);
          }
          this.queryForm.checkTaskId = aaa;
          this.isIndeterminateTwo = false;
        } else {
          this.queryForm.checkTaskId = [];
          this.isIndeterminateTwo = false;
        }
      },
      //   媒体全选
      handleCheckAllChange (val) {
        if (val == true) {
          let aaa = [];
          for (let i = 0; i < this.getWebData.length; i++) {
            aaa.push(this.getWebData[i].id);
          }
          this.queryForm.assignIds = aaa;
          this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          this.isIndeterminate = false;
        } else {
          this.queryForm.assignIds = [];
          this.checkType[2].checkArrLen = 0;
          this.isIndeterminate = false;
        }
      },
      //   系统专题全选
      handleSystemAllChange (val) {
        if (val == true) {
          let aaa = [];
          for (let i = 0; i < this.getSysData.length; i++) {
            aaa.push(this.getSysData[i].id);
          }
          this.queryForm.checkTaskTypeId = aaa;
          this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
          this.isIndeterminateSys = false;
        } else {
          this.queryForm.checkTaskTypeId = [];
          this.checkType[0].checkArrLen = 0;
          this.isIndeterminateSys = false;
        }
      },
      //   自定义专题全选
      handleOurAllChange (val) {
        if (val == true) {
          let aaa = [];
          for (let i = 0; i < this.ourData.length; i++) {
            aaa.push(this.ourData[i].id);
          }
          this.queryForm.checkTaskId = aaa;
          this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
          this.isIndeterminateOur = false;
        } else {
          this.queryForm.checkTaskId = [];
          this.checkType[1].checkArrLen = 0;
          this.isIndeterminateOur = false;
        }
      },
      // 媒体确定选中个数
      closeShowReview () {
        this.queryForm.assignIds = this.checkTaskIds;
        this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        this.showMed = false;
      },
      closeShowMed () {
        this.showMed = false;
      },
      closeShowSystem () {
        this.showSystem = false;
        this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
      },
      closeShowOur () {
        this.showReview = false;
      },
      cancelShowReview () {
        this.showMed = false;
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
            item.single = [];
          }
          this.checkTaskIds = [];
          this.secondAct = false;
          this.checkType[2].checkArrLen = "";
        });
        this.$forceUpdate();
        this.queryForm.assignIds = [];
        this.checkAllTwo = false;
      },
      // 取消
      cancelShowReviewTwo () {
        this.checkTaskIds = [];
        // 系统专题清空
        this.checkSysAll = false;
        this.queryForm.checkTaskTypeId = [];
        this.checkType[0].checkArrLen = "";
        // 自定义专题清空
        this.checkOurAll = false;
        this.queryForm.checkTaskId = [];
        this.checkType[1].checkArrLen = "";
        // 备案网站||媒体账号清空
        this.secondAct = false;
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
            item.single = [];
          }
          this.checkTaskIds = [];
        });
        this.queryForm.assignIds = [];
        this.checkType[2].checkArrLen = "";
        this.$forceUpdate();
        this.drawer = false;
      },
      // 取消draw
      cancelShowReviewDialog () {
        this.checkTaskIds = [];
        // 系统专题清空
        this.checkSysAll = false;
        this.queryForm.checkTaskTypeId = [];
        this.checkType[0].checkArrLen = "";
        // 自定义专题清空
        this.checkOurAll = false;
        this.queryForm.checkTaskId = [];
        this.checkType[1].checkArrLen = "";
        // 备案网站||媒体账号清空
        this.secondAct = false;
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
            item.single = [];
          }
          this.checkTaskIds = [];
        });
        this.queryForm.assignIds = [];
        this.checkType[2].checkArrLen = "";
        this.$forceUpdate();
        this.drawer = false;
      },
      // 确定draw
      closeShowReviewTwo () {
        this.queryForm.assignIds = this.checkTaskIds;
        // this.checktype[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
        this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
        this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        this.drawer = false;
      },
      cancelShowMed () {
        this.checkAll = false;
        this.isIndeterminate = false;
        this.queryForm.assignIds = [];
        this.checkType[2].checkArrLen = 0;
        this.showMed = false;
      },
      // 取消系统专题选中项目
      cancelShowSystem () {
        this.checkSysAll = false;
        this.isIndeterminateSys = false;
        this.queryForm.checkTaskTypeId = [];
        this.checkType[0].checkArrLen = "";
        this.showSystem = false;
      },
      // 取消自定义专题选中项目
      cancelShowOur () {
        this.checkOurAll = false;
        this.isIndeterminateOur = false;
        this.queryForm.checkTaskId = [];
        this.checkType[1].checkArrLen = 0;
        this.showReview = false;
      },
      changeMedType (val) {
        // this.checkType[0].checkArrLen = "";
        this.checkTaskIds = [];
        this.queryForm.assignIds = [];
        this.checkType[2].checkArrLen = "";
        if (val == 0) {
          this.checkType[2].name = "备案网站";
        } else {
          this.$nextTick((_) => {
            this.checkType[2].name = "媒体账号";
          });
        }
        this.secondAct = false;
        this.getDiffChange();
        this.getTreeData();
      },
      //   点击打开审校和媒体弹框
      changeCheckTypeOne (value) {
        let checkedCount = value.length;
        this.checkAllTwo = checkedCount === this.getCheckLiThreeData.length;
        this.isIndeterminateTwo =
          checkedCount > 0 && checkedCount < this.getCheckLiThreeData.length;
        this.queryForm.checkTaskId = value;
      },
      // 自定义反选
      changeCheckTypeTwo (value) {
        let checkedCount = value.length;
        this.checkAll = checkedCount === this.getWebData.length;
        this.isIndeterminate =
          checkedCount > 0 && checkedCount < this.getWebData.length;
        this.queryForm.assignIds = value;
        this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
      },
      // 系统反选
      changeSystemTypeTwo (value) {
        let checkedCount = value.length;
        // this.checkAll = checkedCount === this.getSysData.length;
        this.isIndeterminateSys =
          checkedCount > 0 && checkedCount < this.getSysData.length;
        this.queryForm.checkTaskTypeId = value;
        this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
      },
      // 自定义专题反选
      changeOurTypeTwo (value) {
        let checkedCount = value.length;
        // this.checkOurAll = checkedCount === this.ourData.length;
        this.isIndeterminateOur =
          checkedCount > 0 && checkedCount < this.ourData.length;
        this.queryForm.checkTaskId = value;
        this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
      },
      // 获取审校专题二级数据
      async getTreeData () {
        let params = {
          type: this.queryForm.type,
          isCollect: 1
        };
        let resTree = await checkMedTree(params);
        this.filterDataTree = [];
        this.$nextTick(() => {
          this.filterDataTree = resTree.data;
          this.filterDataTree.map((item) => {
            item.tag = false;
            item.checkAll = false;
            item.single = [];
            if (
              item.webSiteAndMediaAccountList &&
              item.webSiteAndMediaAccountList.length != 0
            ) {
              item.webSiteAndMediaAccountList.map((itemb) => {
                itemb.tag = false;
              });
            }
          });
        });
      },
      // 获取审校专题二级数据
      async getReviewTwoData () {
        let res = await getReviewTwoDataApi();
        this.reviewTwoData = res.rows;
        this.reviewTwoData.map((item) => {
          item.tag = false;
        });
        this.checkLiThree(0, this.reviewTwoData[0].id);
        //   this.checkLiThree()
      },
      //   点击切换按钮颜色
      async btnListClick (index) {
        this.currtIndex = index;
        // this.wrongType = val;
        if (index == 0) {
          //   查询
          this.loading = true;
          if (
            (this.queryForm.timeRound == 5 && !this.queryForm.startTime) ||
            (this.queryForm.timeRound == 5 && !this.queryForm.endTime) ||
            (this.queryForm.timeRound == 5 &&
              this.queryForm.startTime > this.queryForm.endTime)
          ) {
            this.$message.error("请选择正确自定义时间范围");
            this.loading = false;
          } else {
            this.getTotalData();
            this.showReview = false;
            this.showMed = false;
            this.getListData();
          }
        } else if (index == 1) {
          if (
            (this.queryForm.timeRound == 5 && !this.queryForm.startTime) ||
            (this.queryForm.timeRound == 5 && !this.queryForm.endTime) ||
            (this.queryForm.timeRound == 5 &&
              this.queryForm.startTime > this.queryForm.endTime)
          ) {
            this.$message.error("请选择自定义时间范围");
          } else {
            this.exportLoading = true;
            //   导出
            let params = {
              type: this.queryForm.type,
              startTime: this.queryForm.startTime,
              endTime: this.queryForm.endTime,
              checkTaskId: this.queryForm.checkTaskId.join(),
              assignIds: this.queryForm.assignIds.join(),
              timeRound: this.queryForm.timeRound,
              checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
              infoType: this.queryForm.infoType,
            };
            let resTotal
            if (this.questionTab == '11') {
              resTotal = await refTotal(params);
            } else {
              resTotal = await historyExport(params);
            }
            // 条数<5000可以导出
            if (resTotal.code == 200) {
              let restotalData = resTotal.data;
              // 获取数据是否刷新接口（刷新完和未刷新完，可能数据不一样）
              let resRefsh = await refreshApi();
              if (resRefsh.code == 200 || resRefsh.code == 506) {
                // 刷新完成
                this.$confirm(
                  `${resRefsh.code == 200
                    ? "您确定要导出" + restotalData + "数据吗?"
                    : resRefsh.code == 506
                      ? "数据正在刷新，您确定要导出数据吗?"
                      : ""
                  }
                    `,
                  "提示",
                  {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                  }
                )
                  .then(async () => {
                    // 11当前问题  else 历史问题
                    if (this.questionTab == '11') {
                      exportAllApi(params).then((res) => {
                        if (res.code == 200) {
                          this.exportLoading = false;
                          this.download(res.msg);
                        } else {
                          this.exportLoading = false;
                          this.msgError(res.msg);
                        }
                      });
                    } else {
                      historyExportnew(params).then((res) => {
                        if (res.code == 200) {
                          this.exportLoading = false;
                          this.download(res.msg);
                        } else {
                          this.exportLoading = false;
                          this.msgError(res.msg);
                        }
                      });
                    }
                    //显示进度条
                    this.progressFlag = true;
                    this.progressPercent = 0;
                    //每500ms执行一次函数（进度条加step=5%）
                    setTimeout(() => {
                      let timer = setInterval(async () => {
                        // 导出进度条
                        let resProgess = await refPercent();
                        if (resProgess.code == 200) {
                          this.progressPercent = parseInt(resProgess.msg);
                        } else {
                          this.progressFlag = false;
                          this.exportLoading = false;
                          this.msgError(res.msg);
                        }
                        // 父组件数据加载完前进度条最多到stopVal的这个百分值
                        if (this.progressPercent == "100") {
                          //停止执行
                          clearInterval(timer);
                          this.progressFlag = false;
                          this.exportLoading = false;
                          return;
                        }
                      }, 500);
                    }, 200);
                  })
                  .catch((e) => {
                    if (e == "cancel") {
                      this.exportLoading = false;
                    }
                  });
              } else {
                this.msgError(res.msg);
                this.exportLoading = false;
              }
            } else {
              // 条数>5000,不让导出
              this.msgError(resTotal.msg);
              this.exportLoading = false;
            }
          }
        } else if (index == 2) {
          //   报告预览
  
          if (
            (this.queryForm.timeRound == 5 && !this.queryForm.startTime) ||
            (this.queryForm.timeRound == 5 && !this.queryForm.endTime) ||
            (this.queryForm.timeRound == 5 &&
              this.queryForm.startTime > this.queryForm.endTime)
          ) {
            this.$message.error("请选择自定义时间范围");
          } else {
            let params = {
              type: this.queryForm.type,
              startTime: this.queryForm.startTime,
              endTime: this.queryForm.endTime,
              checkTaskId: this.queryForm.checkTaskId.join(),
              pageIndex: this.pageNum,
              pageSize: this.pageSize,
              assignIds: this.queryForm.assignIds.join(),
              timeRound: this.queryForm.timeRound,
              checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
            };
            sessionStorage.setItem("reportQueryForm", JSON.stringify(params));
            if (this.totalDataError != 0) {
              const newRoute = this.$router.resolve({
                path: "/indexPageSee",
                query: {
                  questionTab: this.questionTab,
                  infoType: this.queryForm.infoType.join(','),
                }
              });
              window.open(newRoute.href, "_blank");
            } else {
              this.$message.warning('无数据，暂未生成报告')
            }
          }
        }
      },
      async getListData () {
        if (
          (this.queryForm.timeRound == 5 && !this.queryForm.startTime) ||
          (this.queryForm.timeRound == 5 && !this.queryForm.endTime) ||
          (this.queryForm.timeRound == 5 &&
            this.queryForm.startTime > this.queryForm.endTime)
        ) {
          this.$message.error("请选择自定义时间范围");
          this.loading = false;
        } else {
          if (this.questionTab == "11") {
            let params = {
              type: this.queryForm.type,
              startTime:
                this.queryForm.timeRound == 5 ? this.queryForm.startTime : "",
              endTime:
                this.queryForm.timeRound == 5 ? this.queryForm.endTime : "",
              checkTaskId: this.queryForm.checkTaskId.join(),
              pageIndex: this.pageNum,
              pageSize: this.pageSize,
              assignIds: this.queryForm.assignIds.join(),
              timeRound: this.queryForm.timeRound,
              // wrongType: val,
              checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
              pagesize: this.pagesize,
              // pageNum: this.pageNum,
              infoType: this.queryForm.infoType,
            };
            sessionStorage.setItem("queryForm", JSON.stringify(params));
            let res = await searchDataApi(params);
            // 问题汇总数据
            this.tableData = res.rows;
            this.total = res.total;
            this.loading = false;
            if (this.queryForm.type == 0) {
              this.tableSiteName = "信源名称";
              this.checkwebName = "检测网站数";
              this.showwebcloumn = true;
            } else {
              this.tableSiteName = "信源名称";
              this.checkwebName = "检测媒体账号数";
              this.showwebcloumn = false;
            }
          } else if (this.questionTab == "12") {
            // let formData = new FormData();
            // formData.append("type", this.queryForm.type);
            // if (this.queryForm.timeRound == 5) {
            //   formData.append("startTime", this.queryForm.startTime);
            //   formData.append("endTime", this.queryForm.endTime);
            // }
            // formData.append("checkTaskId", this.queryForm.checkTaskId.join());
            // formData.append("pageNum", this.pageNum);
            // formData.append("pageSize", this.pageSize);
            // formData.append("assignIds", this.queryForm.assignIds.join());
            // formData.append("timeRound", this.queryForm.timeRound);
            // formData.append(
            //   "checkTaskTypeId",
            //   this.queryForm.checkTaskTypeId.join()
            // );
            // let res = await searchHistory(formData);
            // // 历史问题汇总数据
            // this.tableData = res.rows;
            // this.total = res.total;
            // this.loading = false;
            let hisparams = {
              type: this.queryForm.type,
              startTime:
                this.queryForm.timeRound == 5 ? this.queryForm.startTime : "",
              endTime:
                this.queryForm.timeRound == 5 ? this.queryForm.endTime : "",
              checkTaskId: this.queryForm.checkTaskId.join(),
              pageIndex: this.pageNum,
              pageSize: this.pageSize,
              assignIds: this.queryForm.assignIds.join(),
              timeRound: this.queryForm.timeRound,
              // wrongType: val,
              checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
              infoType: this.queryForm.infoType,
            };
            sessionStorage.setItem("queryForm", JSON.stringify(hisparams));
            let res = await searchHistory(hisparams);
            // 历史问题汇总数据
            this.tableData = res.rows;
            this.total = res.total;
            this.loading = false;
            item.loading = false;
          }
        }
      },
      // 序号连续
      getIndex (index) {
        return (this.pageNum - 1) * this.pageSize + index + 1;
      },
      // 类型选择
      clickLiTwo (index) {
        this.curLiTwo = index;
        //   this.curLiThree = 0
        if (index == 0) {
          // 系统
          this.showSystem = true;
          this.showReview = false;
          this.showMed = false;
        } else if (index == 1) {
          // 自定义
          this.showSystem = false;
          this.showReview = true;
          this.showMed = false;
          // this.getReviewTwoData()
        } else if (index == 2) {
          // 媒体
          this.showSystem = false;
          this.showMed = true;
          this.showReview = false;
          this.getDiffChange();
        }
      },
      // 获取自定义专题数据
      async getOurData () {
        let res = await getBigTitleThreeApi();
        this.ourData = res.data;
        this.ourData.map((item) => {
          this.checkOurAllId.push(item.id);
        });
      },
      async getDiffChange () {
        if (this.queryForm.type == 0) {
          // 网站
          let params = {
            pageNum: 1,
            pageSize: 50,
            isCollect: 1
          };
          let res = await getWebDataApi(params);
          this.getWebData = res.rows;
        } else if (this.queryForm.type == 1) {
          // 媒体账号
          this.getMedDataFunc();
        }
      },
      // 获取媒体账号数据
      async getMedDataFunc () {
        let params = {
          pageNum: 1,
          pageSize: 50,
          name: this.formMedSearch.name,
          isCollect: 1
        };
        if (this.queryForm.type == 0) {
          // 网站
          let res = await getWebDataApi(params);
          this.getWebData = res.rows;
        } else {
          // 媒体账号
          let res = await getMedDataApi(params);
          this.getWebData = res.rows;
        }
      },
  
      async checkLiThree (index, id) {
        this.curLiThree = index;
        this.isIndeterminateTwo = false;
        //   获取三层数据
        let params = {
          checkTaskTypeId: id,
        };
        let res = await getCheckLiThreeDataApi(params);
        this.getCheckLiThreeData = res.rows;
        let aa = [];
        for (let i = 0; i < this.getCheckLiThreeData.length; i++) {
          aa.push(this.getCheckLiThreeData[i].id);
        }
        if (this.isContained(this.queryForm.checkTaskId, aa)) {
          this.checkAllTwo = true;
        } else {
          this.checkAllTwo = false;
        }
        if (this.getCheckLiThreeData.length == 0) {
          this.checkAllTwo = false;
        }
      },
      checkLiThrees (index, item) {
        this.filterDataTree[index].tag = !this.filterDataTree[index].tag;
        if (item.webSiteAndMediaAccountList.length == 0) {
          if (this.filterDataTree[index].tag) {
            // this.checkTaskIds.push(item.id);
            // this.queryForm.assignIds = this.checkTaskIds;
            // this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          } else {
            if (this.checkTaskIds.indexOf(item.id) != -1) {
              this.checkTaskIds.splice(this.checkTaskIds.indexOf(item.id), 1);
              this.queryForm.assignIds = this.checkTaskIds;
              // this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
            }
          }
        }
        this.$forceUpdate();
      },
      //是否被包含,是返回true,不是返回false a包含b
      isContained (a, b) {
        if (!(a instanceof Array) || !(b instanceof Array)) return false;
        if (a.length < b.length) return false;
        var aStr = a.toString();
        console.info(aStr);
        for (var i = 0, len = b.length; i < len; i++) {
          console.info(aStr.indexOf(b[i]));
          if (aStr.indexOf(b[i]) == -1) return false;
        }
        return true;
      },
      // 点击切换错误类型
      clickerrorLi (index) {
        this.errorLi = index;
      },
      // 进入当前问题详情页
      goDetail (row) {
        const newPage = this.$router.resolve({
          path: "/conentReview/indexDetail/index",
          query: {
            titName: row.siteName,
            assId: row.siteId,
            questionTab: this.questionTab,
            infoType: this.queryForm.infoType.join(','),
          },
        });
        window.open(newPage.href, "_blank");
      },
      // 进入历史问题详情页
      goDetailHistory (row) {
        const newPage = this.$router.resolve({
          path: "/conentReview/indexDetail/index",
          query: {
            titName: row.siteName,
            assId: row.siteId,
            questionTab: this.questionTab,
            infoType: this.queryForm.infoType.join(','),
          },
        });
        window.open(newPage.href, "_blank");
        // const newPage = this.$router.resolve({
        //   path: "/conentReview/detailPage/index",
        //   query: {
        //     id: item.id,
        //     itemTime: item.time,
        //     itemSolrId: item.solrId,
        //     itemCheckTaskId: item.checkTaskId,
        //   },
        // });
        // window.open(newPage.href, "_blank");
      },
      goSeeDetail (val) {
        const newPage = this.$router.resolve({
          path: "/conentReview/indexList/index",
          query: {
            wrongType: val,
            quesType: this.questionTab,
            infoType: this.queryForm.infoType.join(','),
          },
        });
        window.open(newPage.href, "_blank");
      },
      // 获取当前问题历史问题-总体概况数据
      getTotalData () {
        this.loadingTwo = true;
        let params = {
          type: this.queryForm.type,
          startTime: this.curLi == 5 ? this.queryForm.startTime : "",
          endTime: this.curLi == 5 ? this.queryForm.endTime : "",
          timeRound: this.queryForm.timeRound,
          checkTaskId: this.queryForm.checkTaskId.join(),
          assignIds: this.queryForm.assignIds.join(),
          checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
          infoType: this.queryForm.infoType,
        };
        if (this.questionTab == '11') {
          getTotalDataApi(params).then(res => {
            this.loadingTwo = false;
            // if(res.code == 200){
            this.totalDataList = res;
            this.totalDataError =
              this.totalDataList.severeInfoCount +
              this.totalDataList.generalInfoCount +
              this.totalDataList.weakInfoCount;
            // }else{
            //   console.log(res);
            // }
          }).catch(err => {
            this.loadingTwo = false;
            console.log(err);
          })
        } else if (this.questionTab == '12') {
          getHistoryCount(params).then(res => {
            this.loadingTwo = false;
            // if(res.code == 200){
            this.totalDataList = res;
            this.totalDataError =
              this.totalDataList.severeInfoCount +
              this.totalDataList.generalInfoCount +
              this.totalDataList.weakInfoCount;
            // }else{
            //   console.log(res);
            // }
          }).catch(err => {
            this.loadingTwo = false;
            console.log(err);
          })
        }
      },
      // 获取总体概况数据
      // async getTotalData() {
      //   this.loadingTwo = true;
      //   let params = {
      //     type: this.queryForm.type,
      //     startTime: this.curLi == 5 ? this.queryForm.startTime : "",
      //     endTime: this.curLi == 5 ? this.queryForm.endTime : "",
      //     timeRound: this.queryForm.timeRound,
      //     checkTaskId: this.queryForm.checkTaskId.join(),
      //     assignIds: this.queryForm.assignIds.join(),
      //     checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
      //   };
      //   let res = await getTotalDataApi(params);
      //   if (res) {
      //     this.loadingTwo = false;
      //   }
      //   this.totalDataList = res;
      //   this.totalDataError =
      //     this.totalDataList.severeInfoCount +
      //     this.totalDataList.generalInfoCount +
      //     this.totalDataList.weakInfoCount;
      // },
   changeNature(value) {
    if (this.queryForm.infoType.includes(value)) {
      const indexToRemove = this.queryForm.infoType.indexOf(value)
      this.queryForm.infoType.splice(indexToRemove, 1)
    } else {
      this.queryForm.infoType.push(value)
    }
  }
    },
  };
  </script>
  <style >
  .el-message-box__header {
    border-bottom: solid 1px #ccc;
  }
  </style>
  
  <style scoped lang="scss">
  // .isGray{
  //   color: #CCCCCC;
  //   background:#FFFFFF ;
  // }
  // .gray{
  //   color:#FFFFFF;
  //   background: #1890ff;
  // }
  .processbox {
    width: 50%;
    height: 140px;
    background: #fff;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 20px;
    box-sizing: border-box;
    border-radius: 5px;
  
    .progressSpan {
      width: 100%;
      overflow: hidden;
      margin-bottom: 10px;
      display: block;
    }
  }
  
  .quesUl {
    width: 100%;
    overflow: hidden;
    padding: 0;
    margin: 30px 0px 10px 20px;
  
    li {
      flex-direction: row;
      display: inline-block;
      justify-content: flex-start;
      padding: 8px 16px;
      font-size: 14px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      box-sizing: border-box;
      line-height: 18px;
      cursor: pointer;
    }
  
    li:nth-child(1) {
      border-right: 0px;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  
    li:nth-child(2) {
      border-left: 0px;
      border-top-left-radius: 0px;
      border-bottom-left-radius: 0px;
    }
  
    .activeLi {
      background: #1890ff;
      color: #fff;
      border: solid 1px #1890ff;
    }
  }
  
  .addType {
    width: 100%;
    overflow: hidden;
    height: 34px;
    line-height: 34px;
    font-size: 14px;
    padding: 0 20px;
    color: #1265ed;
    cursor: pointer;
  }
  
  .addTopName {
    padding: 6px 20px;
    box-sizing: border-box;
    width: 100%;
    overflow: hidden;
  }
  
  .addType:hover {
    background: #f5f7fa;
  }
  
  .pData {
    margin: 0;
    padding: 0;
    display: inline-block;
    height: 28px;
    line-height: 28px;
    margin-left: 10px;
    font-size: 12px;
    color: #999;
  }
  
  .checkMoreDialog {
    ::v-deep .el-checkbox__label {
      font-size: 16px;
      color: #333;
    }
  }
  
  .butDif {
    margin-top: 4px;
  }
  
  ::v-deep .el-checkbox {
    height: 36px;
    line-height: 36px;
  }
  
  .titleBox {
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 30px;
  }
  
  .elasticTitle {
    display: inline-block;
    padding: 0;
    margin: 0;
  }
  
  .addelasticTitle {
    margin-bottom: 30px !important;
  }
  
  .third-kinds {
    max-height: 50vh;
    overflow-y: auto;
    
    span {
      display: inline-block;
      padding: 2px 10px;
      border: 1px solid #f7f7f7;
      margin-bottom: 10px;
      margin-right: 5px;
      cursor: pointer;
  
      &.active {
        border: 1px solid #3d9ffe;
      }
    }
  
    .third-item {
      display: inline-block;
    }
  }
  
  /* 设置滚动条的样式 */
  .third-kinds::-webkit-scrollbar {
      width: 5px;
      border-radius: 8px;
  }
  /* 滚动条滑块 */
  .third-kinds::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background: #8B8B8B;
  }
  
  ::v-deep .el-form--inline .el-form-item {
    margin-bottom: 0px;
  }
  
  .home {
    width: 100%;
    overflow: hidden;
    background: #f4f7fb;
    padding: 20px 5%;
    box-sizing: border-box;
    min-height: 750px;
    position: relative;
  
    .homeBox {
      width: 100%;
      background: #fff;
      border-top: solid 3px #3d9ffe;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      padding: 20px 0px;
      box-sizing: border-box;
      margin-bottom: 20px;
  
      .queryForm {
        padding: 0px 10px;
        box-sizing: border-box;
      }
  
      .checkTypeUl {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        margin: 0;
        padding: 0;
        margin-top: 4px;
        position: relative;
  
        li {
          margin-right: 16px;
          list-style: none;
          color: #606266;
          cursor: pointer;
          height: 28px;
          line-height: 28px;
          padding: 0 6px;
          border-radius: 3px;
          border: solid 1px transparent;
        }
  
        li.active {
          border: solid 1px #3d9ffe;
          color: #3d9ffe;
        }
  
        li.activeErrorLi {
          border: solid 1px #3d9ffe;
          color: #3d9ffe;
        }
      }
    }
  }
  
  .totalView {
    width: 100%;
    overflow: hidden;
  
    h2 {
      font-size: 18px;
      border-left: solid 3px #3d9ffe;
      margin-left: 25px;
      font-weight: normal;
      padding-left: 10px;
    }
  
    .totalBox {
      width: 100%;
      padding: 0 25px;
      overflow: hidden;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
  
      div {
        height: 115px;
        width: 18%;
        margin-right: 0.6%;
        color: #fff;
        box-sizing: border-box;
        text-align: center;
        cursor: pointer;
  
        &:last-child {
          margin-right: 0%;
        }
  
        span {
          display: block;
          font-size: 26px;
          margin-top: 10px;
        }
  
        em {
          font-style: normal;
          font-size: 14px;
        }
  
        p {
          font-size: 14px;
          margin: 0;
          padding: 0;
        }
  
        .seeLink {
          display: block;
          margin-top: 20px;
          color: #1178ff;
        }
      }
  
      div.boxOne {
        background: url("./../assets/images/boxOne.png") no-repeat center center;
        background-size: 100%;
      }
  
      div.boxTwo {
        background: url("./../assets/images/boxTwo.png") no-repeat center center;
        background-size: 100%;
      }
  
      div.boxThree {
        background: url("./../assets/images/boxThree.png") no-repeat center center;
        background-size: 100%;
      }
  
      div.boxFour {
        background: url("./../assets/images/boxFour.png") no-repeat center center;
        background-size: 100%;
      }
  
      div.boxFive {
        background: url("./../assets/images/boxFive.png") no-repeat center center;
        background-size: 100%;
      }
  
      div.boxSix {
        background: url("./../assets/images/boxSix.png") no-repeat center center;
        background-size: 100%;
      }
    }
  }
  
  .homeBoxTwo {
    width: 100%;
    overflow: hidden;
    background: #fff;
  
    .homeTable {
      width: 100%;
      overflow: hidden;
      padding: 10px 20px;
      box-sizing: border-box;
    }
  }
  
  .addshowCheckTypeOne {
    border: none !important;
    padding: 0 20px !important;
    position: unset !important;
    margin-bottom: 20px;
  }
  
  .showCheckTypeOne {
    max-height: 400px;
    overflow-y: auto;
    width: 500px;
    position: absolute;
    background: #fff;
    border: solid 1px #ccc;
    z-index: 10;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 5px;
    margin-top: 10px;
  
    .second-check {
      display: inline-block;
      padding: 2px 10px;
      border: 1px solid #f7f7f7;
      margin-bottom: 20px;
      line-height: 24px;
      cursor: pointer;
      font-size: 12px;
      height: 28px;
  
      &.active {
        color: #3d9ffe;
        border: 1px solid #3d9ffe;
      }
    }
  
    ul.showTypeOne {
      margin: 0;
      padding: 0;
      width: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      flex-wrap: wrap;
  
      li {
        list-style: none;
        margin-right: 10px;
        // padding: 0 6px;
        border-radius: 3px;
        // height: 26px;
        line-height: 26px;
        border: solid 1px #ccc;
        cursor: pointer;
        margin-bottom: 10px;
        font-size: 12px;
      }
  
      li.active {
        border-color: #3d9ffe;
        color: #3d9ffe;
      }
    }
  }
  .showTypeSpan{
    display: inline-block;
    padding: 3px 6px;
    height: 26px;
  }
  .customThem {
    position: absolute;
    left: 7px;
    top: 400px;
    width: 80px;
    background: #fff;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    padding: 8px 0px;
    box-sizing: border-box;
  
    p {
      display: block;
      line-height: 26px;
      margin: 0;
      padding: 0;
    }
  }
  
  .customThem:hover {
    background: #3d9ffe;
  
    p {
      color: #fff;
    }
  }
  .el-popover {
    height: 200px;
    overflow: auto;
  }
  </style>
  