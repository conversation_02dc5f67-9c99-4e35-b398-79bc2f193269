import request from '@/utils/request'

//  获取问题汇总详情概览
export function getQueDataApi(query) {
    return request({
        url: '/home/<USER>/overview',
        method: 'get',
        params: query
    })
}
// 问题汇总详情列表
export function getQuestionApi(data) {
    return request({
        url: '/home/<USER>/detail',
        method: 'post',
        data
    })
}


//  获取历史问题汇总详情概览
export function getHistoryCount(query) {
    return request({
        url: '/home/<USER>',
        method: 'get',
        params: query
    })
}
// 历史问题汇总详情列表
export function getHistoryDetail(data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data
    })
}

// 历史问题- 单个和批量过滤数据
export function deletsFDialogApi (data) {
    return request({
        url: '/task/checkTaskInfo/update',
        method: 'post',
        data: data
    })
}

// 历史问题 - 单个处置 
export function cancelAdd (data) {
    return request({
        url: '/task/info/operate/add',
        method: 'post',
        data: data
    })
}

// 历史问题 - 单个处置的详情
export function cancelDetails (checkTaskInfoId) {
    return request({
        url: '/task/info/operate/' + checkTaskInfoId,
        method: 'get',
    })
}
// 历史问题 - 修改单个处置
export function cancelEdit (data) {
    return request({
        url: '/task/info/operate',
        method: 'put',
        data: data
    })
}
// 历史问题 -批量处置 批量查看
export function mulCancelBatch (data) {
    return request({
        url: '/task/info/operate/add/batch',
        method: 'post',
        data: data
    })
}
// 获取处置的历史数据
export function getCancelStatus (operateType) {
    return request({
        url: '/task/info/operate/history/'+ operateType,
        method: 'get',
    })
}
// 当前问题-批量过滤
export function currentdeleteMulApi (data) {
    return request({
        url: '/task/checkTaskInfo/updateSimilar',
        method: 'post',
        data: data
    })
}