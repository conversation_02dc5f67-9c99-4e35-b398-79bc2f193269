<template>
  <div>
    <el-radio-group v-model="tabPosition" style="margin-bottom: 30px">
      <el-radio-button label="1">画面检测</el-radio-button>
      <el-radio-button label="2">音频检测</el-radio-button>
    </el-radio-group>
    <div v-if="tabPosition == 1" v-loading="loading">
      <div class="dialogBox" v-if="seeData && seeData.length">
        <div class="dialogDiv" v-for="item, index in seeData" :key="index">
            <img :src="item.url" alt="">
            <ul v-if="item.picLabel && item.picLabel.length">
              <li><span>识别内容：</span>
                <el-tooltip class="item" effect="dark" :content="item.picLabel[0].value" placement="top-start">
                  <p>{{ item.picLabel[0].value }}</p>
                </el-tooltip>
              </li>
              <li><span>命中级别：</span>
                <el-tooltip class="item" effect="dark"
                  :content="item.picLabel[0].level == 1 ? '疑似' : item.picLabel[0].level == 2 ? '确定' : item.picLabel[0].level == 0 ? '正常' : ''"
                  placement="top-start">
                  <p>{{ item.picLabel[0].level == 1 ? '疑似' : item.picLabel[0].level == 2 ? '确定' : item.picLabel[0].level == 0 ? '正常' : '' }}</p>
                </el-tooltip>
              </li>
              <li><span>类型：</span>
                <el-tooltip class="item" effect="dark" :content="item.picLabel[0].labelName" placement="top-start">
                  <p>{{ item.picLabel[0].labelName }}</p>
                </el-tooltip>
              </li>
              <li><span>具体类型：</span>
                <el-tooltip class="item" effect="dark" :content="item.picLabel[0].secondLabelName" placement="top-start">
                  <p>{{ item.picLabel[0].secondLabelName }}</p>
                </el-tooltip>
              </li>
              <li class="liDiff"><span>时间段：</span>
                <el-tooltip class="item" effect="dark" :content="item.duration" placement="top-start">
                  <span>{{ item.duration }}</span>
                </el-tooltip>
              </li>
              <li class="liDiff">
                <span>截图：</span>
                <em @click="jumpURL(item.url)" style="margin-left: 10px;">查看截图</em>
                <em @click="copyMe" :data-clipboard-text="item.url" class="copyBtn"
                  style="margin-left: 10px;">复制URL</em>
              </li>
            </ul>
          </div>
      </div>
      <div v-else class="dialogNull">检测正常，无敏感内容</div>
    </div>
    <div v-else-if="tabPosition == 2">
      <el-table :data="audio" width="100" v-if="audio && audio.length">
          <el-table-column label="时间戳" prop="duration" align="center">
          </el-table-column>
          <el-table-column label="命中级别" prop="result" align="center">
            <template slot-scope="scope">
              {{ scope.row.audioLabel[0].level == 2 ? '不通过' : scope.row.audioLabel[0].level == 1 ? '嫌疑' : '通过' }}
            </template>
          </el-table-column>
          <el-table-column label="数据类别" prop="timeRange" align="center">
            <template slot-scope="scope">
              <span v-show="scope.row.audioLabel[0].labelName">{{ scope.row.audioLabel[0].labelName }}</span>
              <span v-show="scope.row.audioLabel[0].secondLabelName">-{{ scope.row.audioLabel[0].secondLabelName }}</span>
              <span v-show="scope.row.audioLabel[0].thirdLabelName">-{{ scope.row.audioLabel[0].thirdLabelName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="识别内容" prop="content" align="center">
            <template slot-scope="scope">
              <span v-html="replaceWrong(scope.row)" class="emRed"></span>
            </template>
          </el-table-column>
        </el-table>
      <div v-else class="dialogNull">检测正常，无敏感内容</div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tabPosition: 1,
      seeData: [],//视频数据
      audio: [],//音频数据
      loading:false,
    }
  },
  props: ['wrongData'],
  created() {
  },
  methods: {
    async getListData() {
      this.tabPosition = 1
      this.loading=true
      // 获取查看内容
      let {audioSegment,videoSegment}=this.wrongData
      this.seeData = videoSegment
      this.audio = audioSegment
      this.loading=false
    },
    // 查看原文
    jumpURL(url) {
      window.open(url, '_blank')
    },
    // 复制
    copyMe() {
      var clipboard = new this.Clipboard('.copyBtn')
      clipboard.on('success', () => {
        this.$message.success('复制成功')
        clipboard.destroy()
      })
      clipboard.on('error', () => {
        this.$message.error('复制失败')
        clipboard.destroy()
      })
    },
    replaceWrong(row){
      return row.content.replace(new RegExp(row.audioLabel[0].value, 'g'), `<em>${row.audioLabel[0].value}</em>`);
    }
  },

};

</script>

<style scoped lang="scss">

.dialogNull {
  width: 100%;
  height: 500px;
  line-height: 500px;
  text-align: center;
}
.dialogBox {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.dialogDiv {
  width: 32%;
  overflow: hidden;
  margin-bottom: 20px;
  margin-right: 2%;

  img {
    max-width: 100%;
    overflow: hidden;
    max-height: 270px;
    margin: 0 auto;
  }

  ul {
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    font-size: 0.8vw;

    li {
      list-style: none;
      width: 50%;
      // height: 38px;
      // line-height: 38px;
      height: 1.8vw;
      line-height: 1.8vw;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;

      span {
        display: inline-block;
        // width: 53%;
      }

      p {
        margin: 0;
        padding: 0;
        width: 46%;
        overflow: hidden;
        white-space: nowrap; //不折行
        text-overflow: ellipsis; //溢出显示省略号

      }

      em {
        font-style: normal;
        display: inline-block;
        color: #02A7F0;
        cursor: pointer;

      }
    }

    li:nth-child(1n) {
      width: 45%;

      span {
        // width: 48%;
      }
    }

    li:nth-child(2n) {
      width: 55%;
    }


    li.liDiff {
      width: 100%;

      span {
        // width: 20%;
      }

      p {
        width: 70%;
        overflow: hidden; //超出隐藏
        white-space: nowrap; //不折行
        text-overflow: ellipsis; //溢出显示省略号
      }
    }
  }
}

.dialogDiv:nth-child(3n) {
  margin-right: 0;
}
</style>
