import request from '@/utils/request'

// 搜索
export function handleTFApi (query) {
    return request({
        url: '/task/correctMistakeWord/list',
        method: 'get',
        params: query
    })
}
// 新增
export function AddTFApi (data) {
    return request({
        url: '/task/correctMistakeWord',
        method: 'post',
        data: data
    })
}
// 修改
export function editTFApi (data) {
    return request({
        url: '/task/correctMistakeWord',
        method: 'put',
        data: data
    })
}
// 删除

export function delTFApi (ids) {
    return request({
        url: '/task/correctMistakeWord/remove/' + ids,
        method: 'delete',
    })
}
// 查看
export function seeTFApi (id) {
    return request({
        url: '/task/correctMistakeWord/' + id,
        method: 'get',
    })
}
// 导入过滤词
export function saveImportTFApi (data) {
    return request({
        url: '/task/correctMistakeWord/saveImport',
        method: 'post',
        data: data
    })
}
// 过滤词导入模板下载
export function fileTFDownApi () {
    window.location.href = process.env.VUE_APP_BASE_API + "/task/correctMistakeWord/downModel"
}