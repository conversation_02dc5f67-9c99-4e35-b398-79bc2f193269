import request from '@/utils/request'

// 查询备案网站列表
export function listWebsite (query) {
    return request({
        url: '/platform/website/list',
        method: 'get',
        params: query
    })
}

// 查询备案网站详细
export function getWebsite (id) {
    return request({
        url: '/platform/website/' + id,
        method: 'get'
    })
}

// 新增备案网站
export function addWebsite (data) {
    return request({
        url: '/platform/website',
        method: 'post',
        data: data
    })
}

// 修改备案网站
export function updateWebsite (data) {
    return request({
        url: '/platform/website',
        method: 'put',
        data: data
    })
}

// 删除备案网站
export function delWebsite (id) {
    return request({
        url: '/platform/website/' + id,
        method: 'delete'
    })
}

// 导出备案网站
export function exportWebsite (query) {
    return request({
        url: '/platform/website/export',
        method: 'get',
        params: query
    })
}
// 备案网站模板下载
export function fileWebDownApi () {
    window.location.href = process.env.VUE_APP_BASE_API + "/platform/website/downModel"
}
// 确定上传文件按钮
export function saveImportWebsiteApi () {
    return request({
        url: '/platform/website/saveImportWebsite',
        method: 'get',
    })
}
// 获取所选分组数据
export function getOwnListApi (data) {
    return request({
        url: '/manage/siteType/siteTypeByCustomerSource',
        method: 'post',
        data: data
    })
}
// 修改分组
export function submitFenFormApi (data) {
    return request({
        url: '/manage/siteType/sourceGrouping',
        method: 'post',
        data: data
    })
}