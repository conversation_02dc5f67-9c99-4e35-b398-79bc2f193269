<template>
  <div>
    <canvas ref="canvas" :width="canvasWidth" :height="canvasHeight"></canvas>
  </div>
</template>

<script>
export default {
  data() {
    return {
      canvas: null, // canvas元素
      context: null, // canvas绘图上下文
      canvasWidth: 500,// 设置canvas的宽度为500px
      canvasHeight: 500,// 设置canvas的高度为500px
    };
  },
  props: {
    errorData: {
      type: Object,
      default: {},
    },
  },
  mounted() {
    // this.drawCanvas();
  },
  watch: {
    errorData: {
      handler(newErrorData) {
        this.drawCanvas(newErrorData);
      },
      immediate: true, // 在组件挂载时立即执行一次
      deep: true, // 如果errorData是复杂对象，开启深度监听
    },
  },
  methods: {
    drawCanvas(errorData = this.errorData) {
      console.log("errorData", errorData);
      // 创建一个Image对象
      const image = new Image();

      // 创建一个Promise，用于在图片加载完成后执行后续操作
      const loadImage = new Promise((resolve, reject) => {
        image.onload = resolve;
        image.onerror = reject;
      });

      // 设置图片的src属性，开始加载图片
      image.src = errorData.picUrl||errorData.url;

      // 等待图片加载完成
      loadImage
        .then(() => {
          this.canvas = this.$refs.canvas; // 获取canvas元素
          this.context = this.canvas.getContext("2d"); // 获取canvas的2D绘图上下文

          // 计算canvas的宽度和高度
          const imageAspectRatio = image.width / image.height;
          this.canvasHeight = this.canvasWidth / imageAspectRatio;

          // 在下一个DOM更新周期中绘制图片和矩形框
          this.$nextTick(() => {
            // 在canvas上绘制图片
            this.context.drawImage(
              image,
              0,
              0,
              this.canvasWidth,
              this.canvasHeight
            );

            // 绘制红色矩形框
            errorData.picLabel &&
              errorData.picLabel.forEach((error, index) => {
                const { x1, y1, x2, y2 } = error;
                const rectX = x1 * this.canvasWidth;
                const rectY = y1 * this.canvasHeight;
                const rectWidth = (x2 - x1) * this.canvasWidth;
                const rectHeight = (y2 - y1) * this.canvasHeight;

                this.context.strokeStyle = "red";
                this.context.lineWidth = 2;
                this.context.strokeRect(rectX, rectY, rectWidth, rectHeight);
              });
          });
        })
        .catch((error) => {
          // console.error("图片加载失败:", error);
        });
    },
  },
};
</script>

<style>
canvas {
  /* border: 1px solid black; */
}
</style>
