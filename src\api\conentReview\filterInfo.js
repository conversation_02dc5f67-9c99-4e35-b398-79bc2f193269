import request from '@/utils/request'

// 查询任务管理列表
export function getInfoDataApi (post) {
    return request({
        url: '/task/checkTaskInfo/list',
        method: 'post',
        params: post
    })
}
// 获取过滤信息列表数据
export function getInfoListApi (query) {
    return request({
        url: '/task/checkTaskInfo/listFilterInfo',
        method: 'get',
        params: query
    })
}
// 取消过滤
export function cancelFilterApi (params) {
    return request({
        url: '/task/checkTaskInfo/cancelFilter',
        method: 'post',
        data: params
    })
}
// 查看备注信息
export function seeInfoApi (id) {
    return request({
        url: '/task/infoFilter/' + id,
        method: 'get',
    })
}
// 备注信息查看和修改
export function sureRemarkApi (data) {
    return request({
        url: '/task/infoFilter',
        method: 'put',
        data: data
    })
}
// 获取当前用户的网站列表
export function websiteList (data) {
    return request({
        url: '/platform/website/listByUser',
        method: 'get'
    })
}
// 获取当前用户媒体账号列表
export function mediaList (data) {
    return request({
        url: '/platform/mediacount/listByUser',
        method: 'get'
    })
}

