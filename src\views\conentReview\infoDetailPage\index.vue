<template>
  <div class="home">
    <div class="homeBox">
      <div class="homeLeftBox">
        <div class="homeLeftHead">
          <span>信息详情</span>
        </div>
        <div class="homeLeftUl">
          <h2 class="emRed" v-html="detailDataList.title"></h2>
          <ul>
            <li>来源：{{ detailDataList.host_s }}</li>
            <li style="cursor: pointer; color: #409eff">
              <a
                :href="detailDataList.url"
                referrerpolicy="no-referrer"
                target="_blank"
                >查看原文</a
              >
            </li>
            <li>原文时间：{{ this.$route.query.time }}</li>
          </ul>
          <p class="emRed" v-html="detailDataList.text"></p>
        </div>
      </div>
      <!-- <div class="homeRight">
        <span>词语建议</span>
        <el-table :data="detailDataList.data.wordList"
                  border=""
                  :row-style="{height:10+'px'}"
                  :cell-style="{padding:6+'px'}"
                  :header-cell-style="{ height:'10px', padding:'6px'}">
          <el-table-column prop="mistakeWord"
                           label="问题词语"></el-table-column>
          <el-table-column prop="correctWord"
                           label="建议词语"></el-table-column>
        </el-table>
      </div>-->
    </div>
  </div>
</template>

<script>
import { getDetailDataTwoApi } from "@/api/conentReview/detailPage";
export default {
  data() {
    return {
      tableData: [{ name: "扶贫攻坚", nameTwo: "脱贫攻坚" }],
      detailDataList: [],
    };
  },
  created() {
    this.getDetailDataTwo();
  },
  methods: {
    //   获取详情页数据
    async getDetailDataTwo() {
      let params = {
        solrId: this.$route.query.id,
        time: this.$route.query.time,
        word: this.$route.query.word,
        quadraticWord: this.$route.query.quadraticWord
      };
      let res = await getDetailDataTwoApi(params);
      this.detailDataList = res.data;
    },
    // 查看原文
    // goOrgText() {
    //   window.open(this.detailDataList.url);
    // },
  },
};
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 10%;
  box-sizing: border-box;
  min-height: 889px;
  .homeBox {
    width: 100%;
    background: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 20px;
    box-sizing: border-box;
    .homeLeftBox {
      width: 100%;
      overflow: hidden;
      margin-bottom: 20px;
      .homeLeftHead {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        background: #f5f5f5;
        .homBut {
          display: inline-block;
          padding-top: 6px;
          padding-right: 10px;
        }
        span {
          display: inline-block;
          color: #fff;
          background: #ed9e2f;
          height: 40px;
          line-height: 40px;
          padding: 0 10px;
          font-size: 14px;
        }
      }
      .homeLeftUl {
        width: 100%;
        overflow: hidden;
        h2 {
          width: 100%;
          overflow: hidden;
          font-weight: normal;
          font-size: 24px;
        }
        ul {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          margin: 0;
          padding: 0;
          height: 30px;
          line-height: 30px;
          font-size: 14px;
          li {
            list-style: none;
            margin-right: 10px;
          }
        }
        p {
          line-height: 28px;
        }
      }
    }
    .homeRight {
      background: #fff;
      width: 30%;
      span {
        display: block;
        width: 100%;
        height: 40px;
        line-height: 40px;
        background: #2899dd;
        color: #fff;
        text-align: center;
        font-size: 14px;
        margin-bottom: 20px;
      }
    }
  }
}
</style>