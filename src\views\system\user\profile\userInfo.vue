<template>
  <div>
    <el-form ref="form" :model="user" :rules="rules" label-width="120px">
      <el-form-item label="用户昵称" prop="nickName">
        <el-input v-model.trim="user.nickName" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phonenumber">
        <el-input v-model.trim="user.phonenumber" maxlength="11" />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model.trim="user.email" maxlength="50" />
      </el-form-item>
      <el-form-item label="性别">
        <el-radio-group v-model="user.sex">
          <el-radio label="0">男</el-radio>
          <el-radio label="1">女</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="微信">
        <p class="classP" v-show="isBind == false">
          <span @click="bindWechat">
            <img src="../../../../assets/images/weixinLogo.png" alt />
            未绑定
          </span>
          <span v-show='showwechat'>（绑定微信后，需关注微信公众号即可接收预警信息）</span>
          <el-tooltip class="item" effect="light" placement="top" popper-class="tip-class">
            <div slot="content">
              <img src="../../../../assets/images/wechatmore.png" alt="" />
            </div>
            <img v-show='showwechat' style="width: 135px; height: 36px; cursor: pointer"
              src="../../../../assets/images/wechatclick.png" alt="" />
          </el-tooltip>
        </p>
        <p class="classP" v-show="isBind == true">
          <img src="../../../../assets/images/weixinLogo.png" alt />
          <span @click="cancelWechat">
            {{ wechatNickName }}
          </span>
          <el-tooltip class="item" effect="light" placement="top" popper-class="tip-class">
            <div slot="content">
              <img src="../../../../assets/images/wechatmore.png" alt="" />
            </div>
            <img v-show='showwechat' style="width: 135px; height: 36px; cursor: pointer"
              src="../../../../assets/images/wechatclick.png" alt="" />
          </el-tooltip>
        </p>
      </el-form-item>
      <el-form-item label="微信推送" style="display: none">
        <span slot="label">
          <img class="imgWechat" src="../../../../assets/images/weixinLogo.png" alt="" />微信推送</span>
        <el-tooltip class="item" effect="light" placement="top-start" popper-class="tip-class">
          <div slot="content">
            <img src="../../../../assets/images/wechatmore.png" alt="" />
          </div>
          <p class="classP"><em style="color: red">* </em>此处扫码关注公众号</p>
        </el-tooltip></el-form-item>
      <el-form-item>
        <el-button type="primary" size="mini" @click="submit">保存</el-button>
        <el-button type="danger" size="mini" @click="close">关闭</el-button>
      </el-form-item>
    </el-form>

    <!-- 微信扫码弹框 -->
    <el-dialog title="绑定微信" :visible.sync="dialogVisible" width="380px" :before-close="closeBeforeDialog">
      <div class="wechatImgBox" v-show="weChatNameOne">
        <img :src="wechatImg" alt class="wechatStyle" v-show="wechatImg" />
        <p>微信扫码绑定</p>
      </div>
      <div class="wechatImgBoxTwo" v-show="weChatNameTwo">
        <p>
          <img src="../../../../assets/images/sureChoose.png" alt="" />绑定微信成功
        </p>
        <div>绑定微信: {{ weChatName }}</div>
        <p>{{ timeRedice }} s返回个人中心</p>
        <div>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="closeDialog">确定</el-button>
          </span>
        </div>
      </div>
      <div class="rejectShow" v-show="weChatNameThree">
        <img src="../../../../assets/login/tan.png" alt="" />
        <p>您已取消此次绑定</p>
        <el-button type="primary" @click="tryAgain">重试</el-button>
      </div>
    </el-dialog>
    <!-- 微信解绑 -->
  </div>
</template>

<script>
import { updateUserProfile, cancelWechatUserApi } from "@/api/system/user";
import {
  getWechatlogoApi,
  getCodeStatusApi,
  checkpersonLoginApi,
} from "@/api/login";
import { getUserProfile } from "@/api/system/user";
export default {
  props: {
    user: {
      type: Object,
    },
  },
  data () {
    return {
      tiemr: null,
      timerTwo: null,
      timeRedice: "",
      showCodeWechat: true,
      weChatName: "",
      isBind: "",
      dialogVisible: false,
      wechatImg: "",
      // 表单校验
      rules: {
        nickName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" },
        ],
        email: [
          { required: true, message: "邮箱地址不能为空", trigger: "blur" },
          {
            type: "email",
            message: "'请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
        phonenumber: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
      weChatNameOne: false,
      weChatNameTwo: false,
      weChatNameThree: false,
      wechatNickName: "",
      wechatQRCodeId: "",
      showwechat: false,
    };
  },
  watch: {
    codeStatus: {
      handler (val) {
        if (val == "BIND") {
          clearInterval(this.timer);
        }
      },
      immediate: true,
    },
    // weChatName: {
    //   handler(val) {
    //     if (val) {
    //       this.timeRediceFun();
    //       clearInterval(this.timer);
    //       this.getWechatStatus();
    //     } else {
    //       this.dialogVisible = false;
    //     }
    //   },
    //   immediate: true,
    // },
  },
  created () {
    this.getWechatStatus();
    this.getchatMore()
  },
  methods: {
    getchatMore () {
      let url = document.location.pathname;
      let index = url.substr(url.indexOf('/', url.indexOf('/') - 1) + 1).substring(0, url.substr(url.indexOf('/', url.indexOf('/') - 1) + 1).indexOf('/'));
      if (index == 'gzyh') {
        this.showwechat = false
      } else {
        this.showwechat = true
      }
    },
    closeDialog () {
      this.dialogVisible = false;
      clearInterval(this.timerTwo);
      clearInterval(this.timer);
      this.timerTwo = null;
      this.timer = null;
    },
    // 获取微信是否绑定
    async getWechatStatus () {
      let res = await getUserProfile();
      this.isBind = res.isBind;
      this.wechatNickName = res.wechatName;
    },
    closeBeforeDialog () {
      this.dialogVisible = false;
      clearInterval(this.timer);
      clearInterval(this.timerTwo);
      this.timer = null;
      this.timerTwo = null;
    },
    // 打开绑定微信的弹框，出现二维码
    async bindWechat () {
      this.dialogVisible = true;
      this.weChatNameOne = true;
      this.weChatNameTwo = false;
      this.weChatNameThree = false;
      this.getcodeImg();
    },
    // 获取二维码
    async getcodeImg () {
      let params = {
        position: "USER_CENTER",
      };
      let res = await getWechatlogoApi(params);
      this.wechatImg = res.data.qRCode;
      this.wechatQRCodeId = res.data.wechatQRCodeId;
      this.start();
    },
    // 解绑微信
    cancelWechat () {
      this.$confirm("此操作将解绑该微信号, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          cancelWechatUserApi();
          this.$message({
            type: "success",
            message: "解绑成功!",
          });
          this.getWechatStatus();
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消解绑",
          });
        });
    },
    // 获取二维码状态
    start () {
      // 获取二维码状态
      this.timer = setInterval(() => {
        let params = {
          wechatQRCodeId: this.wechatQRCodeId,
        };
        getCodeStatusApi(params).then((res) => {
          this.codeStatus = res.data;
          if (this.codeStatus == "BIND") {
            // let params = {
            //   wechatQRCodeId: this.wechatQRCodeId,
            // };
            checkpersonLoginApi(params).then((res) => {
              this.weChatName = res.weChatName;
              this.timeRediceFun();
              this.getWechatStatus();
              this.weChatNameOne = false;
              this.weChatNameTwo = true;
              this.weChatNameThree = false;
              clearInterval(this.timer);
              this.timer = null;
            });
          } else if (this.codeStatus == "REJECT_BIND") {
            this.weChatNameOne = false;
            this.weChatNameTwo = false;
            this.weChatNameThree = true;
            clearInterval(this.timer);
            this.timer = null;
          } else if (this.codeStatus == "USER_CENTER_CREATED") {
          }
        });
      }, 2000);
    },

    submit () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          updateUserProfile(this.user).then((response) => {
            this.msgSuccess("修改成功");
          });
        }
      });
    },
    close () {
      this.$store.dispatch("tagsView/delView", this.$route);
      this.$router.push({ path: "/index" });
    },
    // 3秒倒计时
    timeRediceFun () {
      const TIME_COUNT = 3;
      if (!this.timerTwo) {
        this.timeRedice = TIME_COUNT;
        this.timerTwo = setInterval(() => {
          if (this.timeRedice > 0 && this.timeRedice <= TIME_COUNT) {
            this.timeRedice--;
            if (this.timeRedice == 0) {
              clearInterval(this.timerTwo);
              this.timerTwo = null;
              this.dialogVisible = false;
            }
          } else {
            clearInterval(this.timerTwo);
            this.timerTwo = null;
          }
        }, 1000);
      }
    },
    // 重试
    tryAgain () {
      this.weChatNameOne = true;
      this.weChatNameTwo = false;
      this.weChatNameThree = false;
      this.getcodeImg();
    },
  },
  destroyed () {
    clearInterval(this.timer);
  },
};
</script>

<style lang="scss" scoped>
.rejectShow {
  width: 100%;
  overflow: hidden;
  text-align: center;
  padding: 40px 0px 20px;
  box-sizing: border-box;

  img {
    display: inline-block;
    width: 40px;
    height: 40px;
  }

  p {
    margin: 20px 0px 30px;
    padding: 0;
    text-align: center;
  }
}

.wechatImgBox {
  width: 100%;
  overflow: hidden;
  text-align: center;

  .wechatStyle {
    width: 180px;
    height: 180px;
    display: block;
    margin: 0 auto 10px;
    border: solid 1px #ccc;
    border-radius: 8px;
    padding: 3px;
    box-sizing: border-box;
  }

  p {
    width: 100%;
    overflow: hidden;
    margin: 0 0 10px 0;
  }
}

.wechatImgBoxTwo {
  width: 100%;
  overflow: hidden;

  p {
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    color: #36a9ce;
    line-height: 30px;
    text-align: center;

    img {
      display: inline-block;
      margin-right: 10px;
      width: 30px;
      height: 30px;
      vertical-align: middle;
    }
  }

  div {
    text-align: center;
    margin: 20px 0px;
  }
}

.classP {
  display: inline-block;
  margin: 0;
  padding: 0;

  span {
    color: #ccc;
    display: inline-block;
    cursor: pointer;
  }

  img {
    display: inline-block;
    width: 24px;
    height: 24px;
    vertical-align: middle;
    margin-right: 6px;
  }
}

.imgWechat {
  display: inline-block;
  width: 24px;
  height: 24px;
  vertical-align: middle;
  margin-right: 6px;
}
</style>
