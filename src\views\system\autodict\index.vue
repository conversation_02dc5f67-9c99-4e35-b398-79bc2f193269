<template>
  <div class="app-container">
    <div class="homeTopAutodict">
      <el-radio-group v-model="tabPosition" @change="changeType">
        <el-radio-button
          v-for="item in wordType"
          :label="item.value"
          :key="item.value"
          :value="item.value"
          >{{ item.name }}</el-radio-button
        >
      </el-radio-group>
    </div>

    <div class="home">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        label-width="120px"
      >
        <el-row>
          <el-form-item
            :label="
              tabPosition == 1
                ? '正词：'
                : tabPosition == 2
                ? '敏感词：'
                : '错误词：'
            "
            prop="problemWord"
          >
            <el-input
              v-model.trim="queryParams.problemWord"
              @input="changeEvent($event)"
              :placeholder="
                tabPosition == 1
                  ? '请输入正词'
                  : tabPosition == 2
                  ? '请输入敏感词'
                  : '请输入错误词'
              "
              clearable
              size="small"
            />
          </el-form-item>
          <el-form-item
            v-show="tabPosition == 2 || tabPosition == 3"
            :label="tabPosition == 2 ? '专题筛选' : '专题筛选'"
            prop="checkTaskId"
          >
            <el-select
              size="small"
              v-model="queryParams.checkTaskId"
              :placeholder="
                tabPosition == 2 ? '请选择专题' : '请选择专题'
              "
            >
              <el-option
                v-for="item in tasksenDataOption"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="用户名称"
            prop="userName"
            v-if="checkPermi(['system:autodict:userName'])"
          >
            <el-select
              v-model="queryParams.userName"
              size="mini"
              placeholder="请选择用户名称"
              clearable
            >
              <el-option
                :label="`${item.userName}(${item.nickName})`"
                :value="item.userName"
                :key="item.userId"
                v-for="item in userNameSelect"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="添加时间：">
            <el-date-picker
              v-model="dataTime"
              @change="handleDateChange"
              size="small"
              unlink-panels
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
            <!-- <div style="line-height: 28px">
              <el-date-picker
                v-model="queryParams.startTime"
                size="small"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择开始时间"
              ></el-date-picker
              >-
              <el-date-picker
                v-model="queryParams.endTime"
                size="small"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择结束时间"
              ></el-date-picker>
            </div> -->
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-row>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:autodict:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5" style="margin-bottom: 10px">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:autodict:del']"
            >批量删除</el-button
          >
        </el-col>
      </el-row>
      <el-alert
        :title="`已选择了${sureNum}条数据`"
        v-show="showsureNum"
        style="
          color: #8cc5ff;
          background-color: #ecf5ff;
          border-color: #d9ecff;
          margin-bottom: 10px;
        "
        :closable="false"
      ></el-alert>
      <el-table
        v-loading="loading"
        :data="websiteList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          label="序号"
          :index="getIndex"
          width="80"
        ></el-table-column>
        <el-table-column
          label="用户名称"
          align="center"
          prop="createNickName"
          show-overflow-tooltip
          v-if="checkPermi(['system:autodict:userName'])"
        >
          <template slot-scope="scope" v-if="scope.row.createNickName"
            >{{ scope.row.createBy }}({{ scope.row.createNickName }})</template
          >
        </el-table-column>
        <el-table-column
          label="正词"
          align="center"
          prop="properWord"
          show-overflow-tooltip
          v-if="tabPosition == 1"
        />
        <el-table-column
          label="敏感词"
          align="center"
          prop="problemWord"
          show-overflow-tooltip
          v-if="tabPosition == 2"
        />
        <el-table-column
          label="所属专题"
          align="center"
          prop="taskName"
          show-overflow-tooltip
          v-if="tabPosition == 2"
        />
        <el-table-column
          label="错误词"
          align="center"
          prop="problemWord"
          show-overflow-tooltip
          v-if="tabPosition == 3"
        />
        <el-table-column
          label="建议词"
          align="center"
          prop="suggestWord"
          :show-overflow-tooltip="true"
          v-if="tabPosition == 3"
        />
        <el-table-column
          label="所属专题"
          align="center"
          prop="taskName"
          v-if="tabPosition == 3"
        />

        <el-table-column label="添加时间" align="center" prop="createTime" show-overflow-tooltip/>
        <el-table-column label="添加人" align="center" prop="createBy" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          v-if="checkPermi(['system:autodict:operate'])"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row, 1)"
              v-hasPermi="['system:autodict:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:autodict:del']"
              >删除</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleUpdate(scope.row, 2)"
              v-hasPermi="['system:autodict:see']"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
        style="margin-bottom: 30px"
      />

      <!-- 添加或修改备案网站对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="600px"
        append-to-body
      >
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="formForm"
        >
          <el-form-item label-width="0">
            <p v-show="tabPosition == 1">
              说明：将词语添加为正词后，再出现该词语时，首页问题汇总、审校专题、
              文稿审校版块都将不会提示错误。
            </p>
            <p v-show="tabPosition == 2">
              说明：将词语添加为敏感词后，再出现该词语时，首页问题汇总、审校专题版块将会提示敏感，文稿审校版块将会提示错误但不会建议正确词语。
            </p>
            <p v-show="tabPosition == 3">
              说明：将未提示错误的词语添加入错误词库后，再出现该词语时，首页问题汇总、审校专题
              版块将会提示错误，文稿审校版块将会提示错误并给出建议正确词。
            </p>
          </el-form-item>
          <el-form-item
            label="建议词："
            :prop="tabPosition == 3 ? 'suggestWord' : ''"
            v-show="tabPosition == 3"
          >
            <el-input
              type="textarea"
              v-model="form.suggestWord"
              placeholder="请输入建议词，多个请用空格隔开；建议词和错误词需要一一对应"
              :disabled="onlySee"
            />
          </el-form-item>
          <el-form-item
            :label="tabPosition == 2 ? '专题筛选：' : '专题筛选：'"
            :prop="
              tabPosition == 2
                ? 'checkTaskId'
                : tabPosition == 3
                ? 'checkTaskId'
                : ''
            "
            v-show="tabPosition == 2 || tabPosition == 3"
          >
            <el-select
              v-model="form.checkTaskId"
              :placeholder="
                tabPosition == 2 ? '请选择专题或前往专题设置中添加。' : '请选择专题或前往专题设置中添加。'
              "
              style="width: 100%"
              :disabled="onlySee"
            >
              <el-option
                v-for="item in tasksenDataOption"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            :label="
              tabPosition == 1
                ? '正词：'
                : tabPosition == 2
                ? '敏感词：'
                : '错误词：'
            "
            prop="properWord"
          >
            <el-input
              type="textarea"
              v-model="form.properWord"
              @input="changeEvent($event)"
              :placeholder="
                tabPosition == 1
                  ? '请输入正词,多个请用空格隔开'
                  : tabPosition == 2
                  ? '请输入敏感词,多个请用空格隔开'
                  : '请输入错误词,多个请用空格隔开'
              "
              :disabled="onlySee" maxlength="500" show-word-limit
            />
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: center">
          <el-button type="primary" @click="submitForm" :loading="commitLoading">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getsensitiveWordApi,
  delsensitiveWord,
  updatesensitiveWord,
  addsensitiveWord,
  seesensitiveWordApi,
  getSensitiveApi,
  getTrueWordApi,
  delTrueWord,
  updateTrueWord,
  addTrueWord,
  seeTrueWordApi,
} from "@/api/system/autodict";
import { getUserDataApi } from "@/api/conentReview/themManag";
import { checkPermi } from "@/utils/permission.js";
export default {
  data() {
    return {
      tasksenDataOption: [],
      tabPosition: 1,
      wordType: [
        { name: "正词", value: 1 },
        { name: "敏感词", value: 2 },
        { name: "错误词", value: 3 },
      ],
      showsureNum: false,
      // 选中的列表数据
      sureNum: "",
      // 日期范围
      dataTime: [],
      onlySee: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      idsName: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 备案网站表格数据
      websiteList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        properWord: "",
        startTime: "",
        endTime: "",
        checkTaskId: "",
        createBy: "",
        userName: "",
      },
      userNameSelect: [],
      // 表单参数
      form: {
        // 正词||敏感词||错误词
        properWord: "",
        // 建议词
        suggestWord: "",
        // 敏感词专题||错误词专题
        checkTaskId: "",
        id: null,
      },
      // 表单校验
      rules: {
        properWord: [
          {
            required: true,
            message: "正词不能为空",
            trigger: "blur",
          },
          {
            max: 500,
            message: "正词长度不能超过500个字符",
            trigger: "blur",
          },
        ],
        suggestWord: [
          { required: true, message: "建议词不能为空", trigger: "blur" },
        ],
        checkTaskId: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
      },
      commitLoading:false,//提交按钮loading
    };
  },

  created() {
    this.getList();
    this.getTopicData();
    this.getUserNameSelect();
  },
  watch: {
    ids() {
      if (this.ids && this.ids.length) {
        this.showsureNum = true;
        this.sureNum = this.ids.length;
      } else {
        this.showsureNum = false;
      }
    },
    // "queryParams.endTime": {
    //   handler(newv, oldv) {
    //     if (newv < this.queryParams.startTime) {
    //       this.$message.error("请输入正确添加时间");
    //     }
    //   },
    // },
  },
  methods: {
    checkPermi,
    handleDateChange(){
      if(this.dataTime && this.dataTime.length>0){
        this.queryParams.startTime = this.dataTime[0]
        this.queryParams.endTime = this.dataTime[1]
      }else{
        this.queryParams.startTime = ''
        this.queryParams.endTime = ''
      }
    },
    // 获取用户名称数据
    async getUserNameSelect() {
      let res = await getUserDataApi();
      // debugger;
      this.userNameSelect = res.rows;
    },
    // 在method方法中写
    // 多层嵌套无法输入解决方法
    changeEvent(e) {
      this.$forceUpdate();
    },
    // 获取专题数据
    async getTopicData() {
      let params = {
        type: 3,
      };
      let res = await getSensitiveApi(params);
      this.tasksenDataOption = res.rows;
    },
    // 切换词类型
    changeType(val) {
      this.tabPosition == val;
      // 正词||敏感词||错误词名称
      this.queryParams.problemWord = "";
      //   敏感词||错误词分类专题
      this.queryParams.checkTaskId = "";
      this.dataTime=[]
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.queryParams.userName = "";
      if (this.tabPosition == 1) {
        // 正词
        this.rules.properWord = [
          {
            required: true,
            message: "正词不能为空",
            trigger: "blur",
          },
          {
            max: 500,
            message: "正词长度不能超过500个字符",
            trigger: "blur",
          },
        ];
      } else if (this.tabPosition == 2) {
        // 敏感词;
        this.rules.properWord = [
          {
            required: true,
            message: "敏感词不能为空",
            trigger: "blur",
          },
          {
            max: 500,
            message: "敏感词长度不能超过500个字符",
            trigger: "blur",
          },
        ];
        this.rules.checkTaskId = [
          {
            required: true,
            message: "专题不能为空",
            trigger: "blur",
          },
        ];
      } else {
        // 错误词
        this.rules.properWord = [
          {
            required: true,
            message: "错误词不能为空",
            trigger: "blur",
          },
          {
            max: 500,
            message: "错误词长度不能超过500个字符",
            trigger: "blur",
          },
        ];
        this.rules.checkTaskId = [
          {
            required: true,
            message: "专题不能为空",
            trigger: "blur",
          },
        ];
      }
      this.getList();
    },
    getIndex(index) {
      return (
        (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
      );
    },
    /** 查询添加表格列表 */
    getList() {
      this.loading = true;
      //   正词
      if (this.tabPosition == 1) {
        let params = {
          properWord: this.queryParams.problemWord,
          startTime: this.queryParams.startTime,
          endTime: this.queryParams.endTime,
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          createBy: this.queryParams.userName,
        };
        getTrueWordApi(params).then((response) => {
          this.websiteList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      } else if (this.tabPosition == 2) {
        // 敏感词
        let params = {
          problemWord: this.queryParams.problemWord,
          startTime: this.queryParams.startTime,
          endTime: this.queryParams.endTime,
          checkTaskId: this.queryParams.checkTaskId,
          problemType: 1,
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          createBy: this.queryParams.userName,
        };
        getsensitiveWordApi(params).then((response) => {
          this.websiteList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      } else if (this.tabPosition == 3) {
        // 错误词
        let params = {
          problemWord: this.queryParams.problemWord,
          startTime: this.queryParams.startTime,
          endTime: this.queryParams.endTime,
          checkTaskId: this.queryParams.checkTaskId,
          problemType: 2,
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          createBy: this.queryParams.userName,
        };
        getsensitiveWordApi(params).then((response) => {
          this.websiteList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        properWord: null,
        suggestWord: "",
        checkTaskId: "",
        userName: "",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (
        this.queryParams.startTime &&
        this.queryParams.endTime &&
        this.queryParams.startTime < this.queryParams.endTime
      ) {
        this.getList();
      } else if (!this.queryParams.startTime && !this.queryParams.endTime) {
        this.getList();
      } else {
        this.msgError("请输入正确时间范围");
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dataTime=[];
      // 正词||敏感词||错误词名称
      this.queryParams.problemWord = "";
      //   敏感词||错误词分类专题
      this.queryParams.checkTaskId = "";
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      this.queryParams.userName = "";
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      if (this.tabPosition == 1) {
        this.title = "新增正词";
        this.onlySee = false;
      } else if (this.tabPosition == 2) {
        this.title = "新增敏感词";
        this.onlySee = false;
      } else if (this.tabPosition == 3) {
        this.title = "新增错误词";
        this.onlySee = false;
      }
    },
    /** 修改按钮操作 */
    async handleUpdate(row, val) {
      if (val == 2) {
        // 查看
        this.onlySee = true;
        if (this.tabPosition == 1) {
          this.title = "查看正词";
          let res = await seeTrueWordApi(row.id);
          this.form = res.data;
        } else if (this.tabPosition == 2) {
          this.title = "查看敏感词";
          let res = await seesensitiveWordApi(row.id);
          this.form = res.data;
          this.form.properWord = res.data.problemWord;
        } else if (this.tabPosition == 3) {
          this.title = "查看错误词";
          let res = await seesensitiveWordApi(row.id);
          this.form = res.data;
          this.form.properWord = res.data.problemWord;
        }
      } else {
        // 修改
        this.onlySee = false;
        if (this.tabPosition == 1) {
          this.title = "修改正词";
          let res = await seeTrueWordApi(row.id);
          this.form = res.data;
        } else if (this.tabPosition == 2) {
          this.title = "修改敏感词";
          let res = await seesensitiveWordApi(row.id);
          this.form = res.data;
          this.form.properWord = res.data.problemWord;
        } else if (this.tabPosition == 3) {
          this.title = "修改错误词";
          let res = await seesensitiveWordApi(row.id);
          this.form = res.data;
          this.form.properWord = res.data.problemWord;
        }
      }
      this.open = true;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            if (this.tabPosition == 1) {
              // 正词;
              if (this.title == "查看正词") {
                this.open = false;
                this.form.properWord = "";
                this.form.id = "";
              } else if (this.title == "修改正词") {
                let params = {
                  properWord: this.form.properWord,
                  id: this.form.id,
                };
                if (this.form.properWord) {
                  this.commitLoading = true
                  updateTrueWord(params).then((res) => {
                    this.commitLoading = false
                    if (res.code == 200) {
                      this.msgSuccess("修改正词成功");
                      this.open = false;
                      this.getList();
                    } else {
                      this.msgError("修改正词失败");
                      this.open = true;
                    }
                  });
                } else {
                  this.msgError("请输入完整信息");
                }
              }
            } else if (this.tabPosition == 2) {
              if (this.title == "查看敏感词") {
                this.open = false;
                this.form.properWord = "";
                this.form.checkTaskId = "";
                this.form.id = "";
              } else if (this.title == "修改敏感词") {
                // 敏感词
                let params = {
                  problemType: 1,
                  id: this.form.id,
                  problemWord: this.form.properWord,
                  checkTaskId: this.form.checkTaskId,
                };
                if (this.form.properWord && this.form.checkTaskId) {
                  this.commitLoading = true
                  updatesensitiveWord(params).then((res) => {
                    this.commitLoading = false
                    if (res.code == 200) {
                      this.msgSuccess("修改敏感词成功");
                      this.open = false;
                      this.getList();
                    } else {
                      this.msgError(res.msg);
                      this.open = true;
                    }
                  });
                } else {
                  this.msgError("请输入完整信息");
                }
              }
            } else {
              if (this.title == "查看错误词") {
                this.open = false;
                this.form.properWord = "";
                this.form.suggestWord = "";
                this.form.checkTaskId = "";
                this.form.id = "";
              } else if (this.title == "修改错误词") {
                // 错误词
                let params = {
                  problemType: 2,
                  id: this.form.id,
                  problemWord: this.form.properWord,
                  checkTaskId: this.form.checkTaskId,
                  suggestWord: this.form.suggestWord,
                };
                if (
                  this.form.properWord &&
                  this.form.checkTaskId &&
                  this.form.suggestWord
                ) {
                  this.commitLoading = true
                  updatesensitiveWord(params).then((res) => {
                    this.commitLoading = false
                    if (res.code == 200) {
                      this.msgSuccess("修改错误词成功");
                      this.open = false;
                      this.getList();
                    } else {
                      this.msgError(res.msg);
                      this.open = true;
                    }
                  });
                } else {
                  this.msgError("请输入完整信息");
                }
              }
            }
          } else {
            if (this.tabPosition == 1) {
              // 正词;
              let params = {
                properWord: this.form.properWord,
              };
              if (this.form.properWord) {
                this.commitLoading = true
                addTrueWord(params).then((res) => {
                  this.commitLoading = false
                  if (res.code == 200) {
                    this.msgSuccess("新增正词成功");
                    this.open = false;
                    this.getList();
                  } else {
                    this.msgError(res.msg);
                    this.open = true;
                  }
                });
              } else {
                this.msgError("请输入完整信息");
              }
            } else if (this.tabPosition == 2) {
              // 敏感词
              let params = {
                problemType: 1,
                problemWord: this.form.properWord,
                checkTaskId: this.form.checkTaskId,
              };
              if (this.form.properWord && this.form.checkTaskId) {
                this.commitLoading = true
                addsensitiveWord(params).then((res) => {
                  this.commitLoading = false
                  if (res.code == 200) {
                    this.msgSuccess("新增敏感词成功");
                    this.open = false;
                    this.getList();
                  } else {
                    this.msgError(res.msg);
                    this.open = true;
                  }
                });
              } else {
                this.msgError("请输入完整信息");
              }
            } else {
              // 错误词
              let params = {
                problemType: 2,
                problemWord: this.form.properWord,
                checkTaskId: this.form.checkTaskId,
                suggestWord: this.form.suggestWord,
              };
              if (
                this.form.properWord &&
                this.form.checkTaskId &&
                this.form.suggestWord
              ) {
                this.commitLoading = true
                addsensitiveWord(params).then((res) => {
                  this.commitLoading = false
                  if (res.code == 200) {
                    this.msgSuccess("新增错误词成功");
                    this.open = false;
                    this.getList();
                  } else {
                    this.msgError(res.msg);
                    this.open = true;
                  }
                });
              } else {
                this.msgError("请输入完整信息");
              }
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    async handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm(
        `是否确认删除该${
          this.tabPosition == 1
            ? "正词"
            : this.tabPosition == 2
            ? "敏感词"
            : "错误词"
        }数据项?`,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(async () => {
        if (this.tabPosition == 1) {
          let res = await delTrueWord(ids);
          if (res.code == 200) {
            this.getList();
            this.msgSuccess("删除该正词成功");
          } else {
            this.msgError(res.msg);
          }
        } else if (this.tabPosition == 2) {
          let res = await delsensitiveWord(ids);
          if (res.code == 200) {
            this.getList();
            this.msgSuccess("删除该敏感词成功");
          } else {
            this.msgError(res.msg);
          }
        } else {
          let res = await delsensitiveWord(ids);
          if (res.code == 200) {
            this.getList();
            this.msgSuccess("删除该错误词成功");
          } else {
            this.msgError(res.msg);
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
// .formForm ::v-deep.el-form-item--medium .el-form-item__label {
//   text-align: left;
// }
.app-container {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: calc(100vh - 60px);
  .homeTopAutodict{
    background: #fff;
    padding: 20px 20px 0 20px;
  }
  .home {
    width: 100%;
    height: 100%;
    background: #fff;
    overflow: hidden;
    padding: 20px 20px 0px 20px;
    box-sizing: border-box;
  }
}
::v-deep .upload-demo {
  display: flex;
  width: 49%;
  margin: 0 auto;
  .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;
    border: solid 1px #ccc;
    height: 32px;
    line-height: 32px;
    margin-left: 6px;
    border-radius: 5px;
    width: 200px;
    overflow: hidden;
  }
  .el-upload-list__item:first-child {
    margin-top: 0;
    height: 32px;
    line-height: 32px;
  }
}
</style>
