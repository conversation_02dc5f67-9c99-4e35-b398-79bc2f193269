<template>
  <div class="home">
    <div class="homeHead">
      <h2><span>问题汇总</span></h2>
      <el-form :model="queryForm" :inline="true">
        <el-form-item label="信源名称：">
          <el-input size="small" placeholder="请输入信源名称" v-model.trim="queryForm.assignName"></el-input>
        </el-form-item>
        <el-form-item label="文章标题：">
          <el-input size="small" placeholder="请输入文章标题" v-model.trim="queryForm.title"></el-input>
        </el-form-item>
        <el-form-item label="问题词语：">
          <el-input size="small" placeholder="请输入问题词语" v-model.trim="queryForm.wrongWord"></el-input>
        </el-form-item>
        <el-form-item label="问题状态：" v-if="questionTab == '12'">
          <el-select v-model="queryForm.infoState" size="small" clearable>
            <el-option v-for="(item, index) in stateOptions" :key="index" :label="item.dictLabel"
              :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="问题分类：" v-show="false">
          <el-select v-model="queryForm.checkTaskTypeId" size="small" multiple="" collapse-tags clearable="">
            <el-option v-for="(item, index) in quesClassList" :key="index" :label="item.name"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getQuestion" size="small">查询</el-button>
          <el-button @click="cancelQuestion" size="small">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="history-operate" v-if="questionTab == '11'">
        <el-button type="primary" size="small" @click="exportExcel" v-loading="exportLoading">导出</el-button>
        <el-button type="primary" size="small" @click="currentdeleteMul" :disabled="ids.length == 0">批量过滤</el-button>
      </div>
      <div class="history-operate" v-if="questionTab == '12'">
        <!-- <el-button class="his-btn" type="primary" size="small" @click="multipleCancel"
          :disabled="ids.length == 0">批量处置</el-button> -->
        <el-button class="his-btn" type="primary" size="small" @click="deleteMul"
          :disabled="ids.length == 0">批量过滤</el-button>
        <el-button class="his-btn" type="primary" size="small" :disabled="ids.length == 0"
          @click="checkMul">批量查看</el-button>
        <el-button class="his-btn" type="primary" size="small" @click="exportExcel"
          v-loading="exportLoading">导出</el-button>
      </div>
      <!-- <el-row>
        <el-col :span="24" class="mb8">
          <el-button
            size="small"
            type="primary"
            @click="exportExcel"
            v-loading="exportLoading"
            >导出</el-button
          ></el-col
        >
      </el-row> -->
      <el-table :data="questionList" v-loading="loading" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" align="center" :index="getIndex" width="50"></el-table-column>
        <el-table-column label="信源名称" align="center" prop="siteName" width="200" :show-overflow-tooltip="true">
          <template slot-scope="scope" show-overflow-tooltip>
            <div class="site-name">
              <span class="overSpan">{{ scope.row.siteName }}</span>
              <!-- <span v-if="scope.row.disposeStatus == 1">
                <img src="@/assets/images/alreadyCancel.png" alt="" class="siteImg">
              </span> -->
              <span v-if="scope.row.readStatus == 1">
                <img src="@/assets/images/alreadyLook.png" alt="" class="siteImg">
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="文章标题" align="center" width="200" prop="title"
          :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="问题词语" align="center" prop="wrongWord" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <div class="wrong-word">
              <span>{{ scope.row.wrongWord }}</span> <i @click="handelAddTrueWord(scope.row.wrongWord)" class="el-icon-circle-plus"></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="问题分类" align="center" prop="checkTaskType" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="错误程度" align="center" width="80">
          <template slot-scope="scope">
            {{
              scope.row.wrongType == 3
              ? "严重错误"
              : scope.row.wrongType == 2
                ? "一般错误"
                : "—"
            }}
          </template>
        </el-table-column>
        <el-table-column label="建议词语" prop="suggestWord" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link v-if="checkSuggestion(scope.row.suggestWord)" class="suggest-word" :underline="false" style="font-weight: 400;" type="primary" @click="goBaidu(scope.row.suggestWord)">{{ scope.row.suggestWord }}</el-link>
            <span v-else>{{ scope.row.suggestWord }}</span>
          </template>
        </el-table-column>
        <el-table-column label="发文时间" prop="time" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="问题状态" prop="state" align="center" width="80">
          <template slot-scope="scope">
            {{ stateFormat(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column label="问题定位" align="center" width="80px">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="goDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="60px" v-if="checkPermi(['conentReview:indexDetail:filter'])">
          <template slot-scope="scope">
            <!-- <el-button
              type="text"
              size="small"
              @click="showDialog(scope.row.id)"
              >过滤</el-button
            > -->
            <el-row>
              <!-- <el-col :span="questionTab == '11' ? 24 : 12"> -->
                <el-button type="text" size="small" v-if="questionTab == '11'"
                  @click="showDialog(scope.row.id)">过滤</el-button>
                <el-button v-if="questionTab == '12'" type="text" size="small"
                  @click="deleteHis(scope.row.id)">过滤</el-button>
              <!-- </el-col> -->
              <!-- <el-col :span="questionTab == '12' ? 12 : 0">
                <el-button v-if="questionTab == '12'" type="text" size="small" @click="cancelHis(scope.row)">{{
                  scope.row.disposeStatus == 1 ? '处置详情' : '处置'
                }}</el-button>
              </el-col> -->
            </el-row>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="pageNum" :limit.sync="pageSize"
        @pagination="getQuestion" />
    </div>
    <!-- 过滤信息备注弹框 -->
    <el-dialog title="备注信息" :visible.sync="dialogFormVisible" width="30%">
      <el-form :model="form" :inline="true" class="formEl">
        <el-form-item :label="note" prop="remark" label-width="140px" style="width: 100%">
          <el-input v-model.trim="form.remark" type="textarea" style="width: 100%" placeholder="备注可不填"
            :disabled="disReamrk"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="sureRemark">确 定</el-button>
        <el-button @click="cancelsureRemark">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addTrueWord } from "@/api/system/autodict";
import { historyExportnew } from "@/api/index.js"
import { getQueDataApi, getQuestionApi, getHistoryDetail, cancelDetails, mulCancelBatch, cancelAdd, cancelEdit, currentdeleteMulApi } from "@/api/conentReview/indexDetail";
import { deletsFDialogApi } from "@/api/conentReview/themMangeChase";
import { checkPermi } from "@/utils/permission.js";
import { getBigTitleTwoApi } from "@/api/conentReview/themManTopic";
import { getBigTitleThreeApi } from "@/api/conentReview/themManTopic";
import { exportAllApi } from "@/api/conentReview/themMangeChase";
import {checkSuggestion} from "@/utils/index"
export default {
  name: "indexList",
  data () {
    return {
      checkSuggestion,
      questionTab: '',
      loading: false,
      serarData: {},
      queryForm: {
        wrongType: this.$route.query.wrongType,
        checkTaskTypeId: [],
        title: "",
        wrongWord: "",
      },
      errorList: [
        { name: "全选", value: "1,2,3" },
        { name: "严重错误", value: "3" },
        { name: "一般错误", value: "2" },
        { name: "自定义错误", value: "1" },
      ],
      getQueDataList: [],
      errWrongOne: "",
      errWrongTwo: "",
      errWrongThree: "",
      errWrongTotal: "",
      questionList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      dialogFormVisible: false,
      form: {
        id: "",
        remark: "",
      },
      quesClassList: [],
      ids: [],
      multiple: true,
      exportLoading: false,
      note: '备注：',
      disReamrk: false,
      stateOptions:[],
    };
  },
  created () {
    this.questionTab = this.$route.query.questionTab
    console.log('this.questionTab', this.questionTab);
    this.serarData = JSON.parse(sessionStorage.getItem("queryForm"));
    if (this.serarData.checkTaskTypeId.length) {
      this.queryForm.checkTaskTypeId = this.serarData.checkTaskTypeId
        .split(",")
        .map((i) => i.replace(/"/g, ""));
    } else {
      this.queryForm.checkTaskTypeId = [];
    }
    this.getQueData();
    this.getQuestion();
    // this.getquesClass();
    this.getDicts("info_state").then((response) => {
      this.stateOptions = response.data;
    });
  },
  methods: {
    goBaidu(word) {
      if (checkSuggestion(word)) {
        window.open(`https://www.baidu.com/s?wd=${word}`, '_blank')
      }
    },
    checkPermi,
    // 添加正词
    handelAddTrueWord(content) {
      this.$confirm(`确定将词“${content}”添加为正词吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => { 
        let words = content
        words = words.replace(/[；;]/g, ' ');  
        addTrueWord({ properWord: words }).then((res) => {
          if (res.code == 200) {
            this.msgSuccess("新增正词成功");
            this.getQuestion()
          } else {
            this.msgError(res.msg);
          }
        });
      })
    },
    //   导出
    exportExcel () {
      let params = {
        type: this.serarData.type,
        startTime: this.serarData.startTime,
        endTime: this.serarData.endTime,
        checkTaskId:
          this.$route.query.wrongType == 1 ? this.serarData.checkTaskId : null,
        assignIds: this.serarData.assignIds,
        timeRound: this.serarData.timeRound,
        checkTaskTypeId:
          this.$route.query.wrongType == 3
            ? this.serarData.checkTaskTypeId
            : this.$route.query.wrongType == 2
              ? this.serarData.checkTaskTypeId
              : null,
        assignName: this.queryForm.assignName,
        title: this.queryForm.title,
        wrongWord: this.queryForm.wrongWord,
        wrongType: this.$route.query.wrongType,
        ids: this.ids.join(),
        infoType: this.$route.query.infoType.split(','),
        infoState: this.queryForm.infoState,
      };

      this.$confirm(
        `您确定要导出${this.ids.length > 0 ? "当前选中" : "全部"}数据吗?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        }
      ).then(() => {
        this.exportLoading = true;
        // 11问题汇总，12历史汇总
        if (this.$route.query.questionTab == '11') {
          exportAllApi(params).then((res) => {
            if (res.code == 200) {
              this.exportLoading = false;
              this.download(res.msg);
            } else {
              this.exportLoading = false;
              this.msgError(res.msg);
            }
          })
        } else {
          historyExportnew(params).then((res) => {
            if (res.code == 200) {
              this.exportLoading = false;
              this.download(res.msg);
            } else {
              this.exportLoading = false;
              this.msgError(res.msg);
            }
          })
        }
      });
    },

    // 历史问题 - 批量处置
    multipleCancel () {
      this.dialogFormVisible = true;
      this.note = '批量备注处置意见：';
      this.disReamrk = false
    },
    // 当前问题-批量过滤
    currentdeleteMul () {
      this.$confirm("是否确认过滤信息?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = { infoIds: this.ids }
        currentdeleteMulApi(params).then((res) => {
          // if (res.code == 200) {
          this.$message.success("过滤成功");
          this.getQuestion();
          // }
        });
      });
    },
    // 历史问题- 批量过滤
    deleteMul () {
      this.$confirm("是否确认过滤信息?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = { infoIds: this.ids }
        deletsFDialogApi(params).then((res) => {
          // if (res.code == 200) {
          this.$message.success("过滤成功");
          this.getQuestion();
          // }
        });
      });
    },
    // 历史问题 - 批量查看
    checkMul () {
      let params = {
        checkTaskInfoIds: this.ids,
        operateType: 2,
        remark: this.form.remark,
      };
      this.$confirm("是否确认查看信息?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        mulCancelBatch(params).then(res => {
          console.log('mulCancelBatch', res)
          if (res.code == 200) {
            this.dialogFormVisible = false;
            this.$message.success("操作成功");
            this.getQuestion();
          } else {
            this.dialogFormVisible = false;
            this.msgError(res.msg);
          }
        })
      });
    },
    // 历史问题-单个过滤（删）
    deleteHis (id) {
      this.$confirm("是否确认过滤信息?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let params = {
          infoIds: [id],
        }
        deletsFDialogApi(params).then((res) => {
          // if (res.code == 200) {
          this.$message.success("过滤成功");
          this.getQuestion();
          // }
        });
      });
    },
    // 历史问题 -单个处置
    cancelHis (row) {
      console.log('cancelHis', row);
      this.dialogFormVisible = true;
      this.form.id = row.id;
      this.note = '备注处置意见：'
      if (row.disposeStatus == 1) {
        this.note = '修改备注处置意见：'
        cancelDetails(row.id).then(res => {
          if (res.code == 200) {
            this.form.id = row.id;
            this.form.remark = res.data.remark
            if (this.form.remark) {
              this.disReamrk = true
            } else {
              this.disReamrk = false
            }
          }
        })
      } else {
        this.disReamrk = false
      }
    },


    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    getIndex (index) {
      return (this.pageNum - 1) * this.pageSize + index + 1;
    },
    // 获取问题分类数据
    async getquesClass () {
      // wrongType == 3严重 2一般 1自定义
      this.quesClassList = [];
      if (
        this.$route.query.wrongType == 3 ||
        this.$route.query.wrongType == 2
      ) {
        let res = await getBigTitleTwoApi();
        this.quesClassList = res.data;
      } else if (this.$route.query.wrongType == 1) {
        let res = await getBigTitleThreeApi();
        this.quesClassList = res.data;
      }
    },
    //   取消
    cancelQuestion () {
      this.queryForm.title = "";
      this.queryForm.wrongType = "";
      this.queryForm.assignName = "";
      this.queryForm.wrongWord = "";
    },
    //   跳转到详情页
    async goDetail (item) {
      // 已查看
      let params = {
        checkTaskInfoIds: [item.id],
        operateType: 2,
      }
      // 历史问题
      if (this.questionTab == '12') {
        if (item.readStatus != 1) {
          let res = await mulCancelBatch(params)
          if (res.code == 200) {
            this.getQuestion()
          } else {
            this.msgError(res.msg)
          }
        }
      }

      const newPage = this.$router.resolve({
        path: "/conentReview/detailPage/index",
        query: {
          id: item.id,
          itemTime: item.time,
          itemSolrId: item.solrId,
          itemCheckTaskId: item.checkTaskId,
          questionTab: this.questionTab,
        },
      });
      window.open(newPage.href, "_blank");
    },
    // 过滤
    showDialog (id) {
      // this.$confirm("此操作将永久过滤掉数据, 是否继续?", "提示", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning",
      // }).then(() => {
      //   var formData = new FormData();
      //   formData.append("ids", id);
      //   deletsFDialogApi(formData).then((res) => {
      //     if (res.code == 200) {
      //       this.$message.success("过滤成功");
      //       this.getQuestion();
      //     }
      //   });
      // });
      this.dialogFormVisible = true;
      this.form.id = id;
      this.note = '备注：'
    },
    // 备注信息查看和修改
    sureRemark () {
      if (this.questionTab == '11') {
        let params = {
          infoIds: [this.form.id],
          remark: this.form.remark,
        };
        deletsFDialogApi(params).then(res => {
          // if (res.code == 200) {
          this.dialogFormVisible = false;
          this.$message.success("过滤成功");
          this.getQuestion();
          this.form.id = "";
          this.form.remark = "";
          // } else {
          //   this.dialogFormVisible = false;
          //   this.msgError(res.msg);
          //   this.form.id = "";
          //   this.form.remark = "";
          // }
        })
      } else if (this.questionTab == '12') {
        let params
        if (this.note == '备注处置意见：') {
          params = {
            checkTaskInfoId: this.form.id,
            operateType: 1,
            remark: this.form.remark,
          };
          cancelAdd(params).then(res => {
            // if (res.code == 200) {
            this.dialogFormVisible = false;
            this.$message.success("处置成功");
            this.getQuestion();
            this.form.id = "";
            this.form.remark = "";
            // this.queryForm.disposeStatus = null
            // } else {
            // this.dialogFormVisible = false;
            // this.msgError(res.msg);
            // this.form.id = "";
            // this.form.remark = "";
            // }
          })
        } else if (this.note == '修改备注处置意见：') {
          params = {
            checkTaskInfoId: this.form.id,
            remark: this.form.remark,
          };
          cancelEdit(params).then(res => {
            console.log('cancelEdit', res);
            if (res.code == 200) {
              this.dialogFormVisible = false;
              this.$message.success("操作成功");
              this.getQuestion();
              this.form.id = "";
              this.form.remark = "";
            }
          })

        }
        else if (this.note == '批量备注处置意见：') {
          params = {
            checkTaskInfoIds: this.ids,
            operateType: 1,
            remark: this.form.remark,
          };
          mulCancelBatch(params).then(res => {
            if (res.code == 200) {
              this.dialogFormVisible = false;
              this.$message.success("批量处置成功");
              this.getQuestion();
              this.form.id = "";
              this.form.remark = "";
            }
            else {
              this.dialogFormVisible = false;
              this.msgError(res.msg);
              this.form.id = "";
              this.form.remark = "";
            }
          })
        }
      }
    },
    // async sureRemark() {
    //   let params = {
    //     infoIds: [this.form.id],
    //     remark: this.form.remark,
    //   };
    //   let res = await deletsFDialogApi(params);
    //   if (res.code == 200) {
    //     this.dialogFormVisible = false;
    //     this.$message.success("过滤成功");
    //     this.getQuestion();
    //     this.form.id = "";
    //     this.form.remark = "";
    //   } else {
    //     this.dialogFormVisible = false;
    //     this.msgError(res.msg);
    //     this.form.id = "";
    //     this.form.remark = "";
    //   }
    // },
    cancelsureRemark () {
      this.form.id = "";
      this.form.remark = "";
      this.dialogFormVisible = false;
    },
    //   获取问题汇总详情概览
    async getQueData () {
      let params = {
        assignIds: this.$route.query.assId,
        // assignIds: this.serarData.assignIds,
        startTime: this.serarData.startTime,
        endTime: this.serarData.endTime,
        type: this.serarData.type,
        checkTaskId: this.serarData.checkTaskId,
        timeRound: this.serarData.timeRound,
        checkTaskTypeId: this.serarData.checkTaskTypeId,
        // wrongType: this.serarData.wrongType,
      };
      let res = await getQueDataApi(params);
      this.getQueDataList = res.data;
      for (let i = 0; i < this.getQueDataList.length; i++) {
        if (this.getQueDataList[i].wrong_type == 1) {
          //   疑似错误
          this.errWrongOne = this.getQueDataList[i].count;
        } else if (this.getQueDataList[i].wrong_type == 2) {
          //   一般错误
          this.errWrongTwo = this.getQueDataList[i].count;
        } else if (this.getQueDataList[i].wrong_type == 3) {
          //   严重错误
          this.errWrongThree = this.getQueDataList[i].count;
        }
      }
      this.errWrongTotal =
        parseInt(this.errWrongOne) +
        parseInt(this.errWrongTwo) +
        parseInt(this.errWrongThree);
    },
    // 算百分比
    GetPercent (num, total) {
      /// <summary>
      /// 求百分比
      /// </summary>
      /// <param name="num">当前数</param>
      /// <param name="total">总数</param>
      num = parseFloat(num);
      total = parseFloat(total);
      if (isNaN(num) || isNaN(total)) {
        return "-";
      }
      return total <= 0
        ? "0%"
        : Math.round((num / total) * 10000) / 100.0 + "%";
    },
    // 获取问题汇总详情列表
    async getQuestion () {
      this.loading = true;
      let params = {
        assignIds: this.serarData.assignIds,
        startTime: this.serarData.startTime,
        endTime: this.serarData.endTime,
        type: this.serarData.type,
        checkTaskId: this.$route.query.wrongType == 1 ? this.serarData.checkTaskId : null,
        timeRound: this.serarData.timeRound,
        wrongType: this.queryForm.wrongType,
        timeRound: this.serarData.timeRound,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        title: this.queryForm.title,
        checkTaskTypeId:
          this.$route.query.wrongType == 3
            ? this.serarData.checkTaskTypeId
            : this.$route.query.wrongType == 2
              ? this.serarData.checkTaskTypeId
              : null,
        wrongType: this.$route.query.wrongType,
        // 信源名称
        assignName: this.queryForm.assignName,
        wrongWord: this.queryForm.wrongWord,
        infoType: this.$route.query.infoType.split(','),
        infoState: this.queryForm.infoState,
      };
      // 11问题汇总，12历史汇总
      if (this.$route.query.questionTab == '11') {
        let res = await getQuestionApi(params);
        this.questionList = res.rows;
        this.total = res.total;
      } else {
        let res = await getHistoryDetail(params);
        this.questionList = res.rows;
        this.total = res.total;

      }

      if (this.questionList) {
        this.loading = false;
      }
      // this.total = res.total;
    },
    // 状态字典翻译
    stateFormat(row, column) {
          return this.selectDictLabel(this.stateOptions, row.state);
    },
  },
};
</script>
<style >
.el-message-box__header {
  border-bottom: solid 1px #ccc;
}
</style>
<style lang="scss" scoped>
.wrong-word{
  display: flex;
  align-items: center;
  span{
    width: calc(100% - 16px);
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .el-icon-circle-plus{
    width: 16px;
    cursor: pointer;
    color: #1890ff;
    font-size: 16px;
  }
}
.site-name{
  display: flex;
  align-items: center;
  .overSpan {
      width: calc(100% - 0px);
      overflow: hidden;
      text-overflow: ellipsis;
  }
  .siteImg {
    width: 36px;
    height: 28px;
    margin-bottom: -6px;
  }
}
.suggest-word{
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  ::v-deep .el-link--inner{
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}
.formEl ::v-deep.el-form-item--medium .el-form-item__content {
  width: 60%;
}

.history-operate {
  padding-bottom: 10px;

  .his-btn {
    width: 80px;
    height: 32px;
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #3D9FFE;
    font-size: 14px;
    color: #3D9FFE;
    margin: 0px;
    background-color: #fff;
    padding: 6px 12px;
    margin-right: 10px;
  }
}

.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;

  .homeHead {
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    margin-bottom: 20px;

    h2 {
      width: 100%;
      overflow: hidden;
      height: 60px;
      padding: 15px 20px;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      background: #fff;
      margin-bottom: 20px;
      font-weight: normal;
      font-size: 18px;
      margin: 0;
      padding: 0;

      span {
        display: inline-block;
        border-left: solid 3px #3d9ffe;
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
      }
    }

    .homeData {
      width: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin: 0;
      padding: 0;

      li {
        list-style: none;
        display: flex;
        flex-direction: row;
        width: 24%;
        height: 115px;

        div {
          flex: 1;

          span {
            display: block;
            height: 70px;
            line-height: 70px;
            text-align: center;
            font-size: 28px;

            em {
              font-style: normal;
              font-size: 14px;
            }
          }

          p {
            text-align: center;
            margin: 0;
            padding: 0;
          }
        }

        div.liDiff {
          color: #fff;
        }
      }

      li.liDivOne {
        background: url("../../../assets/indexDetail/liDivOne.png") no-repeat center center;
        background-size: 100% 100%;
        color: #fff;
      }

      li.liDivTwo {
        background: url("../../../assets/indexDetail/liDivTwo.png") no-repeat center center;
        background-size: 100% 100%;
      }

      li.liDivThree {
        background: url("../../../assets/indexDetail/liDivThree.png") no-repeat center center;
        background-size: 100% 100%;
      }

      li.liDivFour {
        background: url("../../../assets/indexDetail/liDivFour.png") no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }
}
</style>
