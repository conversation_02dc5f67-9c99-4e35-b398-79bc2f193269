<template>
  <div class="videoBox">
    <div class="videoOneFloor">
      <el-form label-width="120px">
        <el-form-item label="截图频率">
          <el-input-number
            v-model="screenFrequency"
            :min="1"
            :max="60"
            controls-position="right"
            size="mini"
          />
          &nbsp;秒/次&nbsp;
          <el-tooltip placement="top-start" :value="tooltipFlag">
            <div slot="content">
              支持设置区间为 1 - 60 秒/次;<br />
              1分钟及以下视频最低可设置 1 秒/次;<br />
              1-30分钟视频最低可设置 3 秒/次;<br />
              30分钟以上视频最低可设置 5 秒/次;
            </div>
            <img
              src="@/assets/images/question.png"
              alt=""
              style="width: 20px; position: relative; top: 4px"
            />
          </el-tooltip>
        </el-form-item>
      </el-form>
    </div>
    <div class="upVidDiv">
      <p>
        上传文件支持格式：flv,mkv,mp4,rmvb,avi,wmv,3gp,ts,mov,一次最多上传1个文件，单个文件不得大于2G
      </p>
      <el-upload
        class="upload-demo"
        action="#"
        :limit="1"
        multiple
        :auto-upload="false"
        accept=".flv,.mkv,.mp4,.rmvb,.avi,.wmv,.3gp,.ts,.mov"
        :on-change="handleChange"
        :on-remove="handleRemove"
        :on-exceed="handleExceed"
        :headers="headers"
        :file-list="fileList"
      >
        <el-button size="small" type="primary">选择文件</el-button>
      </el-upload>
    </div>
    <div class="checkDiv">
      <el-button
        :disabled="fileList.length == 0 || !screenFrequency"
        type="primary"
        @click="upVideo"
        :loading="loading"
        >开始检测</el-button
      >
      <el-button type="primary" @click="clearVideo">清空</el-button>
    </div>
    <div class="checkOut" v-show="showMe">
      <p>
        检测已提交，检测结果请前往
        <el-link type="primary" @click="goOut">检测列表</el-link> 查看
      </p>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { upVideoApi } from "@/api/textReview";
export default {
  data() {
    return {
      form: {},
      fileList: [],
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      showMe: false,
      loading: false,
      //  uploadFileUrl: process.env.VUE_APP_BASE_API + "/errorDataTaskQuartz/uploadVideo", // 上传的文件服务器地址
      screenFrequency: 5,
      tooltipFlag: false,
      fileFormatList: ['flv','mkv','mp4','rmvb','avi','wmv','3gp','ts','mov'],
    };
  },
  methods: {
    handleChange(file, fileList) {
      const { name } = file
      const extension = name.substring(name.lastIndexOf(".") + 1);
      if(!this.fileFormatList.includes(extension)){
        this.$msgbox({
          title: "警告",
          message: "视频格式只支持：flv,mkv,mp4,rmvb,avi,wmv,3gp,ts,mov，请重新上传。",
          type: "warning",
        });
        fileList.pop();
        return;
      }
      //获取上传文件大小
      let fileSize = Number(file.size / 1024 / 1024);
      if (fileSize > 2048) {
        this.$msgbox({
          title: "警告",
          message: "视频大小不能超过2G，请重新上传。",
          type: "warning",
        });
        fileList.pop(); //新增的图片在最后一个，所以只要删除那个最后新增的>10M的图片就可以了
        return;
      }
      this.fileList = fileList;
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
      console.log("filelist", this.fileList);
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `最多只支持一个文件，若要替换请先删除已上传的文件！`
      );
    },
    // 点击上传
    async upVideo() {
      if (this.fileList.length === 0) {
        this.$message.warning("请选取文件");
        return;
      }
      if (!this.screenFrequency) {
        this.$message.warning("请选择截图频率");
        return;
      }
      this.loading = true;
      this.tooltipFlag = false;
      const formData = new FormData();
      formData.append("frame", this.screenFrequency.toString() || "1");
      this.fileList.forEach((file) => {
        formData.append("file", file.raw);
      });
      let res = await upVideoApi(formData);
      if (res.code == 200) {
        this.showMe = true;
        this.loading = false;
      } else {
        this.showMe = false;
        this.$message.error(res.msg);
        this.tooltipFlag = true;
        this.loading = false;
      }
    },
    clearVideo() {
      this.fileList = [];
      this.showMe = false;
    },
    goOut() {
      this.$router.push({ path: "/linecheck/checklist" });
    },
  },
};
</script>

<style lang="scss" scoped>
.checkOut {
  width: 100%;
  overflow: hidden;
  background: #eafbe4;
  border: solid 1px #91e481;
  color: #80858b;
  padding: 0px 20px;

  .el-link {
    vertical-align: middle;
    height: 26px;
    font-size: 16px;
    display: inline-block;
  }
}

.videoBox {
  width: 100%;
  overflow: hidden;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;

  // .videoOneFloor {
  //   width: 100%;
  //   overflow: hidden;
  //   padding: 20px;
  //   box-sizing: border-box;
  // }

  .upVidDiv {
    width: 100%;
    overflow: hidden;
    background: #eeeff1;
    border-bottom: solid 1px #dae1e9;
    padding: 20px;

    p {
      color: #737780;
      padding: 0 10px;
    }
  }

  .checkDiv {
    width: 100%;
    overflow: hidden;
    text-align: center;
    margin: 20px 0;
  }
}
</style>
