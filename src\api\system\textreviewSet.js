import request from '@/utils/request'

// 查询备案网站列表
export function handleQueryApi (query) {
    return request({
        url: '/textCheckMember/list',
        method: 'get',
        params: query
    })
}
// 新增确定
export function addSetApi (data) {
    return request({
        url: '/textCheckMember',
        method: 'post',
        data: data
    })
}

// 删除
export function handleDeleteApi (ids) {
    return request({
        url:'/textCheckMember/'+ids,
        method: 'delete'
    })
}
// 修改
export function editSetApi (data) {
    return request({
        url: '/textCheckMember',
        method: 'put',
        data: data
    })
}
// 查看列表
export function handleUpdateApi (id) {
    return request({
        url: '/textCheckMember/' + id,
        method: 'get',
    })
}
