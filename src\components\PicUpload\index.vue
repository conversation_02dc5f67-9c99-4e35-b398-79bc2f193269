<template>
  <div class="uploadDiv">
    <el-upload ref="upload" action="#" list-type="picture-card" :headers="headers" :limit="100" multiple drag
      :show-file-list="true" :auto-upload="false" :accept="picAccept" :file-list="fileList"
      :on-preview="handlePictureCardPreview" :on-change="handleChange" :on-remove="handleRemove">
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <p v-show="fileList.length == 0">支持图片拖入，一次最多上传100张，图片支持格式：jpg,png,bmp,gif,webp,tiff,svg,单张图片 < 10M</p>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  props: {
    fileList: {
      type: Array,
    },
    // 图片类型
    picAccept: {
      type: String,
      default: ''
    },
  },
  data () {
    return {
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      dialogImageUrl: '',
      dialogVisible: false,
      fileFormatList: ['jpg','png','bmp','gif','webp','tiff','svg'],
    }
  },
  methods: {
    handleChange (file, fileList) {
      //获取上传文件大小
      if (file.size == 0) {
        this.$message.error('请输入正确图片')
        fileList.pop();
        return
      }
      const { name } = file
      const extension = name.substring(name.lastIndexOf(".") + 1);
      if(!this.fileFormatList.includes(extension)){
        this.$msgbox({
          title: "警告",
          message: "图片格式只支持：jpg,png,bmp,gif,webp,tiff,svg，请重新上传。",
          type: "warning",
        });
        fileList.pop();
        return;
      }
      let imgSize = Number(file.size / 1024 / 1024);
      if (imgSize > 10) {
        this.$msgbox({
          title: "警告",
          message: "图片大小不能超过10MB，请重新上传。",
          type: "warning",
        });
        fileList.pop();   //新增的图片在最后一个，所以只要删除那个最后新增的>10M的图片就可以了
        return;
      }
      this.$emit('update:fileList', fileList)
    },
    handleRemove (file, fileList) {
      this.$emit('update:fileList', fileList)
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
  }
}
</script>

<style lang="scss" scoped>
.uploadDiv {
  width: 100%;
  height: 100%;

  p {
    color: #737780;
  }

  ::v-deep .el-upload--picture-card {
    width: 228px;
    height: 208px;
  }

  ::v-deep .el-upload-list--picture-card .el-upload-list__item {
    width: 228px;
    height: 208px;

  }

  ::v-deep .el-upload--picture-card i {
    line-height: 7;
  }

  ::v-deep .el-upload-dragger {
    width: 226px;
    height: 206px;
  }
}
</style>
