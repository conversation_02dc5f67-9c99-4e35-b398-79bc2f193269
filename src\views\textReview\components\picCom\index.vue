<template>
  <div class="picBox">
    <div>
      <el-form ref="form" :model="form" label-width="120px">
        <el-form-item label="业务选择：">
          <el-select v-model="form.businessId" placeholder="请选择活动区域">
            <el-option
              v-for="item in busList"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="picDiv">
        <PicUpload :fileList.sync="fileList" :picAccept="picAccept"></PicUpload>
      </div>
      <div class="butCenter">
        <el-button
          @click="startcheck"
          type="primary"
          :disabled="fileList.length == 0"
          :loading="loading"
          >开始检测</el-button
        >
        <el-button @click="clearUpload" type="primary">清空</el-button>
      </div>
    </div>
    <div>
      <div class="picFloor">
        <ul>
          <li v-for="item in picList" :key="item.picUrl">
            <div v-if="item.status == 3">
              <p class="postionTopRed">检测失败</p>
              <ErrImg :errorData="item"></ErrImg>
            </div>

            <div v-else-if="item.status == 2 && item.suggestion != 0">
              <p
                :class="
                  item.suggestion == 1 ? 'postionTopTwo' : 'postionTopRed'
                "
              >
                {{ item.suggestion == 1 ? "嫌疑" : "不通过" }}
              </p>
              <ErrImg :errorData="item"></ErrImg>
              <div
                v-for="(intemin, indexin) in item.picLabel"
                :key="indexin"
                style="margin-bottom: 20px"
              >
                <el-row>
                  <el-col :span="12">
                    <p class="statusP">
                      <span>识别内容：</span>{{ intemin.value }}
                    </p>
                  </el-col>
                  <el-col :span="12">
                    <p class="statusP">
                      <span>类别：</span>
                      {{ intemin.labelName }}
                      {{
                        intemin.secondLabelName
                          ? "-" + intemin.secondLabelName
                          : ""
                      }}
                      {{
                        intemin.thirdLabelName
                          ? "-" + intemin.thirdLabelName
                          : ""
                      }}
                    </p>
                  </el-col>
                  <el-col :span="12">
                    <p class="statusP" style="color: red">
                      <span>命中级别：</span>{{ switchLevel(intemin.level) }}
                    </p>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div v-else-if="item.status == 2 && item.suggestion == 0">
              <p class="postionTop">通过</p>
              <ErrImg :errorData="item"></ErrImg>
              <p class="statusP" style="color: #1bc29b">
                <span>命中级别：</span>通过
              </p>
            </div>
          </li>
        </ul>
      </div>
      <div v-show="picList.length > 0" style="text-align: center">
        <el-button @click="checkAgain" type="primary">继续检测</el-button>
      </div>
    </div>
    <div></div>
  </div>
</template>

<script>
import PicUpload from "@/components/PicUpload";
import ErrImg from "@/views/components/ErrImg";
import { submitUploadApi, checkPicApi } from "@/api/textReview";
export default {
  components: { PicUpload, ErrImg },
  data() {
    return {
      cur: 1,
      fileList: [],
      picAccept: ".jpg,.png,.bmp,.gif,.webp,.tiff,.svg",
      picList: [],
      loading: false,
      form: {
        businessId: "",
      },
      busList: [],
    };
  },
  created() {
    this.getDicts("pic_check").then((response) => {
      this.busList = response.data;
      this.form.businessId = response.data[0].dictValue;
    });
  },
  methods: {
    switchLevel(level) {
      switch (level) {
        case 0:
          return "通过";
        case 1:
          return "疑似";
        case 2:
          return "不通过";
        default:
          return "通过";
      }
    },
    // 切换检测类型
    changeCur(item) {
      this.cur = item.value;
    },
    getFileList(val) {
      console.log("val", val);
    },
    // 开始检测
    async startcheck() {
      // 一：上传图片到服务器
      if (this.fileList.length === 0) {
        this.$message.warning("请选取文件");
        return;
      }
      this.loading = true;
      const formData = new FormData();
      this.fileList.forEach((file) => {
        formData.append("file", file.raw);
      });
      // let res = await submitUploadApi(formData)
      // // 二：开始检测
      // const paramsTwo = res.data
      // paramsTwo.map(item => {
      //   item.businessId = this.form.businessId
      // })

      formData.append("businessId", this.form.businessId);
      let reason = await checkPicApi(formData);
      this.picList = [];
      this.picList = reason.data;
      this.loading = false;
    },

    clearUpload() {
      this.fileList = [];
      this.picList = [];
    },
    // 继续检测
    checkAgain() {
      this.fileList = [];
      this.picList = [];
    },
  },
};
</script>

<style lang="scss" scope>
.picBox {
  width: 100%;
  overflow: hidden;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;

  .picType {
    width: 100%;
    overflow: hidden;

    ul {
      width: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: row;

      li {
        list-style: none;
        border: solid 1px #ccc;
        padding: 2px 8px;
        cursor: pointer;
      }

      li.active {
        color: #5489f6;
        border: solid 1px #5489f6;
      }
    }
  }

  .picDiv {
    width: 100%;
    overflow: hidden;
    padding: 20px;
    box-sizing: border-box;
  }

  .picFloor {
    width: 100%;
    overflow: hidden;
    padding: 20px;
    box-sizing: border-box;

    ul {
      width: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: row;
      margin: 0;
      padding: 0;
      flex-wrap: wrap;

      li {
        display: inline-block;
        // width: 500px;
        margin-right: 10px;
        overflow: hidden;
        position: relative;
        // height: 238px;
        margin-bottom: 10px;

        img {
          width: 100%;
          overflow: hidden;
          // height: 206px;
        }

        p {
          margin: 0;
          text-align: left;
          line-height: 24px;
          // height: 24px;
        }

        p.statusP {
          color: #2b3546;
          font-size: 14px;

          span {
            color: #797d85;
          }
        }

        p.postionTop {
          position: absolute;
          left: 0;
          top: 0;
          background: #1bc29b;
          display: inline-block;
          color: #fff;
          padding: 0 10px;
        }

        .postionTopTwo {
          position: absolute;
          left: 0;
          top: 0;
          background: #ffba00;
          display: inline-block;
          color: #fff;
          padding: 0 10px;
        }

        .postionTopRed {
          position: absolute;
          left: 0;
          top: 0;
          background: red;
          display: inline-block;
          color: #fff;
          padding: 0 10px;
        }
      }
    }
  }

  .moreUp {
    width: 100%;
    overflow: hidden;
    background-color: #fff;
    padding: 20px;
    box-sizing: border-box;

    p {
      margin: 0 0 20px 0;
    }

    .moreP {
      color: #84878f;
    }
  }

  .butCenter {
    width: 100%;
    overflow: hidden;
    text-align: center;
  }
}
</style>
