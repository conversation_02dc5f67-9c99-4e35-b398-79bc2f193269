<template>
  <div class="wechatMaxBox">
    <!-- 登录页绑定 -->
    <div class="wechatBox" v-show="isBind == 1">
      <div class="wechatHead">账户登录</div>
      <ul>
        <li>
          <span>
            <img :src="headImageUrl" alt="" />
            {{ nickname }}
          </span>
          <i class="el-icon-check" :style="{ color: '#67c23a' }"></i>
        </li>
      </ul>
      <div class="btnDiv">
        <div class="btnOne">
          <el-button type="success" @click="clickTarget">同意</el-button>
        </div>
        <div>
          <el-button @click="rejectLogin">拒绝</el-button>
        </div>
      </div>
    </div>
    <!-- 登录页未绑定 -->
    <div class="wechatBox" v-show="isBind == 0">
      <div class="wechatHead">暂无可用账户</div>
      <div>{{ infoData }}</div>
      <p>立即在网页端密码登录即可自动绑定此微信</p>
    </div>
    <!-- 个人中心绑定 -->
    <div class="wechatBox" v-show="isBind == 2">
      <div class="wechatHead">账户登录</div>
      <ul>
        <li>
          <span>
            <img :src="headImageUrl" alt="" />
            {{ nickname }}
          </span>
          <i class="el-icon-check" :style="{ color: '#67c23a' }"></i>
        </li>
      </ul>
      <div class="btnDiv">
        <div class="btnOne">
          <el-button type="success" @click="clickTargetTwo">同意</el-button>
        </div>
        <div>
          <el-button @click="rejectLoginTwo">拒绝</el-button>
        </div>
      </div>
    </div>
    <!-- 绑定过 -->
    <div class="wechatBox" v-show="isBind == 3">
      <div class="wechatHead">账户登录</div>
      <ul>
        <li>
          <span>
            <img :src="headImageUrl" alt="" />
            {{ nickname }}
          </span>
          <i class="el-icon-check" :style="{ color: '#67c23a' }"></i>
        </li>
      </ul>
      <div class="bindAagin">该微信已被绑定</div>
    </div>
  </div>
</template>

<script>
import { dealMessageApi, getUserInfoApi } from "@/api/wechatPhone";
import axios from "axios";

export default {
  data() {
    return {
      infoData: null,
      wechatuserName: "",
      wechatQRCodeId: "",
      showDiv: false,
      headImageUrl: "",
      nickname: "",
      alldata: "",
      baseUrlLocal: "",
    };
  },
  created() {
    this.getBaseUrl()
    this.dealMessage();
    this.getUserInfo();
  },
  methods: {
    getBaseUrl() {
      if (process.env.NODE_ENV == "production") {
        // 生产环境
        this.baseUrlLocal = "https://smarteye.boryou.com/prod-api";
      } else if (process.env.NODE_ENV == "development") {
        this.baseUrlLocal = "https://www.boryou.com/stage-api";
      } else {
        this.baseUrlLocal = "https://www.boryou.com/stage-api";
      }
    },
    //   处理二维码传递的信息
    async dealMessage() {
      let params = {
        code: this.$route.query.code,
        state: this.$route.query.state,
      };
      let uuu =
        this.baseUrlLocal +
        "/system/wechatQRCode/dealQRCode?code=" +
        params.code +
        "&state=" +
        params.state;
      axios.get(uuu).then((e) => {
        this.nickname = e.data.nickname;
        this.wechatQRCodeId = e.data.wechatQRCodeId;
        this.infoData = e.data.userWechat;
        this.isBind = e.data.isBind;
        this.headImageUrl = e.data.headImageUrl;
        this.wechatuserName = e.data.userWechat.userName;
      });
    },
    // 确定登录
    async clickTarget() {
      let params = {
        userName: this.wechatuserName,
        wechatQRCodeId: this.wechatQRCodeId,
      };

      let uuu =
        this.baseUrlLocal +
        "/system/wechatQRCode/sureLogin?userName=" +
        params.userName +
        "&wechatQRCodeId=" +
        params.wechatQRCodeId;
      axios
        .get(uuu)
        .then((res) => {
          if (res.data.code == 200) {
            this.$message({
              type: "info",
              message: "登录成功",
            });
            this.closeWindow();
          } else {
            this.$message({
              type: "info",
              message: "登录失败",
            });
          }
        })
        .catch((action) => {});
    },
    // 个人中心确定登录
    clickTargetTwo() {
      let uuu =
        this.baseUrlLocal +
        "/system/wechatQRCode/sureBind?wechatQRCodeId=" +
        this.wechatQRCodeId;
      axios
        .get(uuu)
        .then((res) => {
          if (res.data.code == 200) {
            this.$message({
              type: "info",
              message: "绑定成功",
            });
            this.closeWindow();
          } else {
            this.$message({
              type: "info",
              message: "绑定失败",
            });
          }
        })
        .catch((action) => {});
    },
    closeWindow() {
      try {
        WeixinJSBridge.call("closeWindow");
      } catch (e) {
        console.log(e);
      }
    },
    // 拒绝登录
    rejectLogin() {
      let params = {
        wechatQRCodeId: this.wechatQRCodeId,
      };
      let uuu =
        this.baseUrlLocal +
        "/system/wechatQRCode/rejectLogin?wechatQRCodeId=" +
        params.wechatQRCodeId;
      axios.get(uuu).then((res) => {
        this.closeWindow();
      });
    },
    // 个人中心拒绝登录
    rejectLoginTwo() {
      let params = {
        wechatQRCodeId: this.wechatQRCodeId,
      };
      let uuu =
        this.baseUrlLocal +
        "/system/wechatQRCode/rejectBind?wechatQRCodeId=" +
        params.wechatQRCodeId;
      axios.get(uuu).then((res) => {
        this.closeWindow();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.bindAagin{
  margin-top: 20px;
  font-size: 18px;
  color: red;
  text-align: center;
}
::v-deep .el-button {
  width: 40%;
  height: 50px;
}
.btnDiv {
  width: 100%;
  overflow: hidden;
  text-align: center;
  position: fixed;
  bottom: 0px;
  margin-bottom: 10%;
  margin-left: -40px;
  .btnOne {
    margin-bottom: 10px;
  }
}
.wechatMaxBox {
  width: 100%;
  overflow: hidden;
  position: relative;
  .wechatBox {
    width: 100%;
    overflow: hidden;
    padding: 10%;
    box-sizing: border-box;
    ul {
      margin: 0;
      padding: 0;
      li {
        width: 100%;
        list-style: none;
        height: 60px;
        line-height: 60px;
        border-bottom: solid 1px #ccc;
        cursor: pointer;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        span {
          display: inline-block;
          height: 60px;
          line-height: 60px;
          font-size: 18px;
          img {
            display: inline-block;
            vertical-align: middle;
            width: 50px;
            height: 50px;
            border-radius: 3px;
            margin-right: 6px;
          }
        }
        i {
          font-size: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-top: 20px;
        }
      }
    }
    .wechatHead {
      font-size: 24px;
      margin-bottom: 20px;
    }
    p {
      margin: 0;
      padding: 0;
      font-size: 20px;
      color: #666;
    }
  }
}
</style>