<template>
  <div v-if="!item.hidden">
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
        (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
        !item.alwaysShow
      "
    >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{ 'submenu-title-noDropdown': !isNest }"
        >
          <item
            :icon="onlyOneChild.meta.icon || (item.meta && item.meta.icon)"
            :title="onlyOneChild.meta.title"
          />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      popper-append-to-body
    >
      <template slot="title">
        <item
          v-if="item.meta"
          :icon="item.meta && item.meta.icon"
          :title="item.meta.title"
        />
      </template>
      <sidebar-item
        v-for="(child, index) in item.children"
        :key="index"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu top-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from "path";
import { isExternal } from "@/utils/validate";
import Item from "./Item";
import AppLink from "./Link";
import FixiOSBug from "./FixiOSBug";

export default {
  name: "SidebarItem",
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true,
    },
    isNest: {
      type: Boolean,
      default: false,
    },
    basePath: {
      type: String,
      default: "",
    },
  },
  data() {
    this.onlyOneChild = null;
    return {};
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      if (!children) {
        children = [];
      }
      const showingChildren = children.filter((item) => {
        if (item.hidden) {
          return false;
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item;
          return true;
        }
      });

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true;
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: "", noShowingChildren: true };
        return true;
      }

      return false;
    },
    resolvePath(routePath) {
      if (isExternal(routePath)) {
        return routePath;
      }
      if (isExternal(this.basePath)) {
        return this.basePath;
      }
      return path.resolve(this.basePath, routePath);
    },
  },
};
</script>

<style lang="scss">
.el-menu-item.is-active {
  // background-color: #3d9ffe !important;
  // border-bottom: 3px solid #3d9ffe;
  // color: #fff;
  span {
    // color: #fff !important;
  }
  .el-submenu__title {
    background-color: #3d9ffe !important;
    color: #fff;
  span {
    color: #fff;
  }
  i.el-icon-arrow-down:before {
    color: #fff;
  }
}
}
.el-menu-item.is-active.submenu-title-noDropdown{
  border-bottom: 2px solid #3d9ffe;
}




.el-menu-item:hover {
  background-color: #ecf5ff !important;
}
.el-submenu:hover .el-submenu__title{
    background-color: #ecf5ff !important;
}




.el-submenu.is-active .el-submenu__title {
  // background-color: #3d9ffe !important;
  border-bottom: 2px solid #3d9ffe;
  // color: #fff;
  span {
    // color: #fff;
  }
  i.el-icon-arrow-down:before {
    // color: #fff;
  }
}


.topbar-container .el-submenu__icon-arrow {
  right: 14px;
  top: 55%;
}
.el-submenu__title {
  padding: 0 30px 0 20px;
  border-bottom: 2px solid transparent;
  i{
    font-size: 14px;
    color: #000000;
  }
}
</style>
