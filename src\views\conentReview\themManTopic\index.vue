<template>
  <div class="home">
    <div class="homeForm">
      <el-form ref="form" :model="queryForm" label-width="82px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="媒体类型：">
              <el-radio-group v-model="queryForm.type">
                <el-radio
                  v-for="(item, index) in radioData"
                  :key="index"
                  :label="item.value"
                  :value="item.value"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="定向选择：">
              <el-button type="text" @click="chooseMed">
                {{ queryForm.type == 0 ? "备案网站" : "媒体账号" }}
              </el-button>
              <p
                style="
                  margin: 0;
                  padding: 0;
                  text-indent: 20px;
                  line-height: 20px;
                "
                v-if="queryForm.assignIds && queryForm.assignIds.length"
              >
                {{ queryForm.assignIds.length }}
              </p>
              <!-- <el-dialog
            :title="titleMed"
            :visible.sync="dialogVisible"
            width="40%"
          >
            <el-form
              :model="formMedSearch"
              :inline="true"
              @submit.native.prevent
            >
              <el-form-item
                :label="queryForm.type == 0 ? '网站名称' : '媒体账号'"
              >
                <el-input
                  v-model="formMedSearch.name"
                  :placeholder="
                    queryForm.type == 0 ? '请输入网站名称' : '请输入媒体账号'
                  "
                  size="small"
                  clearable=""
                  @keyup.enter.native="getWebData"
                  @clear="getWebData"
                ></el-input>
              </el-form-item>
              <el-button size="mini" type="primary" @click="getWebData"
                >确定</el-button
              >
            </el-form>
            <el-checkbox
              :indeterminate="isIndeterminate"
              v-model="checkAll"
              @change="handleCheckAllChange"
              >全选</el-checkbox
            >
            <div style="margin: 15px 0"></div>
            <el-checkbox-group
              v-model="queryForm.assignIds"
              @change="handleCheckedCitiesChange"
            >
              <el-checkbox
                v-for="item in typeData"
                :label="item.id"
                :key="item.id"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
            <span slot="footer" class="dialog-footer">
              <el-button @click="canceldialogVisible">取 消</el-button>
              <el-button type="primary" @click="suredialogVisible"
                >确 定</el-button
              >
            </span>
              </el-dialog>-->
              <!-- 备案网站||媒体账号 -->
              <div
                class="showCheckTypeOne"
                v-show="showMed"
                ref="treeWrapThree"
              >
                <!-- append-to-body -->
                <div
                  class="second-check"
                  @click="secondCheck"
                  :class="secondAct ? 'active' : ''"
                >
                  全选
                </div>
                <ul class="showTypeOne">
                  <li
                    v-for="(itema, index) in filterDataTree"
                    ref="dateTree"
                    @click="checkLiThrees(index, itema)"
                    :class="
                      itema.webSiteAndMediaAccountList.length == 0
                        ? itema.tag
                          ? 'active'
                          : ''
                        : itema.single.length > 0
                        ? 'active'
                        : ''
                    "
                    :key="itema.id"
                  >
                    <el-popover
                      placement="bottom-start"
                      width="400"
                      trigger="click"
                    >
                      <!-- 三级分类 -->
                      <div
                        class="third-kinds"
                        style="height: 300px; overflow-y: scroll"
                      >
                        <div>
                          <span
                            @click="checkAllThird(itema, index)"
                            :class="itema.checkAll ? 'active' : ''"
                            >全选</span
                          >
                        </div>
                        <div
                          v-for="(
                            itemb, indexb
                          ) in itema.webSiteAndMediaAccountList"
                          :key="indexb"
                          class="third-item"
                        >
                          <span
                            :class="itemb.tag ? 'active' : ''"
                            @click="checkSingle(itemb.id, index, indexb)"
                            >{{ itemb.name }}</span
                          >
                        </div>
                      </div>
                      <span
                        slot="reference"
                        v-if="itema.webSiteAndMediaAccountList.length > 0"
                      >
                        {{ itema.name }}
                        <i class="el-icon-arrow-down"></i>
                      </span>
                    </el-popover>
                    <span v-if="itema.webSiteAndMediaAccountList.length == 0">
                      {{ itema.name }}
                    </span>
                  </li>
                </ul>
                <!-- <div style="text-align: right">
                  <el-button
                    type="primary"
                    @click="closeShowReview"
                    size="small"
                    >确定</el-button
                  >
                  <el-button size="small" @click="cancelShowReview"
                    >取消</el-button
                  >
                </div>-->
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="时间范围：" style="margin-bottom: 20px">
          <ul class="timeUl">
            <li
              v-for="(item, index) in timelist"
              :key="index"
              @click="clickLi(index, item)"
              :class="curLi == index ? 'active' : ''"
            >
              {{ item.name }}
            </li>
            <div v-if="curLi == 5" style="line-height: 28px">
              <el-date-picker
                v-model="queryForm.startTime"
                size="small"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
               :disabled="probationPeriod==0?true:false"
                placeholder="请选择开始时间"
                :picker-options="
                  probationPeriod == 3&&isProbation==1
                    ? pickerOptionsThreeM
                    : probationPeriod == 6&&isProbation==1
                    ? pickerOptionsSixM
                    : probationPeriod == 12&&isProbation==1
                    ? pickerOptionsOneY
                    : probationPeriod == 24&&isProbation==1
                    ? pickerOptionsTwoY
                    : probationPeriod == 36&&isProbation==1
                    ? pickerOptionsThreeY
                    : probationPeriod == 48&&isProbation==1
                    ? pickerOptionsFourY
                    : probationPeriod == 60&&isProbation==1
                    ? pickerOptionsFiveY
                    : probationPeriod == 999&&isProbation==1
                    ? pickerOptionsMoreY
                    : ''
                "
              ></el-date-picker
              >-
              <el-date-picker
                v-model="queryForm.endTime"
                size="small"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择结束时间"
               :disabled="probationPeriod==0?true:false"
                :picker-options="
                  probationPeriod == 3&&isProbation==1
                    ? pickerOptionsThreeM
                    : probationPeriod == 6&&isProbation==1
                    ? pickerOptionsSixM
                    : probationPeriod == 12&&isProbation==1
                    ? pickerOptionsOneY
                    : probationPeriod == 24&&isProbation==1
                    ? pickerOptionsTwoY
                    : probationPeriod == 36&&isProbation==1
                    ? pickerOptionsThreeY
                    : probationPeriod == 48&&isProbation==1
                    ? pickerOptionsFourY
                    : probationPeriod == 60&&isProbation==1
                    ? pickerOptionsFiveY
                    : probationPeriod == 999&&isProbation==1
                    ? pickerOptionsMoreY
                    : ''
                "
              ></el-date-picker>
              <el-tooltip
                class="item"
                effect="light"
                placement="top-start"
                popper-class="tip-class"
              >
                <div slot="content" style="line-height: 24px; font-size: 12px">
                  试用账号{{
                    probationPeriod == 0&&isProbation==1
                      ? "无法查看当前数据":
                    probationPeriod == 3&&isProbation==1
                      ? "最多可查看三个月的数据"
                      : probationPeriod == 6&&isProbation==1
                      ? "最多可查看六个月的数据"
                      : probationPeriod == 12&&isProbation==1
                      ? "最多可查看一年的数据"
                      : probationPeriod == 24&&isProbation==1
                      ? "最多可查看两年的数据"
                      : probationPeriod == 36&&isProbation==1
                      ? "最多可查看三年的数据"
                      : probationPeriod == 48&&isProbation==1
                      ? "最多可查看四年的数据"
                      : probationPeriod == 60&&isProbation==1
                      ? "最多可查看五年的数据"
                      : probationPeriod == 999&&isProbation==1
                      ? "最多可查看全量的数据"
                      : ""
                  }}， <br />查看更长时间数据需购买正式账号
                </div>
                <span class="title-name">
                  <i
                    v-show="isProbation == 1"
                    class="el-icon-question icon-report"
                    style="color: #e6a23c; margin-left: 10px; cursor: pointer"
                  ></i>
                </span>
              </el-tooltip>
            </div>
          </ul>
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button type="primary" @click="getBigTitle" size="mini"
          >查询</el-button
        >
      </div>
    </div>
    <h2 class="diff" style="display: none">
      <ul class="tabUl">
        <li
          v-for="(item, index) in tabList"
          :key="item.id"
          :class="{ activeLi: tabPosition == index }"
          @click="clickTabList(index)"
        >
          {{ item.name }}
        </li>
      </ul>
    </h2>
    <div class="homeBody">
      <div
        class="manageList"
        :class="{ is_fixed: isFixed }"
        id="boxFixed"
        style="display: none"
      >
        <ul>
          <li
            v-for="(item, index) in manageListData"
            :key="index"
            :class="nowCur == index ? 'curLi' : ''"
            @click="changeCur(index, item.id)"
          >
            <div
              @mouseenter="(e) => isShowToltip(e, index)"
              @mouseout="hideTip(index)"
              v-if="!item.isShow"
              class="textDiv"
            >
              {{ item.name }}
            </div>
            <el-tooltip
              class="item"
              effect="dark"
              :content="item.name"
              placement="top"
              v-if="item.isShow"
            >
              <span>{{ item.name }}</span>
            </el-tooltip>
          </li>
        </ul>
      </div>
      <div class="homeRight">
        <div class="homeBox">
          <template v-if="getBigList.length > 0">
            <div class="bigTitle" v-show="tabPosition == 1">
              <h3 class="bigH3Three">
                (个人信息泄露)
                <span @click="goMore(-1)" style="color: #000">更多</span>
              </h3>
              <div v-loading>
                <div v-if="personInfo != undefined">
                  <ul v-if="personInfo.length > 0">
                    <li
                      v-for="(itemb, index) in personInfo"
                      @click="goDetailpage(itemb)"
                      :key="index"
                    >
                      <p>{{ itemb.title }}</p>
                      <span>{{ itemb.siteName }}</span>
                      <span>{{ itemb.time }}</span>
                    </li>
                  </ul>
                  <div v-else>
                    <div class="none-data">
                      <img src="@/assets/images/none.png" alt />
                      <p>暂无数据</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-for="(item, index) in getBigList"
              class="bigTitle"
              :key="index"
              style="display: none"
            >
              <h3
                :class="[
                  item.wrongType == 3
                    ? 'bigH3One'
                    : item.wrongType == 2
                    ? 'bigH3Two'
                    : 'bigH3Three',
                ]"
              >
                ({{ item.name }})
                <span @click="goMore(item.id)">更多</span>
              </h3>
              <div v-loading="item.load">
                <div v-if="item.list != undefined">
                  <ul v-if="item.list.length > 0">
                    <li
                      v-for="(itemb, index) in item.list"
                      @click="goDetailpage(itemb)"
                      :key="index"
                    >
                      <p>{{ itemb.title }}</p>
                      <span>{{ itemb.siteName }}</span>
                      <span>{{ itemb.time }}</span>
                    </li>
                  </ul>
                  <div v-else>
                    <div class="none-data">
                      <img src="@/assets/images/none.png" alt />
                      <p>暂无数据</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div class="box-none">
              <img src="@/assets/images/none.png" alt />
              <p>暂无数据</p>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getWebDataApi,
  getMedDataApi,
  getBigTitleApi,
  getBigTitleTwoApi,
  getBigTitleThreeApi,
  getLittleDataApi,
  getInfoDataApi,
  getManageListDataApi,
  getThemTitleApi,
} from "@/api/conentReview/themManTopic";
import { timeJson } from "@/utils/time.js";
import {
  // getMedDataApi,
  // 获取媒体树数据
  checkMedTree,
} from "@/api/index";
export default {
  name: "ManTopic",
  data() {
    return {
      pickerOptionsThreeM: {
        disabledDate(time) {
          const threeMonth = new Date().setMonth(new Date().getMonth() - 3);
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsSixM: {
        disabledDate(time) {
          const threeMonth = new Date().setMonth(new Date().getMonth() - 6);
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsOneY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 1
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsTwoY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 2
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsThreeY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 3
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsFourY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 4
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsFiveY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 5
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsMoreY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 83
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      // 试用时间范围(1为三个月，2为六个月，3为一年)
      probationPeriod: "",
      tabPosition: 1,
      isFixed: false,
      offsetTop: 0,
      nowCur: 0,
      manageListData: [],
      personInfo: [],
      curLi: 1,
      dialogVisible: false,
      getBigList: [],
      titleMed: "选择备案网站",
      queryForm: {
        type: "0",
        assignIds: [],
        // tiemOne: timeJson.days,
        startTime: "",
        endTime: "",
        wrongTypes: "-1",
      },
      // timeOne: [],
      timelist: [
        { name: "今天", id: 0, value: timeJson.today },
        { name: "24小时", id: 1, value: timeJson.days },
        { name: "三天", id: 2, value: timeJson.threedays },
        { name: "七天", id: 3, value: timeJson.weeks },
        { name: "三十天", id: 4, value: timeJson.threeTendays },
        { name: "自定义", value: [] },
      ],
      checkAll: false,
      isIndeterminate: false,
      getWebList: [],
      getMedList: [],
      typeData: [],
      radioData: [
        { name: "备案网站", value: "0" },
        { name: "媒体账号", value: "1" },
      ],
      pageNum: 1,
      pageSize: 10,
      getLittleDataList: [],
      getLittleArr: [],
      formMedSearch: {},
      themId: "",
      secondAct: false,
      filterDataTree: [],
      showMed: false,
      checkTaskIds: [],
      isProbation: "",
      tabList: [
        { name: "系统", id: 1 },
        { name: "自定义", id: 2 },
      ],
    };
  },
  created() {
    this.getWebData();
    this.getMedData();
    // this.getBigTitle()
    this.getManageListData();
    this.handleClick();
    this.getInfoData();
    // this.changeCur()
    this.getTime();
    this.getTreeData();
  },
  watch: {
    "queryForm.type": {
      handler(val) {
        if (val == 0) {
          this.getTreeData();
          this.$nextTick((_) => {
            this.typeData = this.getWebList;
            this.checkTaskIds = [];
            this.queryForm.assignIds = [];
            this.queryForm.assignIds.length = "";
            this.secondAct = false;
          });
        } else if (val == 1) {
          this.getTreeData();
          this.$nextTick(() => {
            this.typeData = this.getMedList;
            this.checkTaskIds = [];
            this.queryForm.assignIds = [];
            this.queryForm.assignIds.length = "";
            this.secondAct = false;
          });
        }
      },
    },
    // "queryForm.endTime": {
    //   handler(newv, oldv) {
    //     if (newv < this.queryForm.startTime) {
    //       this.$message.error("请输入正确时间范围");
    //     }
    //   },
    // },
    curLi: {
      handler(val) {
        if (val < 5) {
          for (let i = 0; i < this.timelist.length; i++) {
            if (val == this.timelist[i].id) {
              this.queryForm.startTime = this.timelist[i].value[0];
              this.queryForm.endTime = this.timelist[i].value[1];
            }
          }
        } else {
          this.queryForm.startTime = "";
          this.queryForm.endTime = "";
        }
      },
      immediate: true,
    },
  },
  mounted() {
    // 全局点击事件
    document.addEventListener("mouseup", (e) => {
      let treeweb = this.$refs.treeWrapThree;

      if (treeweb) {
        // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
        if (!treeweb.contains(e.target)) {
          this.showMed = false;
        }
      }
    });

    window.addEventListener("scroll", this.initHeight);
    this.$nextTick(() => {
      //获取对象相对于版面或由 offsetTop 属性指定的父坐标的计算顶端位置
      this.offsetTop = document.querySelector("#boxFixed").offsetTop;
    });
  },
  methods: {
    clickTabList(index) {
      if (
        (this.curLi == 5 && !this.queryForm.startTime) ||
        (this.curLi == 5 && !this.queryForm.endTime) ||
        (this.curLi == 5 &&
          !this.queryForm.startTime &&
          !this.queryForm.endTime) ||
        (this.curLi == 5 && this.queryForm.startTime > this.queryForm.endTime)
      ) {
        this.$message.error("请选择自定义时间");
      } else {
        this.tabPosition = index;
        if (this.tabPosition == 0) {
          // 系统
          this.queryForm.wrongTypes = "2,3";
          // this.getBigTitle();
          let paramsTwo = {
            wrongTypes: this.queryForm.wrongTypes,
            checkTaskTypeIds: this.themId,
            assignIds: this.queryForm.assignIds.join(),
          };
          getBigTitleTwoApi(paramsTwo).then((res) => {
            this.getBigList = [];
            this.getBigList = res.data;
            this.getLittleArr = [];
            this.getBigList.map((itema) => {
              itema.load = true;
              let params = {
                checkTaskTypeId: itema.id,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                startTime: this.queryForm.startTime,
                endTime: this.queryForm.endTime,
                type: this.queryForm.type,
                assignIds: this.queryForm.assignIds.join(),
              };
              getLittleDataApi(params).then((resd) => {
                let ddd = resd.rows;
                this.getLittleArr.push({
                  id: params.checkTaskTypeId,
                  list: ddd,
                });
                this.getLittleArr.map((itema) => {
                  this.getBigList.map((item) => {
                    if (item.id == itema.id) {
                      this.$set(item, "list", itema.list);
                      item.load = false;
                    }
                  });
                });
              });
            });
          });
        } else if (this.tabPosition == 1) {
          // 自定义
          this.queryForm.wrongTypes = "1";
          // this.getBigTitle();
          let paramsTwo = {
            wrongTypes: this.queryForm.wrongTypes,
            checkTaskTypeIds: this.themId,
            assignIds: this.queryForm.assignIds.join(),
          };
          getBigTitleThreeApi(paramsTwo).then((res) => {
            this.getBigList = [];
            this.getBigList = res.data;
            this.getLittleArr = [];
            this.getBigList.map((itema) => {
              itema.load = true;
              let params = {
                checkTaskId: itema.id,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                startTime: this.queryForm.startTime,
                endTime: this.queryForm.endTime,
                type: this.queryForm.type,
                assignIds: this.queryForm.assignIds.join(),
              };
              getLittleDataApi(params).then((resd) => {
                let ddd = resd.rows;
                this.getLittleArr.push({ id: params.checkTaskId, list: ddd });
                this.getLittleArr.map((itema) => {
                  this.getBigList.map((item) => {
                    if (item.id == itema.id) {
                      this.$set(item, "list", itema.list);
                      item.load = false;
                    }
                  });
                });
              });
            });
          });
        }
      }
    },
    // 获取审校专题二级数据
    async getTreeData() {
      let params = {
        type: this.queryForm.type,
      };
      let resTree = await checkMedTree(params);
      this.filterDataTree = [];
      this.$nextTick(() => {
        this.filterDataTree = resTree.data;
        // debugger;
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          item.single = [];
          if (
            item.webSiteAndMediaAccountList &&
            item.webSiteAndMediaAccountList.length != 0
          ) {
            item.webSiteAndMediaAccountList.map((itemb) => {
              itemb.tag = false;
            });
          }
        });
      });
    },
    checkLiThrees(index, item) {
      this.filterDataTree[index].tag = !this.filterDataTree[index].tag;
      if (item.webSiteAndMediaAccountList.length == 0) {
        if (this.filterDataTree[index].tag) {
          // this.checkTaskIds.push(item.id);
          // this.queryForm.assignIds = this.checkTaskIds;
          // this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        } else {
          if (this.checkTaskIds.indexOf(item.id) != -1) {
            this.checkTaskIds.splice(this.checkTaskIds.indexOf(item.id), 1);
            this.queryForm.assignIds = this.checkTaskIds;
            // this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          }
        }
      }
      this.$forceUpdate();
    },
    // 全选三级
    checkAllThird(item, index) {
      this.showMed = true;
      this.filterDataTree[index].checkAll =
        !this.filterDataTree[index].checkAll;
      let id = this.filterDataTree[index].webSiteAndMediaAccountList.map(
        (item) => {
          return item.id;
        }
      );
      if (this.filterDataTree[index].checkAll) {
        // 全选
        this.checkTaskIds.push(...id);
        this.checkTaskIds = [...new Set(this.checkTaskIds)];
        this.queryForm.assignIds = this.checkTaskIds;
        // this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        this.filterDataTree[index].single = id;
        this.filterDataTree[index].webSiteAndMediaAccountList.map(
          (item) => (item.tag = true)
        );
      } else {
        // 全不选
        this.filterDataTree[index].webSiteAndMediaAccountList.map(
          (item) => (item.tag = false)
        );
        this.filterDataTree[index].single = [];
        id.map((item) => {
          if (this.checkTaskIds.indexOf(item) != -1) {
            this.checkTaskIds.splice(this.checkTaskIds.indexOf(item), 1);
            // this.queryForm.assignIds = this.checkTaskIds;
            // this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          }
          this.queryForm.assignIds = this.checkTaskIds;
        });
      }
      this.$forceUpdate();
    },
    // 三级单选
    checkSingle(item, index, indexb) {
      this.showMed = true;
      this.filterDataTree[index].webSiteAndMediaAccountList[indexb].tag =
        !this.filterDataTree[index].webSiteAndMediaAccountList[indexb].tag;
      if (this.filterDataTree[index].webSiteAndMediaAccountList[indexb].tag) {
        this.filterDataTree[index].single.push(item);
        this.checkTaskIds.push(item);
        this.queryForm.assignIds = this.checkTaskIds;
      } else {
        if (this.checkTaskIds.indexOf(item) != -1) {
          this.checkTaskIds.splice(this.checkTaskIds.indexOf(item), 1);
          this.queryForm.assignIds = this.checkTaskIds;
        }
        if (this.filterDataTree[index].single.indexOf(item) != -1) {
          this.filterDataTree[index].single.splice(
            this.filterDataTree[index].single.indexOf(item),
            1
          );
        }
      }
      if (
        this.filterDataTree[index].single.length ==
        this.filterDataTree[index].webSiteAndMediaAccountList.length
      ) {
        this.filterDataTree[index].checkAll = true;
      } else {
        this.filterDataTree[index].checkAll = false;
      }
      this.$forceUpdate();
    },
    // 二级全选
    secondCheck() {
      this.secondAct = !this.secondAct;
      if (this.secondAct) {
        this.checkTaskIds = [];
        this.filterDataTree.map((item) => {
          item.tag = true;
          item.checkAll = true;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = true));
            let id = item.webSiteAndMediaAccountList.map((itemb) => itemb.id);
            item.single = id;
            this.checkTaskIds.push(...id);
            this.queryForm.assignIds = this.checkTaskIds;
          }
          // else {
          // this.checkTaskIds.push(item.id);
          // this.queryForm.assignIds = this.checkTaskIds;
          // this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          // }
        });
        this.$forceUpdate();
      } else {
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
            item.single = [];
          }
          this.checkTaskIds = [];
          this.queryForm.assignIds = [];
        });
        this.$forceUpdate();
      }
    },
    getTime() {
      this.$store.dispatch("GetInfo").then((res) => {
        // 试用时间范围
        this.probationPeriod = res.user.probationPeriod;
        this.isProbation = res.user.isProbation;
      });
    },
    // 弃用
    // changeTab(val) {
    //   this.tabPosition = val;
    //   if (this.tabPosition == 1) {
    //     // 系统
    //     this.queryForm.wrongTypes = "2,3";
    //     this.getBigTitle();
    //   } else if (this.tabPosition == 2) {
    //     // 自定义
    //     this.queryForm.wrongTypes = "1";
    //     this.getBigTitle();
    //   }
    // },
    initHeight() {
      // 设置或获取位于对象最顶端和窗口中可见内容的最顶端之间的距离 (被卷曲的高度)
      var scrollTop =
        window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop;
      //如果被卷曲的高度大于吸顶元素到顶端位置 的距离
      this.isFixed = scrollTop > this.offsetTop ? true : false;
    },

    // changeTime(val) {
    //   this.queryForm.tiemOne = val;
    // },
    // 时间选择
    clickLi(index, item) {
      this.curLi = index;
      // // if (index == 0 || index == 1 || index == 2 || index == 3 || index == 4) {
      // //   this.queryForm.startTime = "";
      // //   this.queryForm.endTime = "";
      // // }

      // // this.queryForm.tiemOne = item.value;
      // console.log("123", item.value);
      // if (index == 0 || index == 1 || index == 2 || index == 3 || index == 4) {
      //   this.queryForm.startTime = item.value[0];
      //   this.queryForm.endTime = item.value[1];
      // }
    },
    //   点击切换类型
    handleClick() {
      this.getBigTitle();
    },
    chooseMed() {
      this.showMed = true;
      // if (this.queryForm.type == 0) {
      //   this.titleMed = "选择备案网站";
      // } else {
      //   this.titleMed = "选择媒体账号";
      // }
    },
    suredialogVisible() {
      this.dialogVisible = false;
    },
    canceldialogVisible() {
      this.queryForm.assignIds = [];
      // this.typeData=[]
      this.formMedSearch.name = "";
      this.checkAll = false;
      this.dialogVisible = false;
    },
    //   更多
    goMore(id) {
      this.$router.push({
        path: "/conentReview/themMangeChase",
        query: {
          id: id,
          startTime: this.queryForm.startTime,
          endTime: this.queryForm.endTime,
          type: this.queryForm.type,
          assignIds: this.queryForm.assignIds.join(),
          tabPosition: this.tabPosition,
        },
      });
    },
    //   去详情页
    goDetailpage(item) {
      this.$router.push({
        path: "/conentReview/detailPage/index",
        query: {
          id: item.id,
          itemTime: item.time,
          itemSolrId: item.solrId,
          itemCheckTaskId: item.checkTaskId,
        },
      });
    },
    // 获取所有要展示的大标题
    getBigTitle() {
      if (
        (this.curLi == 5 && !this.queryForm.startTime) ||
        (this.curLi == 5 && !this.queryForm.endTime) ||
        (this.curLi == 5 &&
          !this.queryForm.startTime &&
          !this.queryForm.endTime) ||
        (this.curLi == 5 && this.queryForm.startTime > this.queryForm.endTime)
      ) {
        this.$message.error("请选择自定义时间");
      } else {
        this.getInfoData();
        if (this.tabPosition == 0) {
          // 系统
          let paramsTwo = {
            wrongTypes: this.queryForm.wrongTypes,
            checkTaskTypeIds: this.themId,
            assignIds: this.queryForm.assignIds.join(),
          };
          getBigTitleTwoApi(paramsTwo).then((res) => {
            this.getBigList = [];
            this.getBigList = res.data;
            this.getLittleArr = [];
            this.getBigList.map((itema) => {
              itema.load = true;
              let params = {
                checkTaskTypeId: itema.id,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                startTime: this.queryForm.startTime,
                endTime: this.queryForm.endTime,
                type: this.queryForm.type,
                assignIds: this.queryForm.assignIds.join(),
              };
              getLittleDataApi(params).then((resd) => {
                let ddd = resd.rows;
                this.getLittleArr.push({
                  id: params.checkTaskTypeId,
                  list: ddd,
                });
                this.getLittleArr.map((itema) => {
                  this.getBigList.map((item) => {
                    if (item.id == itema.id) {
                      this.$set(item, "list", itema.list);
                      item.load = false;
                    }
                  });
                });
              });
            });
          });
        } else if (this.tabPosition == 1) {
          // 自定义
          let paramsTwo = {
            wrongTypes: this.queryForm.wrongTypes,
            checkTaskTypeIds: this.themId,
            assignIds: this.queryForm.assignIds.join(),
          };
          getBigTitleThreeApi(paramsTwo).then((res) => {
            this.getBigList = [];
            this.getBigList = res.data;
            this.getLittleArr = [];
            this.getBigList.map((itema) => {
              itema.load = true;
              let params = {
                checkTaskId: itema.id,
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                startTime: this.queryForm.startTime,
                endTime: this.queryForm.endTime,
                type: this.queryForm.type,
                assignIds: this.queryForm.assignIds.join(),
              };
              getLittleDataApi(params).then((resd) => {
                let ddd = resd.rows;
                this.getLittleArr.push({ id: params.checkTaskId, list: ddd });
                this.getLittleArr.map((itema) => {
                  this.getBigList.map((item) => {
                    if (item.id == itema.id) {
                      this.$set(item, "list", itema.list);
                      item.load = false;
                    }
                  });
                });
              });
            });
          });
        }
      }
    },
    // 获取个人信息泄漏模块数据
    async getInfoData() {
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        startTime: this.queryForm.startTime,
        endTime: this.queryForm.endTime,
        type: this.queryForm.type,
        assignIds: this.queryForm.assignIds.join(),
      };
      let res = await getInfoDataApi(params);
      this.personInfo = res.rows;
    },

    handleCheckAllChange(val) {
      if (val == true) {
        let aaa = [];
        for (let i = 0; i < this.typeData.length; i++) {
          aaa.push(this.typeData[i].id);
        }
        this.queryForm.assignIds = aaa;
        this.isIndeterminate = false;
      } else {
        this.queryForm.assignIds = [];
        this.isIndeterminate = false;
      }
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.typeData.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.typeData.length;
      this.queryForm.assignIds = value;
    },
    // 获取数据网站 新媒体数据
    async getWebData() {
      let params = {
        pageNum: 1,
        pageSize: 50,
        name: this.formMedSearch.name,
      };
      let res = await getWebDataApi(params);
      this.getWebList = res.data;
      this.typeData = this.getWebList;
    },
    // 获取新媒体数据
    async getMedData() {
      let params = {
        pageNum: 1,
        pageSize: 50,
      };
      let res = await getMedDataApi(params);
      this.getMedList = res.data;
    },
    // 获取查询专题管理列表页数据
    async getManageListData() {
      let res = await getManageListDataApi();
      this.manageListData = res.rows;
      let aaa = {
        name: "全部",
        id: "",
      };
      this.manageListData.unshift(aaa);
      this.manageListData.forEach((item, index) => {
        Object.assign(item, { isShow: false });
      });
    },
    isShowToltip(e, index) {
      const bool = this.textRange(e.target);
      this.manageListData[index].isShow = bool;
    },
    hideTip(index) {
      this.manageListData[index].isShow = false;
    },
    // el dom元素
    textRange(el) {
      const textContent = el;
      const targetW = textContent.getBoundingClientRect().width;
      const range = document.createRange();
      range.setStart(textContent, 0);
      range.setEnd(textContent, textContent.childNodes.length);
      const rangeWidth = range.getBoundingClientRect().width;
      return rangeWidth > targetW;
    },

    changeCur(index, id) {
      this.nowCur = index;
      this.themId = id;
      this.getBigTitle();
    },
  },
  destroyed() {
    window.removeEventListener("scroll", this.handleScroll);
  },
};
</script>

<style scoped lang="scss">
.tabUl {
  width: 100%;
  overflow: hidden;
  padding: 0;
  margin: 0;
  li {
    display: flex;
    flex-direction: row;
    display: inline-block;
    justify-content: flex-start;
    padding: 10px 20px;
    font-size: 14px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-sizing: border-box;
    line-height: 18px;
    cursor: pointer;
  }
  li:nth-child(1) {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  li:nth-child(2) {
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
  .activeLi {
    background: #1890ff;
    color: #fff;
    border: solid 1px #1890ff;
  }
}
.showCheckTypeOne {
  max-height: 400px;
  overflow-y: auto;
  width: 500px;
  position: absolute;
  background: #fff;
  border: solid 1px #ccc;
  z-index: 10;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 5px;
  margin-top: -20px;
  .second-check {
    display: inline-block;
    padding: 2px 10px;
    border: 1px solid #f7f7f7;
    margin-bottom: 20px;
    line-height: 24px;
    cursor: pointer;
    font-size: 12px;
    height: 28px;
    &.active {
      color: #3d9ffe;
      border: 1px solid #3d9ffe;
    }
  }
  ul.showTypeOne {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    li {
      list-style: none;
      margin-right: 10px;
      padding: 0 6px;
      border-radius: 3px;
      height: 26px;
      line-height: 26px;
      border: solid 1px #ccc;
      cursor: pointer;
      margin-bottom: 10px;
      font-size: 12px;
    }
    li.active {
      border-color: #3d9ffe;
      color: #3d9ffe;
    }
  }
}
ul.showTypeOne {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: wrap;
  li {
    list-style: none;
    margin-right: 10px;
    padding: 0 6px;
    border-radius: 3px;
    height: 26px;
    line-height: 26px;
    border: solid 1px #ccc;
    cursor: pointer;
    margin-bottom: 10px;
    font-size: 12px;
  }
  li.active {
    border-color: #3d9ffe;
    color: #3d9ffe;
  }
}
.third-kinds {
  span {
    display: inline-block;
    padding: 2px 10px;
    border: 1px solid #f7f7f7;
    margin-bottom: 10px;
    margin-right: 5px;
    cursor: pointer;
    &.active {
      border: 1px solid #3d9ffe;
    }
  }
  .third-item {
    display: inline-block;
  }
}
.homeRight {
  display: inline-block;
  float: right;
  width: 100%;
  overflow: hidden;
}
.is_fixed {
  position: fixed;
  left: 5%;
  top: 6rem;
  width: 11% !important;
}
.manageList {
  display: inline-block;
  width: 12%;
  ul {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow: hidden;
    li {
      list-style: none;
      height: 50px;
      line-height: 50px;
      text-align: center;
      background: #fff;
      padding: 0px 20px;
      box-sizing: border-box;
      cursor: pointer;
      .textDiv {
        display: block;
        width: 100%;
        height: 50px;
        line-height: 50px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        border-bottom: solid 1px #efefef;
      }
      span {
        display: block;
        width: 100%;
        height: 32px;
        line-height: 32px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    li.curLi {
      background: #3d9ffe;
      color: #fff;
      .textDiv {
        border-bottom: none;
      }
    }
  }
}
.timeUl {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
  margin-top: 4px;
  li {
    margin-right: 15px;
    list-style: none;
    color: #606266;
    cursor: pointer;
    height: 28px;
    line-height: 28px;
    padding: 0 6px;
    border-radius: 3px;
  }
  li:first-child {
    display: none;
  }
  li.active {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }
  li.activeErrorLi {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }
}
.box-none {
  height: 200px;
  width: 100%;
  padding-top: 20px;
  text-align: center;
  background: #fff;
}
.none-data {
  margin-top: 70px;
  text-align: center;
}
::v-deep.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  color: #3d9ffe;
}
::v-deep .el-form-item {
  margin-bottom: 0px;
}
::v-deep .el-dialog__body {
  padding: 0 20px;
}
::v-deep .el-tabs__item {
  padding: 0 10px;
  height: 30px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  line-height: 30px;
  display: inline-block;
  list-style: none;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  position: relative;
}
.caredError {
  padding-left: 20px;
  padding-top: 20px;
  background: #fff;
}
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  min-height: calc(100vh - 60px);
  .homeForm {
    width: 100%;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    margin-bottom: 20px;
  }
  h2.diff {
    width: 100%;
    overflow: hidden;
    background: #fff;
    height: 60px;
    line-height: 60px;
    padding: 0 20px;
    box-sizing: border-box;
    font-weight: normal;
    font-size: 14px;
  }
  .homeBody {
    width: 100%;
    overflow: hidden;
    h2.homeH2 {
      width: 100%;
      overflow: hidden;
      background: #fff;
      height: 60px;
      line-height: 60px;
      padding: 0 20px;
      box-sizing: border-box;
      font-weight: normal;
      font-size: 14px;
      span {
        display: inline-block;
        border-left: solid 3px #3d9ffe;
        height: 26px;
        line-height: 26px;
        padding-left: 10px;
      }
    }
    .homeBox:after {
      content: "";
      width: 32%;
      height: 0px;
      visibility: hidden;
    }
    .homeBox {
      margin-top: 20px;
      width: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;

      div.bigTitle {
        width: 70%;
        background: #fff;
        margin-bottom: 20px;
        height: 530px;
        margin: 0 auto;
        h3 {
          width: 100%;
          overflow: hidden;
          height: 50px;
          line-height: 50px;
          padding: 0px 20px;
          box-sizing: border-box;
          font-weight: normal;
          font-size: 14px;
          margin: 0px;
          span {
            display: inline-block;
            float: right;
            cursor: pointer;
          }
        }
        h3.bigH3One {
          background: rgba(244, 98, 99, 0.04);
          border-top: solid 3px #f46263;
        }
        h3.bigH3Two {
          background: rgba(255, 187, 51, 0.04);
          border-top: solid 3px #ffbb33;
        }
        h3.bigH3Three {
          background: rgba(61, 159, 254, 0.04);
          border-top: solid 3px #3d9ffe;
        }
        ul {
          margin: 0;
          padding: 0;
        }
        li {
          list-style: none;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          border-bottom: dotted 1px #ccc;
          height: 46px;
          line-height: 46px;
          padding: 0 10px;
          box-sizing: border-box;
          font-size: 14px;
          cursor: pointer;
          p {
            margin: 0;
            padding: 0;
            width: 50%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          span {
            text-align: center;
            display: inline-block;
            width: 30%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        // li:nth-child(8) {
        //   border-bottom: none;
        // }
      }
    }
  }
}
</style>
