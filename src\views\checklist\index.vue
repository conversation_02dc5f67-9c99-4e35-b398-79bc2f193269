<template>
  <div class="home">
    <div class="homeFrom">
      <el-form :model="queryForm" :inline="true">
        <el-form-item prop="fileName" label="文件名：">
          <el-input v-model.trim="queryForm.fileName" placeholder="请输入名称" size="small"></el-input>
        </el-form-item>
        <el-form-item label="上传时间：">
          <!-- <div style="line-height: 28px">
            <el-date-picker v-model="queryForm.createTimeStart" size="small" type="datetime"
              format="yyyy-MM-dd HH:mm:ss" value-format="timestamp" placeholder="请选择开始时间"
              :picker-options="pickerOptionsStart"></el-date-picker> -
            <el-date-picker v-model="queryForm.createTimeEnd" size="small" type="datetime" format="yyyy-MM-dd HH:mm:ss"
              value-format="timestamp" placeholder="请选择结束时间" :picker-options="pickerOptionsEnd"></el-date-picker>
          </div> -->
          
      <!-- value-format="yyyy-MM-dd HH:mm:ss" -->
          <el-date-picker
            v-model="dataTime"
            @change="handleDateChange"
            size="small"
            unlink-panels
            value-format="timestamp"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <!-- v-hasPermi="['system:themClass:query']" -->
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button type="warning" @click="delList" class="mb8" size="mini"
            v-hasPermi="['system:themClass:del']">批量删除</el-button>
        </el-col>
      </el-row>

      <el-table :data="themList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" :index="getIndex" align="center" width="80"> </el-table-column>
        <el-table-column prop="originalName" label="文件名" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <a style="color: #409EFF;" :href="scope.row.videoUrl" target="_blank">{{ scope.row.originalName }}</a>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="检测状态" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.status === 1">检测中</span>
            <span v-else-if="scope.row.status === 2">检测成功</span>
            <span v-else>
              检测失败
              <el-tooltip class="item" effect="dark" :content="scope.row.failReason||'暂无'" placement="top-start">
                <a style="color: #409EFF;">?</a>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="suggestion" label="建议结果" align="center">
          <template slot-scope="scope">
            {{ scope.row.suggestion == 0 ? '通过' : scope.row.suggestion == 1 ? '嫌疑' : scope.row.suggestion == 2 ? '不通过' : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="上传时间" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.createTime ? moment(Number(scope.row.createTime)).format('YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="检测完成时间" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.updateTime ? moment(Number(scope.row.updateTime)).format('YYYY-MM-DD HH:mm:ss') : '' }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="seeDialog(scope.row)"
              v-show="scope.row.status == 2 && (scope.row.suggestion == 1 || scope.row.suggestion == 2)">查看</el-button>
            <el-button type="text" @click="delList(scope.row)" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryForm.pageNum" :limit.sync="queryForm.pageSize"
        @pagination="handleQuery" />
    </div>

    <!-- 查看弹窗 -->
    <el-dialog title="视频检测结果" width="80%" :visible.sync="dialogFormVisible">
      <el-radio-group v-model="tabPosition" style="margin-bottom: 30px;">
        <el-radio-button label="1">画面检测</el-radio-button>
        <el-radio-button label="2">音频检测</el-radio-button>
      </el-radio-group>
      <div v-if="tabPosition == 1">
        <div class="dialogBox" v-if="seeData && seeData.length">
          <div class="dialogDiv" v-for="item, index in seeData" :key="index">
            <img :src="item.url" alt="">
            <!-- <ErrImg :errorData="item"></ErrImg> -->
            <ul v-if="item.picLabel && item.picLabel.length">
              <li><span>识别内容：</span>
                <el-tooltip class="item" effect="dark" :content="item.picLabel[0].value" placement="top-start">
                  <p>{{ item.picLabel[0].value }}</p>
                </el-tooltip>
              </li>
              <li><span>命中级别：</span>
                <el-tooltip class="item" effect="dark"
                  :content="item.picLabel[0].level == 1 ? '疑似' : item.picLabel[0].level == 2 ? '确定' : item.picLabel[0].level == 0 ? '正常' : ''"
                  placement="top-start">
                  <p>{{ item.picLabel[0].level == 1 ? '疑似' : item.picLabel[0].level == 2 ? '确定' : item.picLabel[0].level == 0 ? '正常' : '' }}</p>
                </el-tooltip>
              </li>
              <li><span>类型：</span>
                <el-tooltip class="item" effect="dark" :content="item.picLabel[0].labelName" placement="top-start">
                  <p>{{ item.picLabel[0].labelName }}</p>
                </el-tooltip>
              </li>
              <li><span>具体类型：</span>
                <el-tooltip class="item" effect="dark" :content="item.picLabel[0].secondLabelName" placement="top-start">
                  <p>{{ item.picLabel[0].secondLabelName }}</p>
                </el-tooltip>
              </li>
              <li class="liDiff"><span>时间段：</span>
                <el-tooltip class="item" effect="dark" :content="item.duration" placement="top-start">
                  <span>{{ item.duration }}</span>
                </el-tooltip>
              </li>
              <li class="liDiff">
                <span>截图：</span>
                <em @click="jumpURL(item.url)" style="margin-left: 10px;">查看截图</em>
                <em @click="copyMe" :data-clipboard-text="item.url" class="copyBtn"
                  style="margin-left: 10px;">复制URL</em>
              </li>
            </ul>
          </div>
        </div>
        <div v-else class="dialogNull">检测正常，无敏感内容</div>
      </div>
      <div v-else-if="tabPosition == 2">
        <el-table :data="audio" width="100" v-if="audio && audio.length">
          <el-table-column label="时间戳" prop="duration" align="center">
          </el-table-column>
          <el-table-column label="命中级别" prop="result" align="center">
            <template slot-scope="scope">
              {{ scope.row.audioLabel[0].level == 2 ? '不通过' : scope.row.audioLabel[0].level == 1 ? '嫌疑' : '通过' }}
            </template>
          </el-table-column>
          <el-table-column label="数据类别" prop="timeRange" align="center">
            <template slot-scope="scope">
              <span v-show="scope.row.audioLabel[0].labelName">{{ scope.row.audioLabel[0].labelName }}</span>
              <span v-show="scope.row.audioLabel[0].secondLabelName">-{{ scope.row.audioLabel[0].secondLabelName }}</span>
              <span v-show="scope.row.audioLabel[0].thirdLabelName">-{{ scope.row.audioLabel[0].thirdLabelName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="识别内容" prop="content" align="center">
            <template slot-scope="scope">
              <span v-html="replaceWrong(scope.row)" class="emRed"></span>
            </template>
          </el-table-column>
        </el-table>
        <div v-else class="dialogNull">检测正常，无敏感内容</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCheckListApi,
  delvideoListApi,
  getSeeContentApi
} from "@/api/textReview";
import ErrImg from "@/views/components/ErrImg";

export default {
  components: {
    ErrImg
  },
  data() {
    return {
      tabPosition: '1',
      dataTime:[],
      queryForm: {
        fileName: "",
        createTimeStart: '',
        createTimeEnd: '',
        pageSize: 10,
        pageNum: 1
      },
      themList: [],
      total: 0,
      dialogFormVisible: false,
      ids: [],
      single: true,
      // 非多个禁用
      multiple: true,
      rules: {
        name: [{ required: true, trigger: "blur", message: "名称不能为空" }],
        // remark: [{ required: true, trigger: "blur", message: "备注不能为空" }],
      },
      seeData: [],
      audio: [],
      // pickerOptionsStart: {
      //   disabledDate: (time) => {
      //     let endDateVal = this.queryForm.createTimeEnd;
      //     if (endDateVal) {
      //       return time.getTime() > new Date(endDateVal).getTime();
      //     }
      //   },
      // },
      // pickerOptionsEnd: {
      //   disabledDate: (time) => {
      //     let beginDateVal = this.queryForm.createTimeStart;
      //     if (beginDateVal) {
      //       return time.getTime() < new Date(beginDateVal).getTime();
      //     }
      //   },
      // },
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    handleDateChange(){
      if(this.dataTime && this.dataTime.length>0){
        this.queryForm.createTimeStart = this.dataTime[0]
        this.queryForm.createTimeEnd = this.dataTime[1]
      }else{
        this.queryForm.createTimeStart = ''
        this.queryForm.createTimeEnd = ''
      }
    },
    // 复制
    copyMe() {
      var clipboard = new this.Clipboard('.copyBtn')
      clipboard.on('success', () => {
        this.$message.success('复制成功')
        clipboard.destroy()
      })
      clipboard.on('error', () => {
        this.$message.error('复制失败')
        clipboard.destroy()
      })
    },
    // 查看原文
    jumpURL(url) {
      window.open(url, '_blank')
    },
    // 序号连续
    getIndex(index) {
      return (this.queryForm.pageNum - 1) * this.queryForm.pageSize + index + 1;
    },
    //   搜索
    async handleQuery() {
      let params = {
        fileName: this.queryForm.fileName,
        start: this.queryForm.createTimeStart,
        end: this.queryForm.createTimeEnd,
        pageSize: this.queryForm.pageSize,
        pageNum: this.queryForm.pageNum,
      };
      let res = await getCheckListApi(params);
      this.themList = res.data.list;
      this.total = res.data.total;
    },
    // 重置
    resetQuery() {
      this.dataTime = []
      this.queryForm.fileName = ""
      this.queryForm.createTimeStart = ''
      this.queryForm.createTimeEnd = ''
      this.queryForm.pageNum = 1
      this.queryForm.pageSize = 10
      this.handleQuery()
    },
    // 查看
    async seeDialog(row) {
      this.dialogFormVisible = true;
      this.tabPosition = 1
      this.seeData = row.videoSegment
      this.audio = row.audioSegment
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 刪除
    delList(row) {
      const ids = row.id ? [row.id] : this.ids;
      this.$confirm("是否确认删除该数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delvideoListApi(ids);
        })
        .then((res) => {
          if (res.code == 200) {
            this.handleQuery();
            this.msgSuccess("删除成功");
          } else {
            this.$message.error(res.msg);
          }
        });
    },
    replaceWrong(row){
      return row.content.replace(new RegExp(row.audioLabel[0].value, 'g'), `<em>${row.audioLabel[0].value}</em>`);
    }
  },
};
</script>
<style>
.el-form--inline .el-form-item__content {
  vertical-align: middle;
}
</style>

<style scoped lang="scss">
.dialogNull {
  width: 100%;
  height: 500px;
  line-height: 500px;
  text-align: center;
}

.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: 889px;

  .homeFrom {
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
  }
}

.dialogBox {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.dialogDiv {
  width: 32%;
  overflow: hidden;
  margin-bottom: 20px;
  margin-right: 2%;

  img {
    max-width: 100%;
    overflow: hidden;
    max-height: 270px;
    margin: 0 auto;
  }

  ul {
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    font-size: 0.8vw;

    li {
      list-style: none;
      width: 50%;
      // height: 38px;
      // line-height: 38px;
      height: 1.8vw;
      line-height: 1.8vw;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;

      span {
        display: inline-block;
        // width: 53%;
      }

      p {
        margin: 0;
        padding: 0;
        width: 46%;
        overflow: hidden;
        white-space: nowrap; //不折行
        text-overflow: ellipsis; //溢出显示省略号

      }

      em {
        font-style: normal;
        display: inline-block;
        color: #02A7F0;
        cursor: pointer;

      }
    }

    li:nth-child(1n) {
      width: 45%;

      span {
        // width: 48%;
      }
    }

    li:nth-child(2n) {
      width: 55%;
    }


    li.liDiff {
      width: 100%;

      span {
        // width: 20%;
      }

      p {
        width: 70%;
        overflow: hidden; //超出隐藏
        white-space: nowrap; //不折行
        text-overflow: ellipsis; //溢出显示省略号
      }
    }
  }
}

.dialogDiv:nth-child(3n) {
  margin-right: 0;
}
</style>
