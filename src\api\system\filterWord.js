import request from '@/utils/request'

// 搜索
export function handleFilter<PERSON>pi (query) {
    return request({
        url: '/task/filterWord/list',
        method: 'get',
        params: query
    })
}
// 新增
export function AddfilterApi (data) {
    return request({
        url: '/task/filterWord',
        method: 'post',
        data: data
    })
}
// 修改
export function editfilterApi (data) {
    return request({
        url: '/task/filterWord',
        method: 'put',
        data: data
    })
}
// 删除

export function delfilterApi (ids) {
    return request({
        url: '/task/filterWord/' + ids,
        method: 'delete',
    })
}
// 查看
export function seefilterApi (id) {
    return request({
        url: '/task/filterWord/' + id,
        method: 'get',
    })
}
// 导入过滤词
export function saveImportfilterApi (data) {
    return request({
        url: '/task/filterWord/saveImport',
        method: 'post',
        data: data
    })
}
// 过滤词导入模板下载
export function filefilterDownApi () {
    window.location.href = process.env.VUE_APP_BASE_API + "/task/filterWord/downModel"
}

// 获取问题词数据
export function getquesListApi (data) {
    return request({
        url: '/task/problemWord/listWrongWord',
        method: 'post',
        data: data
    })
}
