<template>
    <div class="home">
      <div class="homeFrom">
        <el-form :model="queryForm" :inline="true">
          <el-form-item prop="name" label="系统名称：">
            <el-input
              v-model.trim="queryForm.name"
              placeholder="请输入系统名称"
              size="small"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
        <el-row :gutter="10">
          <el-col :span="1.5">
            <el-button type="primary" @click="addList" class="mb8" size="mini"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              @click="delList"
              class="mb8"
              size="mini"
              :disabled="multiple"
              >批量删除</el-button
            >
          </el-col>
        </el-row>
  
        <el-table :data="themList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            type="index"
            label="序号"
            :index="getIndex"
            width="80"
          ></el-table-column>
          <el-table-column prop="name" label="sass系统名称" align="center"> </el-table-column>
          <el-table-column prop="context" label="标识" align="center" width="80px"> 
          </el-table-column>
          <el-table-column prop="logo" label="logo" align="center">
            <template slot-scope="scope">
                <div>
                    <img :src="getLogo(scope.row.logo)" alt="" style="width:100px;">
                 </div>
            </template>
         </el-table-column>
          <el-table-column prop="backImage" label="登录背景图" align="center">
            <template slot-scope="scope">
                <div>
                    <img :src="getLogo(scope.row.backImage)" alt="" style="width:100px;">
                 </div>
            </template>
         </el-table-column>
          <el-table-column prop="url" label="链接" align="center" > </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="bindUser(scope.row)"
                >绑定用户</el-button
              >
              <el-button type="text" size="small" @click="seeDialog(scope.row)"
                >查看已绑用户</el-button
              >
              <el-button type="text" size="small" @click="editDialog(scope.row)"
                >修改</el-button
              >
              <el-button type="text" @click="delList(scope.row)" size="small"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="pageNum"
          :limit.sync="pageSize"
          @pagination="handleQuery"
        />
      </div>
  
    <!-- 个性化设置弹框 -->
     <el-dialog :title="title" :visible.sync="dialogFormVisible" width="600px" @close="cancelPersonDialog">
      <el-form :model="personalityForm" label-width="160px" :rules="rulesPerson" ref="personalityRef">
        <el-form-item label="标识：" prop="context">
          <el-input v-model.trim="personalityForm.context"></el-input>
        </el-form-item>
        <el-form-item label="系统名称：" prop="name">
          <el-input v-model.trim="personalityForm.name"></el-input>
        </el-form-item>
        <el-form-item label="上传logo：" prop="logo">
          <!-- <imageUpload v-model="personalityForm.logo"></imageUpload> -->
          <el-upload class="upload-demo" :action="uploadImgUrl" v-model="personalityForm.logo" ref="uploadlogo"
            :limit="1" accept=".png,.jpg" :headers="headers" :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccesslogo" :on-remove="handleFileRemove" :on-exceed="masterFileMax" show-file-list
            :file-list="listLogo" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件</div>
            <div slot="file" slot-scope="{ file }">
              <li class="el-upload-list__item is-success">
                <a @click="() => openUrl(file)" class="el-upload-list__item-name">
                  <i class="el-icon-document"></i>{{ file.name }}
                </a>
                <label class="el-upload-list__item-status-label">
                  <i class="el-icon-upload-success el-icon-circle-check"></i>
                </label>
                <i @click.stop="handleRemove(file)" class="el-icon-close"></i>
              </li>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="上传登录页背景图：" prop="backImage">
          <el-upload class="upload-demo" :action="uploadImgUrl" v-model="personalityForm.backImage" ref="uploadlogoTwo"
            :limit="1" accept=".png,.jpg" :headers="headers" :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccesslogoTwo" :on-remove="handleFileRemoveTwo" :on-exceed="masterFileMaxTwo"
            show-file-list :file-list="listLogoTwo" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__text">建议上传1920px*1080px</div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件</div>
            <div slot="file" slot-scope="{ file }">
              <li class="el-upload-list__item is-success">
                <a @click="() => openUrl(file)" class="el-upload-list__item-name">
                  <i class="el-icon-document"></i>{{ file.name }}
                </a>
                <label class="el-upload-list__item-status-label">
                  <i class="el-icon-upload-success el-icon-circle-check"></i>
                </label>
                <i @click.stop="handleRemoveTwo(file)" class="el-icon-close"></i>
              </li>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelPersonDialog">取 消</el-button>
        <el-button type="primary" @click="closePersonDialog">确 定</el-button>
      </div>
     </el-dialog>

     <!-- 绑定用户弹窗 -->
     <el-dialog :title="titleTwo" :visible.sync="userdialogVisible" width="900px" @close="canceluserDialog">
        <div style="padding:20px 50px;box-sizing: border-box;">
        <el-form :model="accountForm" :inline="true">
          <el-form-item prop="userName" label="">
            <el-input
              v-model.trim="accountForm.userName"
              placeholder="请输入账号名称"
              size="small"
            ></el-input>
          </el-form-item>
          <el-form-item prop="nickName" label="">
            <el-input
              v-model.trim="accountForm.nickName"
              placeholder="用户昵称"
              size="small"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleuserQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetuserQuery"
              >重置</el-button
            >
          </el-form-item>
          <el-form-item style="float:right">
            <el-button type="primary" size="mini" :disabled="usermultiple" @click="bindMore">{{titleTwo=='绑定用户'?'批量绑定':'批量解绑'}}</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="userList" @selection-change="handleuserSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
            type="index"
            label="序号"
            :index="getIndexuser"
            width="80"
          ></el-table-column>
          <el-table-column prop="userName" label="账号" align="center"> </el-table-column>
          <el-table-column prop="nickName" label="用户昵称" align="center"> </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
                <el-button type="primary" size="mini" @click="bindMore(scope.row)" >{{titleTwo=='绑定用户'?'+':'解绑'}}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="titleTwo == '绑定用户'?userTotal>0:bindTotal>0"
          :total="titleTwo == '绑定用户'?userTotal:bindTotal"
          :page.sync="accountForm.pageNum"
          :limit.sync="accountForm.pageSize"
          @pagination="titleTwo == '绑定用户'?handleuserQuery():getBindData()"
        />

        </div>
     </el-dialog>

    </div>
  </template>
  
  <script>
  import {
    selectProjectListApi,
    delSassApi,
    filefilterDownApi,
    getquesListApi,
    getUserListApi,
    bandMoreApi,
    getBindUserApi,
    getPersonSaasApi,
    editSaasDialogApi,
    addSassDialogApi
  } from "@/api/system/sassguall";
  import { getToken } from "@/utils/auth";
  export default {
    
    data() {
    let validateImage = (rule, value, callback) => { //验证器
        if (!this.personalityForm.logo) {     //为true代表图片在  false报错
            callback(new Error('请上传图片'));
        } else {
            callback();
            }
    };
    let validateImageTwo = (rule, value, callback) => { //验证器
        if (!this.personalityForm.backImage) {     //为true代表图片在  false报错
            callback(new Error('请上传图片'));
        } else {
            callback();
            }
    };
      return {
        fileList: [],
        uploadFileUrl:
          process.env.VUE_APP_BASE_API + "/task/filterWord/importWord", // 上传的文件服务器地址
        headers: {
          Authorization: "Bearer " + getToken(),
        },
        queryForm: {
          name: "",
        },
        pageNum: 1,
        pageSize: 10,
        themList: [],
        total: 0,
        dialogFormVisible: false,
          title: "新建sass系统名称",
          titleTwo:"绑定用户",
          ids: [],
          userids:[],
        single: true,
        // 非多个禁用
          multiple: true,
          usermultiple:true,
          userList:[],
        options: [],
          loading: false,
          personalityForm: {},
          userdialogVisible:false,
      listLogo: [],
      listLogoTwo: [],
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
        backData: [],
        accountForm: {
                pageNum: 1,
                pageSize:10,
                userName:"",
                nickName:""
          },
          userTotal: 0,
          bindTotal: 0,
          sassid:'',
          rowContext:"",
      rulesPerson: {
        context: [
          { required: true, message: "标识不能为空", trigger: "blur" },
          {
            min: 4,
            max: 10,
            message: "名称长度在4到10个字符",
            trigger: "change",
          },
        ],
        name: [
          { required: true, message: "系统名称不能为空", trigger: "blur" },
          {
            min: 6,
            max: 16,
            message: "名称长度在6到16个字符",
            trigger: "change",
          },
        ],
        logo: [{ required: true,validator: validateImage, message: "logo不能为空", trigger: "change" }],
        backImage: [
          { required: true,validator: validateImageTwo, message: "背景图不能为空", trigger: "change" },
        ],
      },
      };
    },
    created() {
      this.handleQuery();
      this.handleuserQuery()
    },
    methods: {
    // 搜索用户
        async handleuserQuery () { 
            let params = {
                userName: this.accountForm.userName,
                pageNum: this.accountForm.pageNum,
                pageSize: this.accountForm.pageSize,
                nickName: this.accountForm.nickName,
                context:this.rowContext,
                status: 0,
                delFlag:0
        }
        if (this.titleTwo == '绑定用户') {
            let res = await getUserListApi(params)
            this.userList = res.rows
            this.userTotal = res.total
              
        } else if (this.titleTwo == '查看已绑用户') {
            let res = await getBindUserApi(params)
            this.userList = res.rows
            this.userTotal = res.total
            }
        },
        // 重置搜索用户
        resetuserQuery () {
            this.accountForm.userName=""
            this.accountForm.nickName=''
            this.handleuserQuery()
        },
    // 关闭绑定用户弹框
        canceluserDialog () { 
        this.userdialogVisible=false
        this.accountForm.userName=''
        this.accountForm.nickName=''
    },
    // 点击绑定用户按钮
    bindUser (row) {
        this.userdialogVisible = true
        this.titleTwo = "绑定用户"
        this.rowContext = row.context
        this.handleuserQuery()
        },
    // 查看已绑用户
    async seeDialog (row) {
        this.userdialogVisible = true
        this.titleTwo = "查看已绑用户"
        this.rowContext = row.context
        this.getBindData()
        },
        // 获取已绑用户
      async getBindData () {
        let params = {
            context: this.rowContext,
            pageNum: this.accountForm.pageNum,
            pageSize:this.accountForm.pageSize
        }
        let res = await getBindUserApi(params)
        this.userList = res.rows
        this.bindTotal = res.total
        
    },
    // 点击修改按钮
    editDialog (row) {
        this.dialogFormVisible = true;
        this.title="修改sass系统"
        // this.personalityForm.userId = row.userId;
        this.personalityForm.context = row.context
        this.sassid=row.id
        this.getPersonSystem();
    },
    // 根据用户获取个性化配置
    async getPersonSystem () {
      this.backData = [];
      this.listLogo = [];
      this.listLogoTwo = [];
        let params = {
        context: this.personalityForm.context,
      };
      let res = await getPersonSaasApi(params);
      if (res.rows) {
        this.backData = res.rows[0];
      }
      let arr = Object.keys(this.backData);
      if (arr.length !== 0) {
        this.personalityForm = this.backData;
        this.personalityForm.id = this.backData.id;
        this.listLogo.push({
          name: res.rows[0].logo,
          response: {
            url: process.env.VUE_APP_BASE_API + res.rows[0].logo
          }
        });
        this.listLogoTwo.push({
          name: res.rows[0].backImage,
          response: {
            url: process.env.VUE_APP_BASE_API + res.rows[0].backImage,
          },
        });
      }
    },
    // 关闭新增弹框
    cancelPersonDialog () {
      this.dialogFormVisible = false;
      for (let key in this.personalityForm) {
        this.$set(this.personalityForm, key, "");
        }
        this.listLogo = []
        this.listLogoTwo=[]
        this.$refs.personalityRef.resetFields()
    },
    handleFileRemoveTwo (file, fileList) {
      this.personalityForm.backImage = "";
      this.$refs.uploadlogoTwo.clearFiles();
    },
    handleFileSuccesslogoTwo (response, file, fileList) {
    //   this.upload.open = false;
    //   this.upload.isUploading = false;
      this.personalityForm.backImage = response.fileName;
      this.listLogoTwo = [];
      this.listLogoTwo.push({
        name: response.url,
        response: {
          url: process.env.VUE_APP_BASE_API + response.fileName,
        },
      });
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
    },
    openUrl (file) {
      window.open(file.response.url);
    },
    closePersonDialog () {
        console.log("this.personalityForm",this.personalityForm)
      this.$refs["personalityRef"].validate((valid) => {
          if (valid) {
            let arr = Object.keys(this.backData);
          if (this.title=='新建sass系统名称') {
            // 新增个性化设置
            let params = [{
              userId: this.personalityForm.userId,
              name: this.personalityForm.name,
              context: this.personalityForm.context,
              // state: this.personalityForm.status,
              logo: this.personalityForm.logo,
              backImage: this.personalityForm.backImage,
            }];
            addSassDialogApi(params).then((res) => {
              if (res.code == 200) {
                this.msgSuccess("设置成功");
                this.dialogFormVisible = false;
                for (let key in this.personalityForm) {
                  this.$set(this.personalityForm, key, "");
                }
                this.$refs.uploadlogo.clearFiles();
                this.$refs.uploadlogoTwo.clearFiles();
                this.handleQuery()
              }else{
                this.$message.error(res.msg)
              }
            });
          } else if(this.title=='修改sass系统') {
            // 修改个性化设置
            let params = {
              id: this.sassid,
              name: this.personalityForm.name,
              context: this.personalityForm.context,
              // state: this.personalityForm.status,
              logo: this.personalityForm.logo,
              backImage: this.personalityForm.backImage,
            };
            editSaasDialogApi(params).then((res) => {
              if (res.code == 200) {
                this.msgSuccess("修改成功");
                this.dialogFormVisible = false;
                for (let key in this.personalityForm) {
                  this.$set(this.personalityForm, key, "");
                }
                this.$refs.uploadlogo.clearFiles();
                this.$refs.uploadlogoTwo.clearFiles();
                this.handleQuery()
              }else{
                this.$message.error(res.msg)
              }
            });
          }
        } else {
          this.$message.error("请输入完整信息");
        }
      });
    },
    masterFileMaxTwo () {
      this.$message.warning(`请最多上传 1 个文件。`);
    },
    masterFileMax () {
      this.$message.warning(`请最多上传 1 个文件。`);
    },
    handleFileRemove (file, fileList) {
      this.listLogo = []
      this.personalityForm.logo = "";
      this.$refs.uploadlogo.clearFiles();
    },
    handleRemoveTwo (file) {
      this.listLogoTwo=[]
      this.$refs.uploadlogoTwo.clearFiles();
      this.personalityForm.backImage = "";
    },
    handleFileSuccesslogo (response, file, fileList) {
      this.personalityForm.logo = response.fileName;
      this.listLogo = [];
      this.listLogo.push({
        name: response.url,
        response: {
          url: process.env.VUE_APP_BASE_API + response.fileName,
        },
      });
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
    },
    // 文件上传中处理
    handleFileUploadProgress (event, file, fileList) {
    //   this.upload.isUploading = true;
    },
    getLogo(val){
        return process.env.VUE_APP_BASE_API+val
        // return 'https://smarteye.boryou.com/prod-api'+val
    },
    // 序号连续
    getIndex(index) {
      return (this.pageNum - 1) * this.pageSize + index + 1;
    },
    getIndexuser(index) {
      return (this.accountForm.pageNum - 1) * this.accountForm.pageSize + index + 1;
    },
    async remoteMethod(query) {
        if (query !== "") {
          this.loading = true;
          let formData = new FormData();
          formData.append("problemWord", query);
          let res = await getquesListApi(formData);
          let queslist = res.data;
          if (res.code == 200) {
            setTimeout(() => {
              this.loading = false;
              this.options = queslist.filter((item) => {
                return (
                  item.problemWord.toLowerCase().indexOf(query.toLowerCase()) > -1
                );
              });
            }, 200);
          } else {
            this.msgError(res.msg);
          }
        } else {
          this.options = [];
        }
      },
      handlePreview(file) {
        console.log(file);
      },
      // 上传成功回调
      handleUploadSuccess(res, file) {
        this.resCode = res.code;
        if (res.code == 200) {
          this.$message.success(res.msg);
        } else {
          this.$message.error(res.msg);
        }
        this.$emit("input", res.url);
      },
      //   文件上传
    handleRemove (file) {
      this.$refs.uploadlogo.clearFiles();
      this.personalityForm.logo = "";
    },
      beforeRemove(file, fileList) {
        return this.$confirm(`确定移除 ${file.name}？`);
      },
      handleExceed(files, fileList) {
        this.$message.warning(
          `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
            files.length + fileList.length
          } 个文件`
        );
      },
      ClosedialogVisible() {
        this.fileList = [];
        this.dialogVisible = false;
      },
      // 过滤词模板下载
      fileWebDown() {
        filefilterDownApi();
      },
      //   搜索
      async handleQuery() {
        let params = {
          name: this.queryForm.name,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        };
        let res = await selectProjectListApi(params);
        this.themList = res.rows;
        console.log("res",res)
        this.total =Number(res.total);
      },
      // 重置
      resetQuery() {
          this.queryForm.name = "";
      },
      // 点击新增弹框
    addList () {
        this.dialogFormVisible = true;
        this.title = "新建sass系统名称";
        for (let key in this.personalityForm) {
            this.$set(this.personalityForm, key, "");
            }
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map((item) => item.id);
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
        },
        handleuserSelectionChange (selection) {
            this.userids = selection.map((item) => item.userId);
        // this.single = selection.length !== 1;
        this.usermultiple = !selection.length;
        },
        // 批量绑定
        bindMore (row) {
            const ids = row.userId ? [row.userId] : this.userids 
            let params = {
                userIds: ids,
                context:this.titleTwo=='查看已绑用户'?null: this.rowContext
             }
        this.$confirm(`是否确认${this.titleTwo=='绑定用户'?'绑定':'解绑'}该数据项?`, "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return bandMoreApi(params);
          })
          .then((res) => {
              if (res.code == 200) {
                  if (this.titleTwo == '绑定用户') {
                    this.handleuserQuery();
                    this.msgSuccess("绑定成功");
                  } else {
                    this.getBindData()
                    this.msgSuccess("解绑成功");
                }
            } else {
              this.$message.error(res.msg);
            }
          });},
      // 刪除
        delList (row) {
        const ids = row.id? [row.id]: this.ids 
        this.$confirm("是否确认删除该数据项?", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(function () {
            return delSassApi(ids);
          })
          .then((res) => {
            if (res.code == 200) {
              this.handleQuery();
              this.msgSuccess("删除成功");
            } else {
              this.$message.error(res.msg);
            }
          });
      },
    },
  };
  </script>
  
  <style scoped lang="scss">
  
  .home {
    width: 100%;
    overflow: hidden;
    background: #f4f7fb;
    padding: 20px 5%;
    box-sizing: border-box;
    min-height: 889px;
    .homeFrom {
      width: 100%;
      overflow: hidden;
      background: #fff;
      padding: 20px;
      box-sizing: border-box;
    }
  }
//   ::v-deep .upload-demo {
//     display: flex;
//     width: 49%;
//     margin: 0 auto;
//     .el-upload-list {
//       margin: 0;
//       padding: 0;
//       list-style: none;
//       border: solid 1px #ccc;
//       height: 32px;
//       line-height: 32px;
//       margin-left: 6px;
//       border-radius: 5px;
//       width: 200px;
//       overflow: hidden;
//     }
//     .el-upload-list__item:first-child {
//       margin-top: 0;
//       height: 32px;
//       line-height: 32px;
//     }
//   }
  </style>