import request from '@/utils/request'

// 网站备案缓存
export function getWebRefreshApi() {
    return request({
        url: '/platform/website/refreshCache',
        method: 'post',
    })
}
// 媒体账号缓存

export function getMedRefreshApi() {
    return request({
        url: '/platform/mediacount/refreshCache',
        method: 'post',
    })
}
// 刷新词过滤缓存

export function getWordRefreshApi() {
    return request({
        url: '/task/filterWord/refreshCache',
        method: 'post',
    })
}
// 获取更新时间
export function getUpdataTimeApi(query) {
    return request({
        url: '/manage/refresh/list',
        method: 'get',
        params: query
    })
}