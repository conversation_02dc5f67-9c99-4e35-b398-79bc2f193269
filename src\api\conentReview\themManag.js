import request from '@/utils/request'

// 查询任务管理列表
export function listCheckTask (query) {
    return request({
        url: '/task/checkTask/list',
        method: 'get',
        params: query
    })
}

// 查询任务管理详细
export function getCheckTask (id) {
    return request({
        url: '/task/checkTask/' + id,
        method: 'get'
    })
}

// 新增任务管理
export function addCheckTask (data) {
    return request({
        url: '/task/checkTask',
        method: 'post',
        data: data
    })
}

// 修改任务管理
export function updateCheckTask (data) {
    return request({
        url: '/task/checkTask',
        method: 'put',
        data: data
    })
}

// 删除任务管理
export function delCheckTask (id) {
    return request({
        url: '/task/checkTask/' + id,
        method: 'delete'
    })
}

// 导出任务管理
export function exportCheckTask (query) {
    return request({
        url: '/task/checkTask/export',
        method: 'get',
        params: query
    })
}
// 导入关键词

export function getKeyWordApi (query) {
    return request({
        url: '/task/checkTask/listWord',
        method: 'get',
        params: query
    })
}
// 选择用户
export function getChooseUserApi (query) {
    return request({
        url: '/task/checkTask/listUser',
        method: 'get',
        params: query
    })
}
// 获取所属分类数据
export function getTypeListApi (query) {
    return request({
        url: '/task/checkTask/listCheckTaskType',
        method: 'get',
        params: query
    })
}

// 修改状态
export function overStateApi (params) {
    return request({
        url: '/task/checkTask/updateState',
        method: 'post',
        data: params
    })
}
// 获取用户
export function getUserDataApi (query) {
    return request({
        url: '/system/user/list',
        method: 'get',
        params: query
    })
}
