<template>
  <div>
    <div class="dialogBox">
      <ErrImg :errorData="seeData"></ErrImg>
      <div class="dialogDiv" v-for="(item, index) in seeData.picLabel" :key="index">
        <ul>
          <li>
            <span>识别内容：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="item.value"
              placement="top-start"
            >
              <p>{{ item.value }}</p>
            </el-tooltip>
          </li>
          <li>
            <span>类别：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="`${item.labelName}${
                item.secondLabelName ? '-' + item.secondLabelName : ''
              }${item.thirdLabelName ? '-' + item.thirdLabelName : ''}`"
              placement="top-start"
            >
              <p>
                {{ item.labelName }}
                {{ item.secondLabelName ? "-" + item.secondLabelName : "" }}
                {{ item.thirdLabelName ? "-" + item.thirdLabelName : "" }}
              </p>
            </el-tooltip>
          </li>
          <li>
            <span>命中级别：</span>
            <el-tooltip
              class="item"
              effect="dark"
              :content="switchLevel(item.level)"
              placement="top-start"
            >
              <p>{{ switchLevel(item.level) }}</p>
            </el-tooltip>
          </li>
        </ul>
      </div>
    </div>
    <!-- <div v-else class="dialogNull">检测正常，无敏感内容</div> -->
  </div>
</template>

<script>
import ErrImg from "@/views/components/ErrImg";
export default {
  components: { ErrImg },
  data() {
    return {
      seeData: {}, //图片数据
    };
  },
  props: ["wrongData"],
  created() {
    // this.getListData()
  },
  methods: {
    async getListData() {
      // console.log('this.wrongData',this.wrongData);
      // 获取查看内容
      // let res = await getSeeContentApi(resultId)
      // let res = await getSeeContentApi('1667080047152758784')
      // if (res.data) {
      this.seeData = this.wrongData;
      // }
    },
    switchLevel(level) {
      switch (level) {
        case 0:
          return "通过";
        case 1:
          return "疑似";
        case 2:
          return "不通过";
        default:
          return "通过";
      }
    },
  },
};
</script>

<style scoped lang="scss">
.dialogBox {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.dialogDiv {
  width: 100%;
  overflow: hidden;
  margin-bottom: 20px;
  margin-right: 2%;

  img {
    width: 100%;
    overflow: hidden;
    // height: 180px;
  }

  ul {
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;

    li {
      list-style: none;
      width: 50%;
      height: 38px;
      line-height: 38px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;

      span {
        display: inline-block;
        // width: 53%;
      }

      p {
        margin: 0;
        padding: 0;
        width: 46%;
        overflow: hidden;
        white-space: nowrap; //不折行
        text-overflow: ellipsis; //溢出显示省略号
      }

      em {
        font-style: normal;
        display: inline-block;
        color: #02a7f0;
        cursor: pointer;
      }
    }

    li:nth-child(1n) {
      width: 45%;

      span {
        // width: 48%;
      }
    }

    li:nth-child(2n) {
      width: 55%;
    }

    li.liDiff {
      width: 100%;

      span {
        // width: 20%;
      }

      p {
        width: 70%;
        overflow: hidden; //超出隐藏
        white-space: nowrap; //不折行
        text-overflow: ellipsis; //溢出显示省略号
      }
    }
  }
}

.dialogDiv:nth-child(3n) {
  margin-right: 0;
}
</style>
