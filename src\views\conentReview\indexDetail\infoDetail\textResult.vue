<template>
  <div class="homeText">
    <div class="homeBox">
      <div class="homeLeftBox">
        <div class="homeLeftHead">
          <span>信息详情</span>
        </div>
        <div class="homeLeftUl" v-loading="loading">
          <h2 v-html="detailDataList.title" class="emRed"></h2>
          <ul>
            <li><span>来源：</span>{{ detailDataList.host }}</li>
            <!-- <li><span>错误分类：</span>{{ detailDataList.checkTaskType }}</li> -->
            <li style="cursor: pointer; color: #409eff">
              <!-- @click="goOrgText()" -->
              <a :href="detailDataList.url" referrerpolicy="no-referrer" target="_blank">查看原文</a>
            </li>
            <li>原文时间：{{ detailDataList.postTime }}</li>
          </ul>
          <p class="textCont emRed" @contextmenu="contextmenu" v-html="detailDataList.redText"></p>
        </div>
      </div>
      <div class="homeRight">
        <span class="wordSpan">词语建议</span>
        <el-table :data="detailDataList.wordList" border="" :row-style="{ height: 10 + 'px' }"
          :cell-style="{ padding: 6 + 'px' }" :header-cell-style="{ height: '10px', padding: '6px' }" v-loading="loading">
          <el-table-column prop="wrongWord" label="问题词语"></el-table-column>
          <el-table-column prop="adviceWord" label="建议词语"></el-table-column>
          <el-table-column prop="wordGroupDetail" label="释义" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="top" width="300" v-if="scope.row.wordGroupDetail">
                <p>{{ scope.row.wordGroupDetail }}</p>
                <div slot="reference" style="background: none">
                  <p class="ellipsis">
                    {{ scope.row.wordGroupDetail }}
                  </p>
                </div>
              </el-popover>
              <p v-else class="ellipsis" style="color: #999; font-size: 12px">
                常见表述错误
              </p>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getTextDetail } from "@/api/checkData";
export default {
  name: "detailPage",
  inject: ["contextmenu"],
  data() {
    return {
      tableData: [{ name: "扶贫攻坚", nameTwo: "脱贫攻坚" }],
      detailDataList: [],
      loading:false,
    };
  },
  props: ['wrongData'],
  created() {

  },
  methods: {
    //   获取详情页数据
    async getListData() {
      console.log('this.wrongData', this.wrongData)
      const { solrId, dataInfoErrorId, dataInfoId } = this.wrongData
      let params = {
        solrId,
        dataInfoErrorId,
        dataInfoId
      };
      this.loading=true
      getTextDetail(params).then(res => {
        let { data } = JSON.parse(JSON.stringify(res))
        if (data) {
          this.detailDataList = {
            ...data,
            wordList: [{
              adviceWord: data.adviceWord,
              wordGroup: data.wordGroup,
              wordGroupDetail: data.wordGroupDetail,
              wrongWord: data.wrongWord,
            }]
          }
        }else{
          this.detailDataList={wordList:[]}
        }
        this.loading=false
      })
    },
    // 查看原文
    goOrgText() {
      window.open(this.detailDataList.url, "_blank");
    },
  },
};
</script>

<style scoped lang="scss">
.ellipsis {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.el-popover {
  height: 150px;
  overflow: auto;
}

.homeText {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  // padding: 20px 6%;
  box-sizing: border-box;
  // min-height: 889px;

  .homeBox {
    width: 100%;
    background: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    // min-height: 800px;

    .homeLeftBox {
      width: 68%;
      overflow: hidden;
      margin-bottom: 20px;

      .homeLeftHead {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        background: #f5f5f5;

        .homBut {
          display: inline-block;
          padding-top: 6px;
          padding-right: 10px;
        }

        span {
          display: inline-block;
          color: #fff;
          background: #ed9e2f;
          height: 40px;
          line-height: 40px;
          padding: 0 10px;
          font-size: 14px;
        }
      }

      .homeLeftUl {
        width: 100%;
        overflow: hidden;

        h2 {
          width: 100%;
          overflow: hidden;
          font-weight: normal;
          font-size: 24px;
        }

        ul {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          //   flex-wrap: wrap;
          margin: 0;
          padding: 0;
          font-size: 14px;
          width: 100%;
          overflow: hidden;
          color: #666666;

          li {
            list-style: none;
            height: 30px;
            line-height: 30px;
            margin-right: 10px;

            span {
              display: inline-block;
              text-align: center;
              margin-right: 8px;
            }
          }
        }

        p.textCont {
          line-height: 28px;
          border: solid 1px #ccc;
          padding: 20px;
          box-sizing: border-box;
          height: 500px;
          overflow-y: scroll;
        }
      }
    }

    .homeRight {
      background: #fff;
      width: 30%;

      span.wordSpan {
        display: block;
        width: 100%;
        height: 40px;
        line-height: 40px;
        background: #3d9ffe;
        color: #fff;
        text-align: center;
        font-size: 14px;
        margin-bottom: 20px;
      }
    }
  }
}
</style>
