<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input
          placeholder="请输入任务名称"
          size="mini"
          v-model.trim="queryParams.name"
        ></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-select
          v-model="queryParams.state"
          size="mini"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="停止执行" value="0" />
          <el-option label="正在执行" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:themManag:add']"
          >新增</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="success"
                   plain
                   icon="el-icon-edit"
                   size="mini"
                   :disabled="single"
                   @click="handleUpdate"
                   v-hasPermi="['task:checkTask:edit']">修改</el-button>
      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:themManag:remove']"
          >批量删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          size="mini"
          :disabled="ids.length == 0 || ids.length > 1"
          :loading="freshLoading"
          @click="handleFresh"
          >刷新</el-button
        >
        <p class="pData" v-show="reashP">{{ reashP }}</p>
      </el-col>
      <el-col :span="1.5">
        <!-- <el-button type="warning"
                   plain
                   icon="el-icon-download"
                   size="mini"
                   @click="handleExport"
        v-hasPermi="['task:checkTask:export']">导出</el-button>-->
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="checkTaskList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        type="index"
        label="序号"
        :index="getIndex"
        width="80"
      ></el-table-column>
      <el-table-column label="任务名称" align="center" prop="name" />
      <el-table-column label="规则" align="center" prop="type">
        <template slot-scope="scope">
          {{
            scope.row.type == 1
              ? "常规"
              : scope.row.type == 2
              ? "导入关键词组"
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="开始时间"
        align="center"
        prop="startTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="结束时间"
        align="center"
        prop="endTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="state">
        <template slot-scope="scope">
          {{
            scope.row.state == 0
              ? "停用"
              : scope.row.state == 1
              ? "正在执行"
              : ""
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:themManag:edit']"
            >改</el-button
          >
          <el-button type="text" @click="goChase(scope.row.id)" size="mini"
            >追</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:themManag:remove']"
            >删</el-button
          >

          <el-button
            type="text"
            v-show="scope.row.state == 0"
            @click="overState(scope.row)"
            size="mini"
            >启用</el-button
          >

          <el-button
            type="text"
            v-show="scope.row.state == 1"
            @click="overState(scope.row)"
            size="mini"
            >禁用</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改任务管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="任务名称：" prop="name">
          <el-input v-model.trim="form.name" placeholder="请输入任务名称" />
        </el-form-item>
        <div style="text-align: center">
          <el-radio-group
            v-model="form.type"
            v-for="dict in typeOptions"
            @change="clickRadio(dict.dictValue)"
            :key="dict.dictValue"
          >
            <el-radio-button
              :label="parseInt(dict.dictValue)"
              style="margin: 0 5px 20px"
              >{{ dict.dictLabel }}</el-radio-button
            >
          </el-radio-group>
        </div>
        <!-- <el-radio-group v-model="form.type">
            <el-radio v-for="dict in typeOptions"
                      :key="dict.dictValue"
                      :label="parseInt(dict.dictValue)">{{dict.dictLabel}}</el-radio>
        </el-radio-group>-->
        <el-form-item
          label="关键词组："
          v-show="this.form.type == 2"
          :prop="this.form.type == 2 ? 'wordMonitorName' : ''"
        >
          <el-row>
            <el-col :span="16" style="margin-right: 10px">
              <el-input
                v-model.trim="form.wordMonitorName"
                placeholder="请选择关键词组"
              />
            </el-col>
            <el-col :span="6">
              <span
                @click="exportKeyWord"
                style="color: #1890ff"
                class="keyWordSpan"
                >导入关键词组</span
              >
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item
          label="关键词1："
          v-show="this.form.type == 1"
          :prop="this.form.type == 1 ? 'keyword1' : ''"
        >
          <el-input
            v-model.trim="form.keyword1"
            type="textarea"
            placeholder="请输入关键词1"
          />
        </el-form-item>
        <el-form-item
          label="关键词2："
          v-show="this.form.type == 1"
          prop="keyword2"
        >
          <el-input
            v-model.trim="form.keyword2"
            type="textarea"
            placeholder="请输入关键词2"
          />
        </el-form-item>
        <el-form-item
          label="关键词3："
          v-show="this.form.type == 1"
          prop="keyword3"
        >
          <el-input
            v-model.trim="form.keyword3"
            type="textarea"
            placeholder="请输入关键词3"
          />
        </el-form-item>
        <el-form-item
          label="过滤词："
          v-show="this.form.type == 1"
          prop="excludeWord"
        >
          <el-input
            v-model.trim="form.excludeWord"
            type="textarea"
            placeholder="请输入过滤词"
          />
        </el-form-item>
        <el-form-item label="错误类型：" prop="wrongType">
          <el-select v-model="form.wrongType" placeholder="请选择错误类型">
            <el-option
              v-for="dict in wrongTypeOptions"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="parseInt(dict.dictValue)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务描述：" prop="description">
          <el-input
            v-model.trim="form.description"
            type="textarea"
            :rows="2"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        <el-form-item label="开始时间：" prop="startTime">
          <el-date-picker
            clearable
            size="small"
            v-model="form.startTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择开始时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间：" prop="endTime">
          <el-date-picker
            clearable
            size="small"
            v-model="form.endTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择结束时间"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="用户：" prop="userIdList">
          <el-select
            v-model="form.userIdList"
            multiple
            placeholder="请选择用户"
          >
            <el-option
              v-for="(item, index) in getUserList"
              :key="index"
              :label="item.nickName"
              :value="item.userId"
            ></el-option>
          </el-select>
          <!-- <el-row>
            <el-col :span="16"
                    style="margin-right:10px">
              <el-input v-model="form.customer"
                        placeholder="请选择用户" />
            </el-col>
            <el-col :span="6">
              <span style="color:#1890ff;cursor:pointer"
                    @click="getChooseUser">选择用户</span>
            </el-col>
          </el-row>-->
        </el-form-item>
        <el-form-item label="所属分类：" prop="checkTaskTypeId">
          <el-select
            v-model="form.checkTaskTypeId"
            placeholder="请选择所属分类"
          >
            <el-option
              v-for="(item, index) in getTypeListData"
              :key="index"
              :label="item.name"
              :value="parseInt(item.id)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="请选择关键词组"
      :visible.sync="dialogVisible"
      width="40%"
      :before-close="handleClose"
    >
      <ul class="keyUl">
        <li
          v-for="(item, index) in keyWordList"
          :class="keyWordCur == index ? 'keyWordActive' : ''"
          @click="keyWordEvent(index, item.id, item.name)"
          :key="index"
        >
          {{ item.name }}
        </li>
      </ul>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="closeKeyWord">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCheckTask,
  getCheckTask,
  delCheckTask,
  addCheckTask,
  updateCheckTask,
  exportCheckTask,
  getKeyWordApi,
  getChooseUserApi,
  getTypeListApi,
  overStateApi,
} from "@/api/conentReview/themManag";
import { refresh, getreashPApi } from "@/api/index";

export default {
  name: "CheckTask",
  components: {},
  data() {
    return {
      reashP: "",
      freshLoading: false,
      dialogVisible: false,
      keyWordList: [],
      keyWordCur: 0,
      getUserList: [],
      getTypeListData: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 任务管理表格数据
      checkTaskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 类型字典
      typeOptions: [],
      // 错误类型字典
      wrongTypeOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: "",
        state: null,
      },
      // 表单参数
      form: {
        type: "",
        wordMonitorId: "",
        wordMonitorName: "",
        id: "",
        userIdList: [],
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "任务名称不能为空", trigger: "blur" },
        ],
        type: [{ required: true, message: "类型不能为空", trigger: "blur" }],
        wordMonitorId: [
          { required: true, message: "关键词组不能为空", trigger: "blur" },
        ],
        wordMonitorName: [
          { required: true, message: "关键词组不能为空", trigger: "blur" },
        ],
        keyword1: [
          { required: true, message: "关键词1不能为空", trigger: "blur" },
        ],
        // keyword2: [
        //   { required: true, message: '关键词2不能为空', trigger: 'blur' },
        // ],
        // keyword3: [
        //   { required: true, message: '关键词3不能为空', trigger: 'blur' },
        // ],
        // excludeWord: [
        //   { required: true, message: '排除词不能为空', trigger: 'blur' },
        // ],
        wrongType: [
          { required: true, message: "错误类型不能为空", trigger: "change" },
        ],
        // description: [
        //   { required: true, message: '任务描述不能为空', trigger: 'blur' },
        // ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "blur" },
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "blur" },
        ],
        state: [{ required: true, message: "状态不能为空", trigger: "blur" }],
        userId: [
          { required: true, message: "客户标识不能为空", trigger: "blur" },
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" },
        ],
        createUserId: [
          { required: true, message: "创建人id不能为空", trigger: "blur" },
        ],
        checkTaskTypeId: [
          { required: true, message: "创建人id不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.getDicts("by_task_word_type").then((response) => {
      this.typeOptions = response.data;
    });
    this.getDicts("by_task_wrong_type").then((response) => {
      this.wrongTypeOptions = response.data;
    });
    this.getKeyWord();
    this.getChooseUser();
    this.getTypeList();
    this.getreashP();
  },
  methods: {
    // 获取刷新按钮后面数据
    async getreashP() {
      let res = await getreashPApi();
      this.reashP = res.msg;
    },
    // 刷新
    async handleFresh() {
      try {
        this.freshLoading = true;
        let res = await refresh({ id: this.ids[0] });
        if (res.code == 200) {
          this.freshLoading = false;
          this.msgSuccess(res.msg);
        }
      } catch (error) {
        console.log(error);
      }
    },
    getIndex(index) {
      return (
        (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
      );
    },
    //   追
    goChase(id) {
      this.$router.push({
        path: "/conentReview/themMangeChase/index",
        query: { id: id },
      });
    },
    //   获取所属分类数据
    async getTypeList() {
      let res = await getTypeListApi();
      this.getTypeListData = res;
      console.log("111", this.getTypeListData);
    },
    //   获取选择用户
    async getChooseUser() {
      let res = await getChooseUserApi();
      this.getUserList = res;
    },
    // 关闭导入关键词组弹窗
    closeKeyWord() {
      for (let i = 0; i < this.keyWordList.length; i++) {
        if (this.keyWordCur == i) {
          this.form.wordMonitorName = this.keyWordList[i].name;
          this.form.wordMonitorId = this.keyWordList[i].id;
        }
      }
      this.dialogVisible = false;
    },
    //   关键词点击事件
    keyWordEvent(index, id, name) {
      this.keyWordCur = index;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    //   导入关键词弹窗
    exportKeyWord() {
      this.dialogVisible = true;
    },
    // 导入关键词
    async getKeyWord() {
      let res = await getKeyWordApi();
      this.keyWordList = res;
    },
    clickRadio(val) {
      if (val == 1) {
        this.form.wordMonitorName = "";
        this.form.wordMonitorId = "";
        this.form.userIdList = [];
        this.form.checkTaskTypeId = null;
      } else if (val == 2) {
        this.form.keyword1 = "";
        this.form.keyword2 = "";
        this.form.keyword3 = "";
        this.form.excludeWord = "";
        this.form.userIdList = [];
        this.form.checkTaskTypeId = null;
      }
    },
    /** 查询任务管理列表 */
    getList() {
      this.loading = true;
      listCheckTask(this.queryParams).then((response) => {
        this.checkTaskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.type);
    },
    // 错误类型字典翻译
    wrongTypeFormat(row, column) {
      return this.selectDictLabel(this.wrongTypeOptions, row.wrongType);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        type: this.typeOptions[0].dictValue,
        wordMonitorId: null,
        keyword1: null,
        keyword2: null,
        keyword3: null,
        excludeWord: null,
        wrongType: null,
        description: null,
        startTime: null,
        endTime: null,
        state: 1,
        createTime: null,
        createUserId: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加任务管理";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      this.form.id = row.id;
      getCheckTask(id).then((response) => {
        this.form = response.data;
        this.form.wordMonitorId = response.data.wordMonitorId;
        this.form.checkTaskTypeId = parseInt(this.form.checkTaskTypeId);
        this.form.userIdList = response.data.userIdList;
        for (let i = 0; i < this.keyWordList.length; i++) {
          if (response.data.wordMonitorId == this.keyWordList[i].id) {
            this.form.wordMonitorName = this.keyWordList[i].name;
          }
        }
        this.open = true;
        this.title = "修改任务管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            if (this.form.userIdList) {
              this.form.userId = this.form.userIdList.toString();
            }
            if (this.form.startTime < this.form.endTime) {
              updateCheckTask(this.form).then((response) => {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              this.msgError("请选择正确时间范围");
            }
          } else {
            if (this.form.userIdList) {
              this.form.userId = this.form.userIdList.toString();
            }
            if (this.form.startTime < this.form.endTime) {
              addCheckTask(this.form).then((response) => {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            } else {
              this.msgError("请选择正确的时间范围");
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm("是否确认删除项任务?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delCheckTask(ids);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    // 结束状态
    overState(row) {
      this.$confirm("此操作将修改任务状态, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let params = {
            id: row.id,
            state: row.state,
          };
          if (row.state == 0) {
            params.state = 1;
          } else if ((row.state = 1)) {
            params.state = 0;
          }
          overStateApi(params).then((res) => {
            if (res.code == 200) {
              this.$message.success("修改成功");
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消修改",
          });
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("您确定要导出数据吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(function () {
          return exportCheckTask(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>
<style>
.el-message-box__header {
  border-bottom: solid 1px #ccc;
}
</style>

<style scoped lang="scss">
.pData {
  margin: 0;
  padding: 0;
  display: inline-block;
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  font-size: 12px;
  color: #999;
}
.keyWordSpan {
  display: inline-block;
  cursor: pointer;
}
.keyUl {
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  li {
    list-style: none;
    padding: 10px;
    border: solid 1px #ccc;
    margin-bottom: 20px;
    width: 30%;
    text-align: center;
    cursor: pointer;
  }
  li.keyWordActive {
    border-color: #3d9ffe;
    color: #3d9ffe;
  }
}
</style>
