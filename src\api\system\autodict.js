import request from '@/utils/request'
// 获取正词列表数据
export function getTrueWordApi (query) {
    return request({
        url: '/task/properWord/list',
        method: 'get',
        params: query
    })
}
// 删除列表
export function delTrueWord (id) {
    return request({
        url: '/task/properWord/' + id,
        method: 'delete'
    })
}
// 修改列表
export function updateTrueWord (data) {
    return request({
        url: '/task/properWord',
        method: 'put',
        data: data
    })
}
// 新增列表
export function addTrueWord (data) {
    return request({
        url: '/task/properWord',
        method: 'post',
        data: data
    })
}
// 查看列表
export function seeTrueWordApi (id) {
    return request({
        url: '/task/properWord/' + id,
        method: 'get',
    })
}

// 获取正词列表数据
export function getsensitiveWordApi (query) {
    return request({
        url: '/task/problemWord/list',
        method: 'get',
        params: query
    })
}
// 删除列表
export function delsensitiveWord (id) {
    return request({
        url: '/task/problemWord/' + id,
        method: 'delete'
    })
}
// 修改列表
export function updatesensitiveWord (data) {
    return request({
        url: '/task/problemWord',
        method: 'put',
        data: data
    })
}
// 新增列表
export function addsensitiveWord (data) {
    return request({
        url: '/task/problemWord',
        method: 'post',
        data: data
    })
}
// 查看列表
export function seesensitiveWordApi (id) {
    return request({
        url: '/task/problemWord/' + id,
        method: 'get',
    })
}
// 获取敏感词分类数据
export function getSensitiveClassApi () {
    return request({
        url: '/task/checkTaskType/list',
        method: 'get',
    })
}

// 获取敏感词专题数据
export function getSensitiveApi (query) {
    return request({
        url: '/task/checkTask/list',
        method: 'get',
        params: query
    })
}
