<template>
  <div class="home">
  <div class="app-container">
    <el-row>
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px">
        <el-form-item label="账号：" prop="userName">
          <el-input v-model.trim="queryParams.userName" placeholder="请输入账号" clearable size="small" style="width: 180px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="用户昵称：" prop="nickName">
          <el-input v-model.trim="queryParams.nickName" placeholder="请输入用户昵称" clearable size="small" style="width: 180px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="部门：" prop="deptIds" v-if="curTigger('部门') || false">
          <treeselect v-model="queryParams.deptIds" :options="deptOptions" :show-count="true" style="width: 180px"
            placeholder="请选择归属部门" />
        </el-form-item>
        <el-form-item label="手机号码：" prop="phonenumber" v-if="curTigger('手机号码') || false">
          <el-input v-model.trim="queryParams.phonenumber" placeholder="请输入手机号码" clearable size="small"
            style="width: 180px" />
        </el-form-item>
        <el-form-item label="账号类型：" prop="isProbation" v-if="curTigger('账号类型') || false">
          <el-select v-model="queryParams.isProbation" placeholder="请选择账号类型" @change="changePro" style="width: 180px">
            <el-option v-for="(item, index) in isProbationOptions" :key="index" :label="item.dictLabel"
              :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据查询范围：" prop="probationPeriod" v-if="curTigger('数据查询范围') || false">
          <el-select v-model="queryParams.probationPeriod" placeholder="请选择数据查询范围" style="width: 180px" >
            <el-option v-for="(item, index) in probationPeriodOptions" :key="index" :label="item.dictLabel"
              :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="角色：" prop="roleIds" v-if="curTigger('角色') || false">
          <el-select v-model="queryParams.roleIds" multiple="" placeholder="请选择" style="width: 180px">
            <el-option v-for="item in rolesListData" :key="item.roleId" :label="item.roleName"
              :value="item.roleId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文稿审校接口：" prop="isUsedIfly" v-if="curTigger('文稿审校接口') || false"
          v-hasPermi="['system:user:isUsedIfly']">
          <el-select v-model="queryParams.isUsedIfly" placeholder="请选择文稿审校接口" style="width: 180px">
            <el-option v-for="(item, index) in isUsedIflylist" :key="index" :label="item.dictLabel"
              :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户经理：" prop="accountManager" v-if="curTigger('客户经理') || false">
          <el-input v-model.trim="queryParams.accountManager" placeholder="请输入客户经理" clearable size="small"
            style="width: 180px" />
        </el-form-item>
        <el-form-item label="所属saas系统：" prop="context" v-if="curTigger('所属saas系统') || false" >
          <!-- <el-input v-model.trim="queryParams.context" placeholder="请输入所属saas系统" clearable size="small"
            style="width: 180px" /> -->
          <el-select v-model.trim="queryParams.context" filterable placeholder="请输入所属saas系统" style="width: 180px">
            <el-option
              v-for="item in saasoptions"
              :key="item.context"
              :label="item.context"
              :value="item.context">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="账号到期时间:" v-if="curTigger('账号到期时间') || false">
          <ul class="timeUl">
            <li v-for="(item, index) in timeRange" :key="index" @click="clickTime(index, item)"
              :class="curLi == index ? 'active' : ''">
              {{ item.name }}
            </li>
            <div style="line-height: 28px" v-if="curLi == 3">
              <el-date-picker v-model="queryParams.expireTimeStart" style="width: 155px" size="small" type="date"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择开始时间">
              </el-date-picker>
              -
              <el-date-picker v-model="queryParams.expireTimeEnd" style="width: 155px" size="small" type="date"
                format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择结束时间"></el-date-picker>
            </div>
          </ul>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="totalView" v-if="checkPermi(['system:user:refreshStatus'])">
        <!-- <h2>总体概况</h2> -->
        <div class="totalBox">
          <div class="boxOne">
            <p>近一周数据刷新</p>
            <span>开始时间：{{refreshStatus.week.start?refreshStatus.week.start:'--'}}</span>
            <span>结束时间：{{refreshStatus.week.end?refreshStatus.week.end:'--'}}</span>
          </div>
          <div class="boxTwo">
            <p>问题数据刷新</p>
            <span>开始时间：{{refreshStatus.existsSolr0.start?refreshStatus.existsSolr0.start:'--'}}</span>
            <span>结束时间：{{refreshStatus.existsSolr0.end?refreshStatus.existsSolr0.end:'--'}}</span>
          </div>
          <div class="boxThree">
            <p>已修改数据刷新</p>
            <span>开始时间：{{refreshStatus.existsSolr1.start?refreshStatus.existsSolr1.start:'--'}}</span>
            <span>结束时间：{{refreshStatus.existsSolr1.end?refreshStatus.existsSolr1.end:'--'}}</span>
          </div>
          <div class="boxFour">
            <p>solr全量数据刷新</p>
            <span>开始时间：{{refreshStatus.solr23.start?refreshStatus.solr23.start:'--'}}</span>
            <span>结束时间：{{refreshStatus.solr23.end?refreshStatus.solr23.end:'--'}}</span>
          </div>
        </div>
      </div>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:user:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['system:user:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:user:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport"
            v-hasPermi="['system:user:import']">导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:user:export']">导出</el-button>
        </el-col>
        <right-toolbar-user :showSearch.sync="showSearch" @queryTable="getList" :columns.sync="columns"
          @sureTabSet="sureSet"></right-toolbar-user>
      </el-row>

      <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="序号" align="center" key="userId" prop="userId" width="50">
          <template slot-scope="scope">
            <div>{{ scope.$index + 1 }}</div>
          </template>
        </el-table-column>
        <el-table-column label="账号" align="center" key="userName" prop="userName" :show-overflow-tooltip="true" />
        <el-table-column label="用户昵称" align="center" key="nickName" prop="nickName" width="180px"
          :show-overflow-tooltip="true" />
        <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName" :show-overflow-tooltip="true"
          v-if="curTigger('部门') || false" />
        <el-table-column label="手机号码" align="center" key="phonenumber" prop="phonenumber"
          v-if="curTigger('手机号码') || false" />
        <el-table-column label="账号类型" align="center" prop="isProbation" v-if="curTigger('账号类型') || false">
          <template slot-scope="scope">
            {{
    scope.row.isProbation == 0
      ? "正式账号"
      : scope.row.isProbation == 1
        ? "试用账号"
        : ""
}}
          </template>
        </el-table-column>
        <el-table-column label="文稿审校接口" align="center" prop="isUsedIfly" v-if="curTigger('文稿审校接口') || false">
          <template slot-scope="scope">
            {{ scope.row.isUsedIfly == 1 ? "讯飞接口&系统接口" : "系统接口" }}
          </template>
        </el-table-column>
        <el-table-column label="每天字数" align="center" v-if="curTigger('每天字数') || false" key="limitedWordsNum" prop="limitedWordsNum"/>
        <el-table-column label="账号到期时间" align="center" v-if="curTigger('账号到期时间') || false">
          <template slot-scope="scope">
            {{
    scope.row.expireTime == null
      ? "永久"
      : moment(scope.row.expireTime).format("YYYY-MM-DD")
}}
          </template>
        </el-table-column>
        <el-table-column label="角色" align="center" prop="roleIds" :formatter="formatRoles"
          v-if="curTigger('角色') || false">
        </el-table-column>
        <el-table-column label="数据查询范围" align="center" prop="probationPeriod" :key="Math.random()"
          :formatter="formatPeri" v-if="curTigger('数据查询范围') || false">
        </el-table-column>
        <el-table-column label="扫描问题状态" align="center" key="dataCheckStatus" prop="dataCheckStatus" width="120"
          v-if="curTigger('扫描问题状态') || false" >
          <template slot="header" slot-scope="scope">
            扫描问题状态
            <el-tooltip class="item" effect="dark" content="当前账号扫描新增问题的状态情况" placement="top-start">
                <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="复检状态" align="center" key="reviewStatus" prop="reviewStatus" width="120"
          v-if="curTigger('复检状态') || false" >
          <template slot="header" slot-scope="scope">
            复检状态
            <el-tooltip class="item" effect="dark" content="当前账号复检历史问题的状态情况" placement="top-start">
                <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="客户经理" align="center" prop="accountManager" v-if="curTigger('客户经理') || false">
          <template slot-scope="scope">
            {{ scope.row.accountManager }}
          </template>
        </el-table-column>
        <el-table-column label="登录时间" align="center" prop="loginDate" v-if="curTigger('登录时间') || false" width="160">
          <template slot-scope="scope">
            <span>{{
              scope.row.loginDate?
    moment(scope.row.loginDate).format("YYYY-MM-DD HH:mm:ss"):
    ''
}}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属saas系统" align="center" prop="context"
          v-if="curTigger('所属saas系统') || false">
        </el-table-column>
        <el-table-column label="客户标识" align="center" key="customer" prop="customer" v-if="curTigger('客户标识') || false"
          :show-overflow-tooltip="true" />
        <el-table-column label="查询节点" align="center" key="indexNode" prop="indexNode" v-if="curTigger('查询节点') || false"
          :show-overflow-tooltip="true" />
        <el-table-column label="操作" align="center" width="220px" class-name="small-padding fixed-width"
          v-if="checkPermi(['system:user:edit'])">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-refresh" @click="refreshClick(scope.row)" :loading="scope.row.scanLoading"
              v-hasPermi="['system:user:refresh']">扫描问题</el-button>
            <el-button size="mini" type="text" icon="el-icon-refresh" :loading="scope.row.loading" @click="recheckClick(scope.row)"
              v-hasPermi="['system:user:refresh']">复检问题</el-button>
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['system:user:edit']">修改</el-button>
            <el-button v-if="scope.row.userId !== 1" size="mini" type="text" icon="el-icon-delete"
              @click="handleDelete(scope.row)" v-hasPermi="['system:user:remove']">删除</el-button>
            <el-button size="mini" type="text" icon="el-icon-key" @click="handleResetPwd(scope.row)"
              v-hasPermi="['system:user:resetPwd']">重置</el-button>
            <!-- <el-button size="mini" type="text" icon="el-icon-s-tools" @click="personalization(scope.row)"
              v-hasPermi="['system:user:diffOther']">个性化设置</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-row>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户昵称" prop="nickName">
              <el-input v-model.trim="form.nickName" placeholder="请输入用户昵称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <treeselect v-model="form.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属部门" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input v-model.trim="form.phonenumber" placeholder="请输入手机号码" maxlength="11" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model.trim="form.email" placeholder="请输入邮箱" maxlength="50" />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="账号" prop="userName">
              <el-input v-model.trim="form.userName" placeholder="请输入账号名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.userId == undefined" label="密码" prop="password">
              <el-input v-model.trim="form.password" placeholder="请输入密码" type="password" show-password />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select v-model="form.sex" placeholder="请选择">
                <el-option v-for="dict in sexOptions" :key="dict.dictValue" :label="dict.dictLabel"
                  :value="dict.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="角色" prop="roleIds">
              <el-select v-model="form.roleIds" multiple="" placeholder="请选择">
                <el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName" :value="item.roleId"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="客户标识" prop="customer">
              <el-input v-model.trim="form.customer" placeholder="请输入客户标识"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="查询节点" prop="indexNode">
              <!-- <el-input
                placeholder="请输入节点"
                v-model="form.indexNode"
              ></el-input> -->
              <el-select v-model="form.indexNode" placeholder="请选择节点" multiple>
                <el-option v-for="(item, index) in indexNodeOptions" :key="index" :label="item.dictLabel"
                  :value="item.dictLabel"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账号到期时间" prop="expireTime">
              <el-date-picker v-model="form.expireTime" type="date" format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户经理" prop="accountManager">
              <el-input placeholder="请输入客户经理" v-model.trim="form.accountManager"></el-input> </el-form-item></el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账号类型" prop="isProbation">
              <el-select v-model="form.isProbation" placeholder="请选择账号类型" @change="changePro">
                <el-option v-for="(item, index) in isProbationOptions" :key="index" :label="item.dictLabel"
                  :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据查询范围" :prop="form.isProbation == 1 ? 'probationPeriod' : ''" v-if="showPro">
              <el-select v-model="form.probationPeriod" placeholder="">
                <el-option v-for="(item, index) in probationPeriodOptions" :key="index" :label="item.dictLabel"
                  :value="item.dictValue"></el-option>
              </el-select> </el-form-item></el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="文稿审校接口" prop="isUsedIfly">
              <el-select v-model="form.isUsedIfly" placeholder="" @change="changeUsedId" >
                <el-option v-for="(item, index) in isUsedIflylist" :key="index" :label="item.dictLabel"
                  :value="item.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每天字数" prop="memberId">
              <el-select v-model="form.memberId" placeholder="">
                <el-option v-for="(item, index) in everyFontNum" :key="index" :label="item.limitedWordsNum"
                  :value="item.id"></el-option>
              </el-select> </el-form-item></el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model.trim="form.remark" type="textarea" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :disabled="nosubmit">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
          <el-link type="info" style="font-size: 12px" @click="importTemplate">下载模板</el-link>
        </div>
        <div class="el-upload__tip" style="color: red" slot="tip">
          提示：仅允许导入“xls”或“xlsx”格式文件！
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 个性化设置弹框 -->
    <el-dialog title="个性化设置" :visible.sync="dialogFormVisible" width="600px" @close="cancelPersonDialog">
      <el-form :model="personalityForm" label-width="140px" :rules="rulesPerson" ref="personalityRef">
        <el-form-item label="标识：" prop="context">
          <el-input v-model.trim="personalityForm.context"></el-input>
        </el-form-item>
        <el-form-item label="系统名称：" prop="name">
          <el-input v-model.trim="personalityForm.name"></el-input>
        </el-form-item>
        <el-form-item label="上传logo：" prop="logo">
          <!-- <imageUpload v-model="personalityForm.logo"></imageUpload> -->
          <el-upload class="upload-demo" :action="uploadImgUrl" v-model="personalityForm.logo" ref="uploadlogo"
            :limit="1" accept=".png,.jpg" :headers="headers" :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccesslogo" :on-remove="handleFileRemove" :on-exceed="masterFileMax" show-file-list
            :file-list="listLogo" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件</div>
            <div slot="file" slot-scope="{ file }">
              <li class="el-upload-list__item is-success">
                <a @click="() => openUrl(file)" class="el-upload-list__item-name">
                  <i class="el-icon-document"></i>{{ file.name }}
                </a>
                <label class="el-upload-list__item-status-label">
                  <i class="el-icon-upload-success el-icon-circle-check"></i>
                </label>
                <i @click.stop="handleRemove(file)" class="el-icon-close"></i>
              </li>
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="上传登录页背景图：" prop="backImage">
          <el-upload class="upload-demo" :action="uploadImgUrl" v-model="personalityForm.backImage" ref="uploadlogoTwo"
            :limit="1" accept=".png,.jpg" :headers="headers" :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccesslogoTwo" :on-remove="handleFileRemoveTwo" :on-exceed="masterFileMaxTwo"
            show-file-list :file-list="listLogoTwo" drag>
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__text">建议上传1920px*1080px</div>
            <div class="el-upload__tip" slot="tip">只能上传jpg/png文件</div>
            <div slot="file" slot-scope="{ file }">
              <li class="el-upload-list__item is-success">
                <a @click="() => openUrl(file)" class="el-upload-list__item-name">
                  <i class="el-icon-document"></i>{{ file.name }}
                </a>
                <label class="el-upload-list__item-status-label">
                  <i class="el-icon-upload-success el-icon-circle-check"></i>
                </label>
                <i @click.stop="handleRemoveTwo(file)" class="el-icon-close"></i>
              </li>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelPersonDialog">取 消</el-button>
        <el-button type="primary" @click="closePersonDialog">确 定</el-button>
      </div>
    </el-dialog>
  </div>
  </div>
</template>

<script>
import {
  listUser,
  getUser,
  delUser,
  addUser,
  updateUser,
  exportUser,
  resetUserPwd,
  changeUserStatus,
  importTemplate,
  closePersonDialogApi,
  getPersonSystemaApi,
  editPersonDialogApi,
  getdayNumApi,
  getTabeSetApi,
  sureSetApi,
  getdayfontApi,
  getSaasSysApi,
  getallRefreshStatus
} from "@/api/system/user";
  import {
    handleQueryApi,
  } from "@/api/system/textreviewSet";
import { getInfo } from "@/api/login";
import { getToken } from "@/utils/auth";
import { treeselect } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import imageUpload from "@/components/ImageUpload";
import { refresh,recheck } from "@/api/index";
import { IsPassword } from "@/utils/validate.js";
// 自定义表格工具扩展
import RightToolbarUser from "@/components/RightToolbarUser";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
export default {
  name: "User",
  components: { Treeselect, imageUpload, RightToolbarUser },
  data () {
    return {
      recheckLoading:false,
      curLi: 0,
      timeRange: [
        { name: "不限", value: "" },
        { name: "已到期", value: 1 },
        { name: "进行中", value: 0 },
        { name: "自定义", value: "" },
      ],
      showPro: false,
      isProbationOptions: [],
      indexNodeOptions: [],
      probationPeriodOptions: [],
      uploadImgUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      listLogo: [],
      listLogoTwo: [],
      backData: null,
      dialogFormVisible: false,
      personalityForm: {},
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 性别状态字典
      sexOptions: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label",
      },
      everyFontNum: [],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData",
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        nickName: undefined,
        deptIds: undefined,
        phonenumber: "",
        isProbation: "",
        probationPeriod: "",
        roleIds: [],
        isUsedIfly: "",
        accountManager: "",
        expireTimeStart: "",
        expireTimeEnd: "",
        expireStatus: "",
      },
      rolesListData: [],
      isUsedIflylist: [
        { dictLabel: "讯飞接口&系统接口", dictValue: 1 },
        { dictLabel: "系统接口", dictValue: 0 },
      ],
      nosubmit: false,
      saasoptions:[],
      // 列信息
      columns: [],
      // columns: [
      //   { key: 0, label: `部门`, visible: true },
      //   { key: 1, label: `手机号码`, visible: true },
      //   { key: 2, label: `账号类型`, visible: true },
      //   { key: 3, label: `讯飞接口`, visible: false },
      //   { key: 4, label: `账号到期时间`, visible: true },
      //   { key: 5, label: `角色`, visible: false },
      //   { key: 6, label: `数据查询范围`, visible: true },
      //   { key: 7, label: `数据更新时间`, visible: true },
      //   { key: 8, label: `客户经理`, visible: false },
      //   { key: 9, label: `登录时间`, visible: true },
      //   { key: 10, label: `客户标识`, visible: true },
      //   { key: 11, label: `查询节点`, visible: true },
      // ],
      // 表单校验
      rules: {
        customer: [
          { required: true, message: "用户标识不能为空", trigger: "blur" },
        ],
        indexNode: [
          { required: true, message: "查询节点不能为空", trigger: "blur" },
        ],
        userName: [
          { required: true, message: "账号不能为空", trigger: "blur" },
        ],
        nickName: [
          { required: true, message: "用户昵称不能为空", trigger: "blur" },
        ],
        password: [{ required: true, validator: IsPassword, trigger: "blur" }],
        roleIds: [{ required: true, message: "角色不能为空", trigger: "blur" }],
        expireTime: [
          { required: true, message: "角色不能为空", trigger: "change" },
        ],
        isProbation: [
          {
            required: true,
            message: "账号类型不能为空",
            trigger: "change",
          },
        ],
        probationPeriod: [
          {
            required: true,
            message: "数据查询范围不能为空",
            trigger: "change",
          },
        ],
        isUsedIfly: [
          {
            required: true,
            message: "是否含有讯飞接口不能为空",
            trigger: "change",
          },
        ],
        memberId: [
          {
            required: true,
            message: "每天字数不能为空",
            trigger: "change",
          },
        ],
        email: [
          {
            type: "email",
            message: "'请输入正确的邮箱地址",
            trigger: ["blur", "change"],
          },
        ],
        phonenumber: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur",
          },
        ],
      },
      rulesPerson: {
        context: [
          { required: true, message: "标识不能为空", trigger: "blur" },
          {
            min: 4,
            max: 10,
            message: "名称长度在4到10个字符",
            trigger: "change",
          },
        ],
        name: [
          { required: true, message: "系统名称不能为空", trigger: "blur" },
          {
            min: 6,
            max: 16,
            message: "名称长度在6到16个字符",
            trigger: "change",
          },
        ],
        logo: [{ required: true, message: "logo不能为空", trigger: "blur" }],
        backImage: [
          { required: true, message: "背景图不能为空", trigger: "blur" },
        ],
      },
      refreshStatus:{
        week:{
          start:'',
          end:''
        },
        existsSolr0:{
          start:'',
          end:''
        },
        existsSolr1:{
          start:'',
          end:''
        },
        solr23:{
          start:'',
          end:''
        }
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName (val) {
      this.$refs.tree.filter(val);
    },
  },
  created () {
    this.getTabeSet();
    this.getList();
    this.getTreeselect();
    this.getdayNum();
    this.getrolesList();
    this.getSaasSys()
    this.getDicts("sys_normal_disable").then((response) => {
      this.statusOptions = response.data;
    });
    this.getDicts("sys_user_sex").then((response) => {
      this.sexOptions = response.data;
    });
    this.getConfigKey("sys.user.initPassword").then((response) => {
      this.initPassword = response.msg;
    });
    // 查询节点
    this.getDicts("user_index_node").then((response) => {
      this.indexNodeOptions = response.data;
    });
    // 试用账号
    this.getDicts("is_probation_user").then((response) => {
      this.isProbationOptions = response.data;
      // 正式账号=>0
      // 试用账号=>1
    });
    // 试用时间范围;
    this.getDicts("user_probation_period").then((response) => {
      this.probationPeriodOptions = response.data;
    });
  },
  methods: {
    checkPermi,
    // 获取所属日期信息
    async getRefreshStatus () {
      let res = await getallRefreshStatus()
      this.refreshStatus = res.data
    },
    curTigger (val) {
      let itemVisible = "";
      this.columns.map((item) => {
        if (item.label == val) {
          itemVisible = item.visible;
        }
      });
      return itemVisible;
    },
    // 获取所属saas系统
    async getSaasSys () {
      let params = {
        context:null
      }
      let res = await getSaasSysApi(params)
      this.saasoptions = res.rows
    },
    // 账号到期时间选择
    clickTime (index, item) {
      this.curLi = index;
      if (
        item.name == "已到期" ||
        item.name == "进行中" ||
        item.name == "不限"
      ) {
        this.queryParams.expireStatus = item.value;
        this.queryParams.expireTimeStart = "";
        this.queryParams.expireTimeEnd = "";
      } else {
        this.queryParams.expireStatus = "";
      }
    },
    // 表格设置编辑
    async sureSet (val) {
      let params = val;
      let res = await sureSetApi(params);
      if (res.code == 200) {
        this.msgSuccess("设置成功");
      }
    },
    // 获取表格设置数据
    async getTabeSet () {
      let resInfo = await getInfo();
      let params = {
        userId: resInfo.user.userId,
        fixedColumns: "userId,userName,nickName",
      };
      let resSetApi = await getTabeSetApi(params);
      if (resSetApi.data && resSetApi.data.length) {
        this.columns = resSetApi.data;

      }
    },
    // 过滤角色
    formatRoles (row) {
      const aa = [];
      if (row.roles) {
        row.roles.map((item) => aa.push(item.roleName));
      }
      return aa.toString();
    },
    // 过滤数据查询范围
    formatPeri (row) {
      let resul = "";
      if (
        (row.probationPeriod == "" || row.probationPeriod == null) &&
        row.isProbation == 0
      ) {
        return "--";
      }
      this.probationPeriodOptions.map((item) => {
        if (row.probationPeriod == item.dictValue) {
          resul = item.dictLabel;
        }
      });
      return resul;
    },
    // 获取每天字数接口
    // async getdayNum () {
    //   let res = await getdayNumApi();
    //   this.everyFontNum = res.data;
    // },
    async getdayNum () {
      let params = {
        usedIfly:this.form.isUsedIfly
      }
      let res = await handleQueryApi(params)
      this.everyFontNum = res.rows;
    },
    // 扫描问题
    async refreshClick (row) {
      try {
        let params = {
          userId: row.userId,
        };
        row.scanLoading = true
        let res = await refresh(params);
        if (res.code == 200) {
          row.scanLoading = false
          this.msgSuccess(res.msg);
        } else {
          row.scanLoading = false
          this.msgError(res.msg);
        }
      } catch (error) {
        row.scanLoading = false
        console.log(error);
      }
    },
     // 复检问题
     async recheckClick (row) {
      try {
        let params = {
          customer: row.customer,
        };
        row.loading = true
        let res = await recheck(params);
        if (res.code == 200) {
          row.loading = false
          this.msgSuccess(res.msg);
        } else {
          this.msgError(res.msg);
          row.loading = false
        }
      } catch (error) {
        console.log(error);
          row.loading = false
      }
    },
    // 修改账号类型
    changePro (val) {
      if (val == 0) {
        // 正式账号
        this.form.probationPeriod = "";
        this.showPro = false;
      } else if (val == 1) {
        // 试用账号
        this.showPro = true;
      }
    },
    // 是否含有讯飞接口
    changeUsedId () {
      this.form.memberId = "";
        this.getdayNum()
    },
    // 个性化设置
    personalization (row) {
      this.dialogFormVisible = true;
      this.personalityForm.userId = row.userId;
      // this.personalityForm.status = row.status;
      this.getPersonSystem();
    },
    /** 查询用户列表 */
    getList () {
      this.loading = true;
      if (
        (this.curLi == 3 && !this.queryParams.expireTimeStart) ||
        (this.curLi == 3 && !this.queryParams.expireTimeEnd) ||
        (this.curLi == 3 &&
          this.queryParams.expireTimeStart > this.queryParams.expireTimeEnd)
      ) {
        this.$message.error("请选择自定义时间范围");
        this.loading = false;
      } else {
        listUser(this.addDateRange(this.queryParams, this.dateRange)).then(
          (response) => {
            this.userList = response.rows.map((res)=>{
              this.$set(res,'loading',false)
              this.$set(res,'scanLoading',false)
              return res
            })

            console.log(this.userList,'this.userList');
            this.total = response.total;
            this.loading = false;
          }
        );
        this.getRefreshStatus()
      }
    },
    /** 查询部门下拉树结构 */
    getTreeselect () {
      treeselect().then((response) => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode (value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick (data) {
      this.queryParams.deptId = data.id;
      this.getList();
    },
    // 用户状态修改
    handleStatusChange (row) {
      let text = row.status === "0" ? "启用" : "停用";
      this.$confirm(
        '确认要"' + text + '""' + row.userName + '"用户吗?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return changeUserStatus(row.userId, row.status);
        })
        .then(() => {
          this.msgSuccess(text + "成功");
        })
        .catch(function () {
          row.status = row.status === "0" ? "1" : "0";
        });
    },
    // 取消按钮
    cancel () {
      this.open = false;
      this.reset();
      this.$refs.form.clearValidate();
    },
    // 表单重置
    reset () {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: [],
        customer: "",
        indexNode: [],
        isProbation: undefined,
        probationPeriod: undefined,
        memberId: undefined,
        isUsedIfly: undefined,
        accountManager: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.userName = "";
      this.queryParams.nickName = "";
      this.queryParams.expireTimeStart = "";
      this.queryParams.expireTimeEnd = "";
      this.expireStatus = "";
      this.queryParams.context=""
      this.curLi = 0;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map((item) => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset();
      this.getTreeselect();
      getUser().then((response) => {
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.open = true;
        this.title = "添加用户";
        this.form.password = "";
        this.nosubmit = false
      });
    },
    // 获取用户角色
    async getrolesList () {
      let res = await getUser();
      if (res.code == 200) {
        this.rolesListData = res.roles;
      } else {
        this.msgError(res.msg);
      }
    },
    /** 修改按钮操作 */
    handleUpdate (row) {
      this.reset();
      this.getTreeselect();
      const userId = row.userId || this.ids;
      getUser(userId).then((response) => {
        this.form = response.data;
        this.postOptions = response.posts;
        this.roleOptions = response.roles;
        this.form.postIds = response.postIds;
        this.form.roleIds = response.roleIds;
        if (response.data.indexNode) {
          this.form.indexNode = response.data.indexNode
            .split(",")
            .map((i) => i.replace(/"/g, ""));
        }

        this.form.isProbation = response.data.isProbation.toString();
        if (this.form.isProbation == 1) {
          this.form.probationPeriod = response.data.probationPeriod.toString();
        } else {
          this.form.probationPeriod = "";
        }

        if (response.data.isUsedIfly) {
          this.form.isUsedIfly = response.data.isUsedIfly;

        }
        this.open = true;
        this.title = "修改用户";
        this.form.password = "";
        this.nosubmit = false
        if (this.form.isProbation == 1) {
          this.showPro = true;
        } else {
          this.showPro = false;
        }
        this.getdayNum()
      });
    },
    /** 重置密码按钮操作 */
    handleResetPwd (row) {
      this.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(({ value }) => {
          let newPd = this.$md5(value);

          resetUserPwd(row.userId, value).then((res) => {
            if (res.code == 200) {
              this.msgSuccess("修改成功，新密码是：" + value);
            } else {
              this.msgError(res.msg);
            }
          });
        })
        .catch(() => { });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.nosubmit = true
          if (this.form.isProbation == 0) {
            this.form.probationPeriod = "";
          }
          this.form.indexNode = this.form.indexNode.toString();
          if (this.form.userId != undefined) {
            updateUser(this.form).then((response) => {
              if (response.code == 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.nosubmit = false
                this.getList();
              } else {
                this.msgError(response.msg);
                this.open = false;
                this.nosubmit = false
              }
            });
          } else {
            if (this.form.isProbation == 0) {
              this.form.probationPeriod = "";
            }
            this.form.indexNode = this.form.indexNode.toString();
            addUser(this.form).then((response) => {
              if (response.code == 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.nosubmit = false
                this.getList();
              } else {
                this.msgError(response.msg);
                this.open = false;
                this.nosubmit = false
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      const userIds = row.userId || this.ids;
      this.$confirm(
        '是否确认删除用户编号为"' + userIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          return delUser(userIds);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport () {
      const queryParams = this.queryParams;
      this.$confirm("您确定要导出数据吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(function () {
          return exportUser(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    /** 导入按钮操作 */
    handleImport () {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate () {
      importTemplate().then((response) => {
        this.download(response.msg);
      });
    },
    // 文件上传中处理
    handleFileUploadProgress (event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess (response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm () {
      this.$refs.upload.submit();
    },
    handleFileSuccesslogo (response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.personalityForm.logo = response.fileName;
      this.listLogo = [];
      this.listLogo.push({
        name: response.url,
        response: {
          url: process.env.VUE_APP_BASE_API + response.fileName,
        },
      });
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
    },
    handleFileRemove (file, fileList) {
      this.personalityForm.logo = "";
      this.$refs.uploadlogo.clearFiles();
    },
    openUrl (file) {
      window.open(file.response.url);
    },
    handleRemove (file) {
      this.$refs.uploadlogo.clearFiles();
      this.personalityForm.logo = "";
    },
    masterFileMax () {
      this.$message.warning(`请最多上传 1 个文件。`);
    },
    handleFileSuccesslogoTwo (response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.personalityForm.backImage = response.fileName;
      this.listLogoTwo = [];
      this.listLogoTwo.push({
        name: response.url,
        response: {
          url: process.env.VUE_APP_BASE_API + response.fileName,
        },
      });
      this.$alert(response.msg, "导入结果", { dangerouslyUseHTMLString: true });
    },
    handleFileRemoveTwo (file, fileList) {
      this.personalityForm.backImage = "";
      this.$refs.uploadlogoTwo.clearFiles();
    },
    openUrlTwo (file) {
      window.open(file.response.url);
    },
    handleRemoveTwo (file) {
      this.$refs.uploadlogoTwo.clearFiles();
      this.personalityForm.backImage = "";
    },
    masterFileMaxTwo () {
      this.$message.warning(`请最多上传 1 个文件。`);
    },
    closePersonDialog () {
      this.$refs["personalityRef"].validate((valid) => {
        if (valid) {
          let arr = Object.keys(this.backData);
          if (arr.length == 0) {
            // 新增个性化设置
            let params = {
              userId: this.personalityForm.userId,
              name: this.personalityForm.name,
              context: this.personalityForm.context,
              // state: this.personalityForm.status,
              logo: this.personalityForm.logo,
              backImage: this.personalityForm.backImage,
            };
            closePersonDialogApi(params).then((res) => {
              if (res.code == 200) {
                this.msgSuccess("设置成功");
                this.dialogFormVisible = false;
                for (let key in this.personalityForm) {
                  this.$set(this.personalityForm, key, "");
                }
                this.$refs.uploadlogo.clearFiles();
                this.$refs.uploadlogoTwo.clearFiles();
              }else{
                this.$message.error(res.msg)
              }
            });
          } else {
            // 修改个性化设置
            let params = {
              id: this.personalityForm.id,
              name: this.personalityForm.name,
              context: this.personalityForm.context,
              // state: this.personalityForm.status,
              logo: this.personalityForm.logo,
              backImage: this.personalityForm.backImage,
            };
            editPersonDialogApi(params).then((res) => {
              if (res.code == 200) {
                this.msgSuccess("修改成功");
                this.dialogFormVisible = false;
                for (let key in this.personalityForm) {
                  this.$set(this.personalityForm, key, "");
                }
                this.$refs.uploadlogo.clearFiles();
                this.$refs.uploadlogoTwo.clearFiles();
              }else{
                this.$message.error(res.msg)
              }
            });
          }
        } else {
          this.$message.error("请输入完整信息");
        }
      });
    },
    cancelPersonDialog () {
      this.dialogFormVisible = false;
      for (let key in this.personalityForm) {
        this.$set(this.personalityForm, key, "");
      }
      this.$refs.uploadlogo.clearFiles();
      this.$refs.uploadlogoTwo.clearFiles();
    },
    // 根据用户获取个性化配置
    async getPersonSystem () {
      this.backData = [];
      this.listLogo = [];
      this.listLogoTwo = [];
      let params = {
        userId: this.personalityForm.userId,
      };
      let res = await getPersonSystemaApi(params);
      if (res.data) {
        this.backData = res.data;
      }
      let arr = Object.keys(this.backData);
      if (arr.length !== 0) {
        this.personalityForm = this.backData;
        this.personalityForm.id = this.backData.id;
        this.listLogo.push({
          name: res.data.logo,
          response: {
            url: process.env.VUE_APP_BASE_API + res.data.logo
          }
        });
        this.listLogoTwo.push({
          name: res.data.backImage,
          response: {
            url: process.env.VUE_APP_BASE_API + res.data.backImage,
          },
        });
      }
    },
  },
};
</script>
<style >
.el-message-box__header {
  border-bottom: solid 1px #ccc;
}
</style>

<style scoped lang="scss">
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: calc(100vh - 60px);
}
.app-container{
  background: #ffffff;
}
::v-deep .el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
}

.timeUl {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
  margin-top: 4px;

  li {
    margin-right: 16px;
    list-style: none;
    color: #606266;
    cursor: pointer;
    height: 28px;
    line-height: 28px;
    padding: 0 6px;
    border-radius: 3px;
  }

  li.active {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }

  li.activeErrorLi {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }
}

.totalView {
  // padding: 10px 0;
  margin-bottom: 20px;
  width: 100%;
  overflow: hidden;

  h2 {
    font-size: 18px;
    border-left: solid 3px #3d9ffe;
    margin-left: 25px;
    font-weight: normal;
    padding-left: 10px;
  }

  .totalBox {
    width: 100%;
    padding: 0 25px;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    div {
      // height: 115px;
      width: 24%;
      margin-right: 0.6%;
      box-sizing: border-box;
      text-align: center;
      // cursor: pointer;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 10px 20px;
      font-size: 14px;
      font-family:'Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif';
      color: #606266;

      &:last-child {
        margin-right: 0%;
      }

      span {
        text-align: left;
        display: block;
        margin-top: 10px;
      }

      p {
        font-weight:700;
        margin: 0;
        padding: 0;
      }
    }
  }
}
</style>
