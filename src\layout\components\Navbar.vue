<template>
  <div
    class="navbar"
    :style="{
      backgroundColor:
        sideTheme === 'theme-dark' && menuView == 1
          ? variables.menuBg
          : variables.menuLightBg,
    }"
  >
    <hamburger
      v-if="menuView == 0"
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <breadcrumb
      v-if="menuView == 0"
      id="breadcrumb-container"
      class="breadcrumb-container"
    />

    <div class="topbar-logo" v-if="menuView == 1">
      <logo v-if="showLogo" :collapse="false" />
    </div>

    <topbar
      v-if="menuView == 1"
      class="topbar-container"
      :style="{
        backgroundColor:
          sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg,
      }"
    />

    <div
      :class="
        sideTheme === 'theme-dark' && menuView == 1
          ? 'right-menu right-dark-menu'
          : 'right-menu'
      "
    >
      <template v-if="device !== 'mobile'">
        <search id="header-search" class="right-menu-item" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom"> -->
        <size-select id="size-select" class="right-menu-item hover-effect" />
        <!-- </el-tooltip> -->
      </template>
      <img v-hasPermi="['conentReview:plugin']" src="@/assets/images/download.svg" alt="" @click="downloadPlugin" class="img-plugin">
      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar" />
          <div class="user-name">{{ this.$store.state.user.name }}</div>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <div class="showInfo">
            <ul>
              <li style="text-align: left">
                <i
                  class="el-icon-user-solid"
                  style="color: #fff; margin-right: 4px"
                ></i
                >{{ this.$store.state.user.name }}
              </li>
              <li>
                有效期：{{
                  this.$store.state.user.expireTime == null
                    ? "永久"
                    : this.$store.state.user.expireTime
                }}
              </li>
            </ul>
          </div>
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <!-- <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item> -->
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import Topbar from "./../components/Topbar";
import variables from "@/assets/styles/variables.scss";
import Logo from "./Topbar/Logo";
import {pluginDownload} from "@/api/textReview"
import { downloadBlob } from "@/utils/index"
import moment from 'moment';
export default {
  components: {
    Breadcrumb,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    Topbar,
    Logo,
  },
  computed: {
    ...mapState({
      sideTheme: (state) => state.settings.sideTheme,
    }),
    ...mapGetters(["sidebar", "avatar", "device"]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    menuView() {
      return this.$store.state.settings.menuView;
    },
  },
  created() {},
  methods: {
    // 下载插件
    downloadPlugin() {
      pluginDownload().then((res) => {
        downloadBlob(res,`smartEye-${moment().format('YYYYMMDDHHmmss')}.zip`)
      })
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },

    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$store.dispatch("LogOut").then((res) => {
          var url = document.location.pathname;
          var index = url
            .substr(url.indexOf("/", url.indexOf("/") - 1) + 1)
            .substring(
              0,
              url
                .substr(url.indexOf("/", url.indexOf("/") - 1) + 1)
                .indexOf("/")
            );
          location.href = `${index}/login`;
          // sessionStorage.removeItem("idUrl");
          console.log("index", `${index}/login`);
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.img-plugin{
  margin: 0 20px 6px 0;
  width: 34px;
  cursor: pointer;
}
.showInfo {
  width: 100%;
  overflow: hidden;
  background: #283854;
  color: #fff;
  padding: 0 10px;
  margin-bottom: 6px;
  ul {
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    li {
      list-style: none;
      text-align: center;
      height: 34px;
      line-height: 34px;
    }
  }
}
.navbar {
  height: 60px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 4px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topbar-logo {
    float: left;
    margin-left: 5vw;
  }

  .topbar-container {
    float: left;
    margin-left: 20px;
    display: flex;
    flex-direction: row;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 60px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          display: inline-block;
          vertical-align: middle;
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .user-name {
          display: inline-block;
          vertical-align: middle;
          cursor: pointer;
          height: 40px;
          line-height: 40px;
          margin-left: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }

  .right-dark-menu {
    .right-menu-item {
      color: #ffffff;
    }
  }
}
</style>
