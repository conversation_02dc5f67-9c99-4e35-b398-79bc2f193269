<template>
  <div class="app-container home">
    <div class="homeBox">
      <el-form
        ref="form"
        class="queryForm"
        :model="queryForm"
        label-width="100px"
        :inline="true"
        v-if="isRouterAlive"
      >
        <el-row :gutter="20" style="margin-bottom: 10px">
          <el-col :span="12">
            <el-form-item label="信源类型：">
              <ul class="checkTypeUl">
                <li
                  v-for="item in medType"
                  :key="item.value"
                  :class="queryForm.type == item.value ? 'active' : ''"
                  @click="changeMedType(item.value)"
                >
                  {{ item.name }}
                </li>
              </ul>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <i
              class="el-icon-setting"
              @click="drawerOpen"
              style="
                font-size: 16px;
                vertical-align: middle;
                float: right;
                margin-right: 20px;
              "
            ></i>
            <el-form-item label="信息类型：">
              <ul class="checkTypeUl">
                <!-- <li
                  v-for="item in carrierData"
                  :key="item.value"
                  :class="
                    queryForm.infoType.includes(item.value) ? 'active' : ''
                  "
                  @click="changeNature(item.value)"
                >
                  {{ item.label }}
                </li> -->
                <li :class="queryForm.infoType.includes(1) ? 'active' : ''" @click="changeNature(1)">文章</li>
                <li v-hasPermi="['index:attachment']" :class="queryForm.infoType.includes(2) ? 'active' : ''" @click="changeNature(2)">附件</li>
                <li v-hasPermi="['index:audio']" :class="queryForm.infoType.includes(3) ? 'active' : ''" @click="changeNature(3)">音频</li>
                <li v-hasPermi="['index:pic']" :class="queryForm.infoType.includes(4) ? 'active' : ''" @click="changeNature(4)">图片</li>
                <li v-hasPermi="['index:video']" :class="queryForm.infoType.includes(5) ? 'active' : ''" @click="changeNature(5)">视频</li>
              </ul>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-bottom: 10px">
          <el-col :span="12">
            <el-form-item label="信源筛选：">
              <!-- 一级分类 -->
              <!--<ul class="checkTypeUl">
                <li v-for="(item, index) in checkType" @click="clickLiTwo(index)"
                  :class="curLiTwo == index ? 'active' : ''" :key="index">
                  {{ item.name }}
                  <i v-show="curLiTwo == index" class="el-icon-arrow-down"></i>
                  <p style="margin: 0; padding: 0; text-align: center" v-if="item.checkArrLen">
                    {{ item.checkArrLen }}
                  </p>
                </li>
                <li>
                  <i class="el-icon-setting" @click="drawerOpen" style="font-size: 16px; vertical-align: middle"></i>
                </li>
              </ul> -->

              <!-- 一级分类 -->
              <ul class="checkTypeUlCount">
                <li
                  v-for="(item, index) in checkType.slice(2)"
                  class="active"
                  @click="clickLiTwo(2)"
                  :key="index"
                >
                  <div class="checkArrButton">
                    {{ item.name }}
                    <i class="el-icon-arrow-down"></i>
                  </div>
                  <div v-if="item.checkArrLen" class="checkArrlength">
                    {{ item.checkArrLen }}
                  </div>
                </li>

                <!-- <li>
                  <i class="el-icon-setting" @click="drawerOpen" style="font-size: 16px; vertical-align: middle"></i>
                </li> -->
              </ul>
              <!-- 备案网站||媒体账号 -->
              <div
                class="showCheckTypeOne"
                v-show="showMed"
                ref="treeWrapThree"
              >
                <!-- append-to-body -->
                <div
                  class="second-check"
                  @click="secondCheck"
                  :class="secondAct ? 'active' : ''"
                >
                  全选
                </div>
                <ul
                  v-show="assignLoading"
                  v-loading="assignLoading"
                  style="height: 50px"
                ></ul>
                <ul v-show="!assignLoading" class="showTypeOne">
                  <li
                    v-for="(itema, index) in filterDataTree"
                    ref="dateTree"
                    @click="checkLiThrees(index, itema)"
                    :class="
                      (itema.tag &&
                        itema.webSiteAndMediaAccountList.length === 0) ||
                      itema.single.length > 0
                        ? 'active'
                        : ''
                    "
                    :key="itema.id"
                  >
                    <el-popover
                      placement="bottom-start"
                      width="400"
                      trigger="click"
                    >
                      <!-- 三级分类 -->
                      <div class="third-kinds">
                        <div>
                          <span
                            @click="checkAllThird(itema, index)"
                            :class="itema.checkAll ? 'active' : ''"
                            >全选</span
                          >
                        </div>
                        <div
                          v-for="(
                            itemb, indexb
                          ) in itema.webSiteAndMediaAccountList"
                          :key="indexb"
                          class="third-item"
                        >
                          <span
                            :class="itemb.tag ? 'active' : ''"
                            @click="checkSingle(itemb.id, index, indexb)"
                            >{{ itemb.name }}</span
                          >
                        </div>
                      </div>
                      <span
                        slot="reference"
                        class="showTypeSpan"
                        v-if="itema.webSiteAndMediaAccountList.length > 0"
                      >
                        {{ itema.name }}
                        <i class="el-icon-arrow-down"></i>
                      </span>
                    </el-popover>
                    <span
                      v-if="itema.webSiteAndMediaAccountList.length == 0"
                      class="showTypeSpan"
                    >
                      {{ itema.name }}
                    </span>
                  </li>
                </ul>
                <!-- <div style="text-align: right">
                  <el-button
                    type="primary"
                    @click="closeShowReview"
                    size="small"
                    >确定</el-button
                  >
                  <el-button size="small" @click="cancelShowReview"
                    >取消</el-button
                  >
                </div>-->
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专题筛选：">
              <!-- 一级分类 -->
              <ul class="checkTypeUlCount">
                <!-- <li v-for="(item, index) in checkType.slice(0,2)" @click="clickLiTwo(index)"
                  :class="curLiTwo == index ? 'active' : ''" :key="index">
                  {{ item.name }}
                  <i v-show="curLiTwo == index" class="el-icon-arrow-down"></i>
                  <p style="margin: 0; padding: 0; text-align: center" v-if="item.checkArrLen">
                    {{ item.checkArrLen }}
                  </p>
                </li> -->

                <li
                  @click="clickLiTwo(0)"
                  :class="curLiTwo == 0 ? 'active' : ''"
                >
                  <div class="checkArrButton">
                    {{ checkType[0].name }}
                    <i v-show="curLiTwo == 0" class="el-icon-arrow-down"></i>
                  </div>
                  <div v-if="checkType[0].checkArrLen" class="checkArrlength">
                    {{ checkType[0].checkArrLen }}
                  </div>
                </li>
                <li
                  v-if="queryForm.infoType != 4 && queryForm.infoType != 5"
                  @click="clickLiTwo(1)"
                  :class="curLiTwo == 1 ? 'active' : ''"
                >
                  <div class="checkArrButton">
                    {{ checkType[1].name }}
                    <i v-show="curLiTwo == 1" class="el-icon-arrow-down"></i>
                  </div>
                  <div v-if="checkType[1].checkArrLen" class="checkArrlength">
                    {{ checkType[1].checkArrLen }}
                  </div>
                </li>
              </ul>
              <!-- 系统 -->
              <div
                class="showCheckTypeOne"
                v-show="showSystem"
                ref="treeWrapOne"
              >
                <el-checkbox
                  :indeterminate="isIndeterminateSys"
                  v-model="checkSysAll"
                  @change="handleSystemAllChange"
                  >全选</el-checkbox
                >
                <el-checkbox-group
                  v-model="queryForm.checkTaskTypeId"
                  @change="changeSystemTypeTwo"
                >
                  <el-checkbox
                    v-for="(item, index) in getSysData"
                    :label="item.id"
                    :key="index"
                    >{{ item.name }}</el-checkbox
                  >
                </el-checkbox-group>
              </div>
              <!-- 自定义 -->
              <div
                class="showCheckTypeOne"
                v-show="showReview"
                ref="treeWrapTwo"
              >
                <el-checkbox
                  :indeterminate="isIndeterminateOur"
                  v-model="checkOurAll"
                  @change="handleOurAllChange"
                  >全选</el-checkbox
                >
                <el-checkbox-group
                  v-model="queryForm.checkTaskId"
                  @change="changeOurTypeTwo"
                >
                  <el-checkbox
                    v-for="(item, index) in ourData"
                    :label="item.id"
                    :key="index"
                    >{{ item.name }}</el-checkbox
                  >
                </el-checkbox-group>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="时间范围：">
              <timePick
                class="timePick"
                :timeRound.sync="queryForm.timeRound"
                :startTime.sync="queryForm.startTime"
                :endTime.sync="queryForm.endTime"
                :curLi.sync="curLi"
              ></timePick>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="dividerLine" style="margin-top: 10px" />
        <el-row>
          <el-col :span="24" style="text-align: center">
            <!-- <el-button v-for="(item, index) in btnList" :key="index" size="mini"
              :loading="item.name == '导出' ? exportLoading : false" @click="btnListClick(index)"
              :type="currtIndex == index ? 'primary' : ''">{{ item.name }}</el-button> -->

            <el-button
              @click="btnListClick(0)"
              icon="el-icon-search"
              size="mini"
              type="primary"
              >查询</el-button
            >
            <el-button
              @click="btnListClick(1)"
              icon="el-icon-upload2"
              :loading="exportLoading"
              size="mini"
              type="primary"
              >导出</el-button
            >
            <el-button
              size="mini"
              @click="exportSelf"
              >自定义导出字段</el-button
            >
            <el-button
              v-show="showReportView"
              v-hasPermi="['index:reportView']"
              @click="btnListClick(2)"
              icon="el-icon-view"
              size="mini"
              >报告预览</el-button
            >
          </el-col>
          <el-col
            :span="24"
            style="text-align: left; margin: 10px 0 10px 20px"
            v-show="false"
          >
            <el-button
              size="mini"
              type="primary"
              @click="refreshClick"
              :loading="freshLoading"
              >刷新</el-button
            >
            <p class="pData" v-show="reashP">{{ reashP }}</p>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="totalView">
      <h2 class="partTitle">总体概况</h2>
      <div class="dividerLine" />
      <OverviewCard
        :totalDataList="totalDataList"
        :loadingTwo="loadingTwo"
        :totalDataError="totalDataError"
        :checkwebName="checkwebName"
        :queryForm="queryForm"
        :questionTab="questionTab"
        :OverviewFlag="OverviewFlag"
      >
      </OverviewCard>
    </div>

    <div class="homeBoxTwo" v-loading="loading">
      <h2 class="partTitle">问题汇总</h2>
      <el-button
        size="mini"
        style="float: right;
        margin: 10px 20px 0 0;"
        v-show="tableData.length>0"
        :loading="exportSummaryLoading"
        @click="exportToExcel"
        >导出</el-button
      >
      <div class="dividerLine" />
      <div v-if="OverviewFlag">
        <el-radio-group
          v-model="questionTab"
          @change="changeques"
          style="margin: 0px 0px 10px 20px"
        >
          <el-radio-button :label="'11'">当前问题</el-radio-button>
          <el-radio-button
            :label="'12'"
            v-if="checkPermi(['index:historyquestion'])"
            >历史问题</el-radio-button
          >
        </el-radio-group>
      </div>

      <div class="homeTable">
        <el-table :data="tableData" stripe style="width: 100%">
          <el-table-column
            type="index"
            label="序号"
            :index="getIndex"
            align="center"
            width="80"
          ></el-table-column>

          <template v-if="OverviewFlag">
            <el-table-column
              prop="siteName"
              label="信源名称"
              align="center"
              :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column
              prop="host"
              label="备案域名"
              align="center"
              v-if="showwebcloumn"
              :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column
              prop="infoCount"
              label="全部错误数"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="severeCount"
              label="严重错误数"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="generalCount"
              label="一般错误数"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="weakCount"
              label="自定义错误数"
              align="center"
            ></el-table-column>
            <el-table-column key="option1" label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="goDetail(scope.row)"
                  >查看详情</el-button
                >
              </template>
            </el-table-column>
          </template>

          <template v-else>
            <el-table-column
              prop="name"
              label="信源名称"
              align="center"
              :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column
              prop="host"
              label="信源域名"
              align="center"
              v-if="showwebcloumn"
              :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column
              prop="errorCount"
              :label="`敏感${
                queryForm.infoType.join() == 4 ? '图片' : '视频'
              }数`"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="suspectCount"
              label="嫌疑数"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="notPassCount"
              label="不通过数"
              align="center"
            ></el-table-column>
            <el-table-column key="option2" label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="goDetailTwo(scope.row)"
                  >查看详情</el-button
                >
              </template>
            </el-table-column>
          </template>
        </el-table>
        <pagination
          v-show="total > 0"
          style="margin: 20px 0px; float: right"
          :total="total"
          :page.sync="pageNum"
          :limit.sync="pageSize"
          @pagination="getListData()"
        />
      </div>
    </div>
    <div
      class="customThem"
      @click="customClick"
      v-if="checkPermi(['index:customword'])"
    >
      <p>添加自定</p>
      <p>义词</p>
    </div>
    <!-- 自定义专题弹框 -->
    <el-dialog
      title="添加自定义词"
      width="600px"
      :visible.sync="dialogFormVisible"
    >
      <div slot="title" class="header-title">
        <span class="title-age">添加自定义词</span>
        <el-tooltip
          class="item"
          effect="light"
          placement="top-start"
          popper-class="tip-class"
        >
          <div slot="content" style="line-height: 24px; font-size: 12px">
            用户可自定义添加正确词、敏感词或者错
            <br />误词并进行应用监测；添加敏感词和错
            <br />误词时需提前维护对应的自定义专题
          </div>
          <span class="title-name">
            <i
              class="el-icon-question icon-report"
              style="color: #e6a23c; margin-left: 10px; cursor: pointer"
            ></i>
          </span>
        </el-tooltip>
      </div>
      <el-form
        :model="formCustom"
        label-width="120px"
        :rules="rules"
        ref="formCustomRules"
      >
        <el-form-item label-width="0" style="text-align: center">
          <el-radio-group v-model="tabPosition" @change="changeRadio">
            <el-radio-button label="1">添加正词</el-radio-button>
            <el-radio-button label="2">添加敏感词</el-radio-button>
            <el-radio-button label="3">添加错误词</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label-width="0">
          <p v-show="tabPosition == 1">
            说明：将词语添加为正词后，再出现该词语时，首页问题汇总、审校专题、
            文稿审校版块都将不会提示错误。
          </p>
          <p v-show="tabPosition == 2">
            说明：将词语添加为敏感词后，再出现该词语时，首页问题汇总、审校专题板块将会提示敏感，文稿审校版块将会提示错误但不会建议正确词语。
          </p>
          <p v-show="tabPosition == 3">
            说明：将未提示错误的词语添加入错误词库后，再出现该词语时，首页问题汇总、审校专题
            版块将会提示错误，文稿审校版块将会提示错误并给出建议正确词。
          </p>
        </el-form-item>
        <el-form-item
          label="正词："
          :prop="tabPosition == 1 ? 'properWord' : ''"
          v-show="tabPosition == 1"
        >
          <el-input
            type="textarea"
            placeholder="请输入正词,多个请用空格隔开"
            v-model="formCustom.properWord"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="敏感词："
          :prop="tabPosition == 2 ? 'sensitiveWord' : ''"
          v-show="tabPosition == 2"
        >
          <el-input
            type="textarea"
            placeholder="请输入敏感词,多个请用空格隔开"
            v-model="formCustom.sensitiveWord"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="错误词"
          :prop="tabPosition == 3 ? 'errorWord' : ''"
          v-show="tabPosition == 3"
        >
          <el-input
            type="textarea"
            v-model="formCustom.errorWord"
            placeholder="请输入错误词,多个请用空格隔开；错误词与正确词需要一一对应"
          />
        </el-form-item>
        <el-form-item
          label="建议词"
          :prop="tabPosition == 3 ? 'suggestWord' : ''"
          v-show="tabPosition == 3"
        >
          <el-input
            type="textarea"
            v-model="formCustom.suggestWord"
            placeholder="请输入建议词，多个请用空格隔开；建议词和错误词需要一一对应"
          />
        </el-form-item>
        <el-form-item
          :label="`${wordType}专题`"
          v-show="tabPosition == 2 || tabPosition == 3"
          :prop="
            (tabPosition == 2 ? 'checkTaskId' : '') ||
            (tabPosition == 3 ? 'checkTaskId' : '')
          "
        >
          <el-select
            v-model="formCustom.checkTaskId"
            :placeholder="`请选择${wordType}专题`"
            style="width: 100%"
            @mouseleave="leaveOption"
          >
            <!-- <div class="addType" @click="addMoreType" v-if="showAdd">
              <i class="el-icon-plus"></i> 添加分类
            </div>
            <div class="addTopName" v-else>
              <el-form :model="topicForm" :inline="true">
                <el-form-item prop="name">
                  <el-input
                    placeholder="请输入专题"
                    v-model="topicForm.name"
                    size="small"
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" size="small">保存</el-button>
                  <el-button size="small" @click="closeAddmoreType"
                    >取消</el-button
                  >
                </el-form-item>
              </el-form>
            </div>-->
            <el-option
              v-for="item in tasksenDataOption"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: center">
        <el-button type="primary" @click="suredialogFormVisible"
          >确 定</el-button
        >
        <el-button @click="canceldialogFormVisible">取 消</el-button>
      </div>
    </el-dialog>
    <el-drawer title="其他筛选设置" :visible.sync="drawer" :with-header="true">
      <!-- @closed="cancelShowReviewDialog" -->
      <!-- 备案网站||媒体账号 -->
      <div
        class="showCheckTypeOne addshowCheckTypeOne"
      >
        <div class="titleBox">
          <h3 class="elasticTitle addelasticTitle">
            {{ queryForm.type == 0 ? "备案网站" : "媒体账号" }}
          </h3>
          <el-checkbox v-model="chooseMe" @change="chooseMeEvent"
            >全选</el-checkbox
          >
        </div>
        <div
          class="second-check"
          @click="secondCheck"
          :class="secondAct ? 'active' : ''"
        >
          全选
        </div>
        <ul v-show="assignLoading" v-loading="assignLoading"></ul>
        <ul v-show="!assignLoading" class="showTypeOne">
          <li
            v-for="(itema, index) in filterDataTree"
            ref="dateTree"
            @click="checkLiThrees(index, itema)"
            :class="
              itema.webSiteAndMediaAccountList.length == 0
                ? itema.tag
                  ? 'active'
                  : ''
                : itema.single.length > 0
                ? 'active'
                : ''
            "
            :key="itema.id"
          >
            <el-popover placement="bottom-start" width="400" trigger="hover">
              <!-- 三级分类 -->
              <div class="third-kinds">
                <div>
                  <span
                    @click="checkAllThird(itema, index)"
                    :class="itema.checkAll ? 'active' : ''"
                    >全选</span
                  >
                </div>
                <div
                  v-for="(itemb, indexb) in itema.webSiteAndMediaAccountList"
                  :key="indexb"
                  class="third-item"
                >
                  <span
                    :class="itemb.tag ? 'active' : ''"
                    @click="checkSingle(itemb.id, index, indexb)"
                    >{{ itemb.name }}</span
                  >
                </div>
              </div>
              <span
                slot="reference"
                class="showTypeSpan"
                v-if="itema.webSiteAndMediaAccountList.length > 0"
              >
                {{ itema.name }}
                <i class="el-icon-arrow-down"></i>
              </span>
            </el-popover>
            <span
              v-if="itema.webSiteAndMediaAccountList.length == 0"
              class="showTypeSpan"
            >
              {{ itema.name }}
            </span>
          </li>
        </ul>
      </div>
      <!-- 系统专题 -->
      <div class="showCheckTypeOne addshowCheckTypeOne">
        <div class="titleBox">
          <h3 class="elasticTitle">系统专题</h3>
          <!-- <div
            class="second-check"
            :class="secondAct ? 'active' : ''"
            @click="secondCheckAll"
          >
            全选
          </div>-->
        </div>
        <el-checkbox
          :indeterminate="isIndeterminateSys"
          v-model="checkSysAll"
          @change="handleSystemAllChange"
          >全选</el-checkbox
        >
        <el-checkbox-group
          v-model="queryForm.checkTaskTypeId"
          @change="changeSystemTypeTwo"
        >
          <el-checkbox
            v-for="(item, index) in getSysData"
            :label="item.id"
            :key="index"
            >{{ item.name }}</el-checkbox
          >
        </el-checkbox-group>
      </div>
      <!-- 自定义专题 -->
      <div
        v-if="queryForm.infoType != 4 && queryForm.infoType != 5"
        class="showCheckTypeOne addshowCheckTypeOne"
      >
        <div class="titleBox">
          <h3 class="elasticTitle">自定义专题</h3>
        </div>
        <!-- 二级分类 -->
        <el-checkbox
          :indeterminate="isIndeterminateOur"
          v-model="checkOurAll"
          @change="handleOurAllChange"
          >全选</el-checkbox
        >
        <el-checkbox-group
          v-model="queryForm.checkTaskId"
          @change="changeOurTypeTwo"
        >
          <el-checkbox
            v-for="(item, index) in ourData"
            :label="item.id"
            :key="index"
            >{{ item.name }}</el-checkbox
          >
        </el-checkbox-group>
        <!-- <div style="text-align: right">
          <el-button type="primary" @click="closeShowOur" size="small"
            >确定</el-button
          >
          <el-button size="small" @click="cancelShowOur">取消</el-button>
        </div>-->
      </div>

      <div style="text-align: center">
        <el-button type="primary" @click="closeShowReviewTwo" size="small"
          >确定</el-button
        >
        <el-button size="small" @click="cancelShowReviewTwo">取消</el-button>
      </div>
    </el-drawer>

    <el-card v-show="progressFlag" class="processbox">
      <span class="progressSpan">导出进度：</span>
      <el-progress
        :text-inside="true"
        :stroke-width="17"
        status="warning"
        :percentage="progressPercent"
      >
      </el-progress>
    </el-card>
    <!-- 自定义表头 -->
    <CustomTable ref="custom"  :loading.sync="subLoading" :list.sync="list" :unselectList.sync="unselectList" :visible.sync="dialogVisible" @submits="submitField"></CustomTable>
  </div>
</template>

<script>
import CustomTable from '@/components/CustomTable/index.vue'
import {
  getReviewTwoDataApi,
  getCheckLiThreeDataApi,
  getWebDataApi,
  getMedDataApi,
  getTotalDataApi,
  getHistoryCount,
  getMediaCount,
  searchDataApi,
  // 获取审校专题二级数据
  checkTaskTree,
  // 获取媒体树数据
  checkMedTree,
  siteTypeTree,
  refresh,
  getreashPApi,
  searchHistory,
  mediaSearchDataApi,
  refreshApi,
  refTotal,
  historyExport,
  historyExportnew,
  getSystemTopicList,
  exportCheckDataApi,
  exportSummaryApi,
  getExportPercent,getUserExportHabits,saveUserExportHabits
} from "@/api/index";
import {
  getSensitiveClassApi,
  getSensitiveApi,
  addsensitiveWord,
  addTrueWord,
} from "@/api/system/autodict";
import { exportAllApi } from "@/api/conentReview/themMangeChase";
import { timeJson } from "@/utils/time.js";
import { checkPermi } from "@/utils/permission.js";
import { getBigTitleTwoApi } from "@/api/conentReview/themManTopic";
import { getBigTitleThreeApi } from "@/api/conentReview/themManTopic";
import { debounce } from "@/utils/boryou";
import timePick from "@/views/components/timePick";
import OverviewCard from "@/views/components/overviewCard";
// import exportloading from "@/components/exportloading ";
import moment from 'moment';
import { nanoid } from 'nanoid';
export default {
  name: "index",
  // components: {
  //   exportloading,
  // },
  components: {
    timePick,CustomTable,
    OverviewCard,
  },
  data() {
    return {
      dialogVisible: false,
      subLoading:false,
      list:[],
      unselectList:[],
      debounceAgain: debounce,
      progressPercent: 0,
      progressFlag: false,
      questionList: [
        { name: "当前问题", value: 11, loading: false },
        { name: "历史问题", value: 12, loading: false },
      ],
      queVal: "",
      questionTab: "11",
      isRouterAlive: true,
      showAdd: true,
      topicForm: {
        name: "",
      },
      // 试用时间范围(1为三个月，2为六个月，3为一年)
      probationPeriod: "",
      isProbation: "",
      // 错误词分类数据
      taskTypeNameOption: [],
      // 错误词专题数据
      tasksenDataOption: [],
      ourData: [],
      tabPosition: 1,
      reashP: "",
      // 错误类型
      wrongType: "",
      drawer: false,
      loadingTwo: false,
      loading: false,
      // formMedSearch: {},
      freshLoading: false,
      mediaDataTree: [],
      secondActMedia: false,
      websiteDataTree: [],
      exportLoading: false,
      secondAct: false,
      filterDataTree: [],
      checkAllTwo: false, //（疑似废弃）
      isIndeterminateTwo: false,
      checkAll: false,
      checkSysAll: true,
      checkOurAll: true,
      isIndeterminate: false,
      isIndeterminateSys: false,
      isIndeterminateOur: false,
      checkTaskIds: [], //备案网站||媒体账号的选中id
      chooseMe: false,
      queryForm: {
        type: "0",
        // 媒体
        assignIds: [],
        // 自定义专题
        checkTaskId: [],
        startTime: "",
        endTime: "",
        // 系统专题
        checkTaskTypeId: [],
        timeRound: 1,
        //信息类型
        infoType: [1],
      },
      getSysData: [],
      // timeOne: timeJson.threeMonth,
      medType: [
        { name: "备案网站", value: "0" },
        { name: "媒体账号", value: "1" },
      ],
      btnList: [
        { name: "查询" },
        { name: "导出" },
        //  { name: "报告预览" }
      ],
      // viewDisabled:false,
      currtIndex: 0,
      tableData: [],
      timelist: [
        { name: "今天", value: "0" },
        { name: "24小时", value: "1" },
        { name: "三天", value: "2" },
        { name: "七天", value: "3" },
        { name: "三十天", value: "4" },
        { name: "自定义", value: "5" },
      ],
      errorList: [
        { name: "全选" },
        { name: "严重错误" },
        { name: "一般错误" },
        { name: "疑似错误" },
      ],
      checkType: [
        { name: "系统专题", value: "0" },
        { name: "自定义专题", value: "1" },
        { name: "备案网站", value: "2" },
      ],
      curLi: 1,
      curLiTwo: 0,
      curLiThree: 0,
      errorLi: 1,
      pageSize: 10,
      pageNum: 1,
      total: 0,
      getCheckLiThreeData: [], //（疑似废弃）
      getWebData: [],
      showReview: false,
      showMed: false,
      totalDataList: {},
      totalDataError: 0,
      dialogFormVisible: false,
      formCustom: {
        // 正词
        properWord: "",
        // 敏感词
        sensitiveWord: "",
        // 错误词
        errorWord: "",
        // 建议词
        suggestWord: "",
        // 错误词分类
        // taskTypeId: "",
        // 错误词专题
        checkTaskId: "",
      },
      wordType: "敏感词",
      // 表单校验
      rules: {
        properWord: [
          { required: true, message: "正词不能为空", trigger: "blur" },
          {
            max: 500,
            message: "正词长度不能超过500个字符",
            trigger: "blur",
          },
        ],
        sensitiveWord: [
          { required: true, message: "敏感词不能为空", trigger: "blur" },
          {
            max: 500,
            message: "敏感词长度不能超过500个字符",
            trigger: "blur",
          },
        ],
        errorWord: [
          { required: true, message: "错误词不能为空", trigger: "blur" },
          {
            max: 500,
            message: "错误词长度不能超过500个字符",
            trigger: "blur",
          },
        ],
        suggestWord: [
          { required: true, message: "建议词不能为空", trigger: "blur" },
        ],
        // taskTypeId: [
        //   { required: true, message: "错误词分类不能为空", trigger: "blur" },
        // ],
        checkTaskId: [
          { required: true, message: "错误词专题不能为空", trigger: "blur" },
        ],
      },
      showSystem: false,
      checkwebName: "检测网站数",
      showwebcloumn: true,
      // 系统专题全部选中的id
      checkIdAllSys: [],
      // 自定义专题全部选中的id
      checkOurAllId: [],
      showReportView: true,
      carrierData: [
        { label: "文章", value: 1 },
        { label: "附件", value: 2 },
        { label: "音频", value: 3 },
        { label: "图片", value: 4 },
        { label: "视频", value: 5 },
      ],
      OverviewFlag: true, //总体概况显示，true为文章+附件，false为其他
      assignLoading: false, //备案网站、媒体账号加载
      exportSummaryLoading: false, //首页问题汇总导出loading,
    };
  },
  watch: {
    checkTaskIds: {
      handler(newv, oldv) {
        this.queryForm.assignIds = newv;
      },
    },
    checkSysAll: {
      handler(newval, oldval) {
        if (newval == true) {
          this.queryForm.checkTaskTypeId = this.checkIdAllSys;
          this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
          this.isIndeterminateSys = false;
        } else {
          this.queryForm.checkTaskTypeId = [];
          this.checkType[0].checkArrLen = 0;
          this.isIndeterminateSys = false;
        }
      },
      immediate: true,
    },
    checkOurAll: {
      handler(newval, oldval) {
        if (newval == true) {
          this.queryForm.checkTaskId = this.checkOurAllId;
          this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
          this.isIndeterminateOur = false;
        } else {
          this.queryForm.checkTaskId = [];
          this.checkType[1].checkArrLen = 0;
          this.isIndeterminateOur = false;
        }
      },
      immediate: true,
    },

    // 详情页新开窗口，用不到了
    $route: {
      handler: function (newval, oldVal) {
        if (
          oldVal.name == "indexList" ||
          oldVal.name == "detailPage" ||
          oldVal.name == "Index"
        ) {
          this.isRouterAlive = true;
        } else {
          this.queryForm.type = "0";
          this.curLiTwo = 0;
          this.curLi = 1;
          this.queryForm.checkTaskTypeId = [];
          this.queryForm.assignIds = [];
          this.queryForm.checkTaskId = [];
          this.checkType[0].checkArrLen = 0;
          this.checkType[1].checkArrLen = 0;
          this.checkType[2].checkArrLen = 0;
        }
      },
      // 深度观察监听
      deep: true,
    },
  },
  created() {
    // this.getSiteTypeTree()
    setTimeout(() => {
      this.btnListClick(0);
    }, 500);
    this.getreashP();
    this.getSensitiveClass();
    this.getTopdata();
    this.getSystemData();
    this.getOurData();
    this.getTreeData();
    this.getTime();
  },
  mounted() {
    // 全局点击事件
    document.addEventListener("mouseup", (e) => {
      let treesys = this.$refs.treeWrapOne;
      let treeself = this.$refs.treeWrapTwo;
      let treeweb = this.$refs.treeWrapThree;

      if (treesys) {
        // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
        if (!treesys.contains(e.target)) {
          this.showSystem = false;
        }
      }
      if (treeself) {
        // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
        if (!treeself.contains(e.target)) {
          this.showReview = false;
        }
      }
      if (treeweb) {
        // 判断鼠标点击的区域是否在tree里面，否则关闭弹窗
        if (!treeweb.contains(e.target)) {
          this.showMed = false;
        }
      }
    });

    if (window.history && window.history.pushState) {
      // 向历史记录中插入了当前页
      history.pushState(null, null, document.URL);
      window.addEventListener("popstate", this.goBack, false);
    }
  },

  beforeRouteLeave(to, from, next) {
    if (to.path == "/404") {
      console.log(12);
      return false;
    }
    next();
  },
  // mounted () {
  //     if (window.history && window.history.pushState) {
  //         // 向历史记录中插入了当前页
  //         history.pushState(null, null, document.URL);
  //         window.addEventListener('popstate', this.goBack, false);
  //     }
  // },
  destroyed() {
    window.removeEventListener("popstate", this.goBack, false);
  },
  methods: {
    exportSelf() {
      this.dialogVisible = true
      getUserExportHabits().then((res) => {
        this.list = res.data.check
        this.unselectList = res.data.unCheck
      })
    },
    // 设置自定义字段
    submitField() {
      saveUserExportHabits({check: this.list, unCheck: this.unselectList}).then((res)=>{
        this.subLoading = false
        this.dialogVisible = false
        this.$message.success('操作成功')
      }).catch(()=>{
        this.subLoading = false
      })
    },
    downloadBlob(res,fileName){
      const blob = res;
          const reader = new FileReader();
          reader.readAsDataURL(blob);
          reader.onload = (e) => {
            const dataUrl = e.target.result;
            const head = dataUrl.split(",")[0];

            if (head == "data:application/json;base64") {
              const base64Data = dataUrl.split(",")[1]; // 提取Base64编码数据
              const decodedData = window.atob(base64Data); // 解码Base64数据
              const jsonStr = decodeURIComponent(escape(decodedData)); // 使用escape编码二进制字符串，然后用decodeURIComponent进行解码
              const jsonObject = JSON.parse(jsonStr); // 解析JSON字符串
              this.$message({
                type: "error",
                message: jsonObject.msg,
              });
            } else {
              const a = document.createElement("a");
              // 生成文件名
              a.download = fileName;
              a.href = e.target.result;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            }
          };
    },

    exportToExcel() {
      this.exportSummaryLoading = true;
      const exportId = nanoid() //用生nanoid成随机exportId
      let params = {
        ...JSON.parse(sessionStorage.getItem("queryForm", params)),
        history: this.questionTab=='11'?1:2,
        exportId,
      };
      this.getExportProgress(exportId)
      exportSummaryApi(params).then((res) => {
        this.exportSummaryLoading = false;
        const fileName=`${ this.getLabelByValue(params.infoType.join()) }汇总-${moment().format('YYYYMMDDHHmmss')}.xlsx`
        this.downloadBlob(res, fileName)
      }).catch((err) => {
        this.exportSummaryLoading = false;
      });
    },
    getLabelByValue(inputValue) {
      const inputNumber = Number(inputValue);
      return this.carrierData.find(item => item.value === inputNumber)?.label || null;
    },
    goBack() {
      // console.log("点击了浏览器的返回按钮");
      history.pushState(null, null, document.URL);
    },
    checkPermi,
    // tab切换问题汇总||历史问题汇总
    async changeques() {
      this.loading = true;
      this.pageNum = 1;
      // this.getTotalData();
      this.getListData();
    },
    addMoreType() {
      this.showAdd = false;
    },
    closeAddmoreType() {
      this.topicForm.name = "";
      this.showAdd = true;
    },
    leaveOption() {},
    // 获取系统专题数据
    async getSystemData() {
      let params = { infoType: this.queryForm.infoType };
      let res = await getSystemTopicList(params);
      this.getSysData = res.data;
      this.checkIdAllSys = this.getSysData.map((item) => item.id);
      this.handleSystemAllChange(true);
    },
    getTime() {
      this.$store.dispatch("GetInfo").then((res) => {
        let expireTime = res.user.expireTime;
        // 试用时间范围
        this.probationPeriod = res.user.probationPeriod;
        this.isProbation = res.user.isProbation;
        if (expireTime) {
          let OldTime = /\d{4}-\d{1,2}-\d{1,2}/g.exec(expireTime);
          const myDate = new Date();
          const Y = myDate.getFullYear();
          const M = myDate.getMonth() + 1;
          const D = myDate.getDate();
          const curDay = Y + "-" + M + "-" + D;
          let timeResult = this.dateDiff(OldTime[0], curDay);
          if (timeResult > 0 && timeResult < 7) {
            this.$notify({
              title: "到期提醒",
              message: `账号还有${timeResult}天过期`,
              type: "warning",
              offset: 50,
            });
          }
        }
      });
    },
    //日期1减去日期2的天数.
    dateDiff(d1, d2) {
      var day = 24 * 60 * 60 * 1000;
      try {
        var dateArr = d1.split("-");
        var checkDate = new Date();
        checkDate.setFullYear(dateArr[0], dateArr[1] - 1, dateArr[2]);
        var checkTime = checkDate.getTime();

        var dateArr2 = d2.split("-");
        var checkDate2 = new Date();
        checkDate2.setFullYear(dateArr2[0], dateArr2[1] - 1, dateArr2[2]);
        var checkTime2 = checkDate2.getTime();

        var cha = (checkTime - checkTime2) / day;
        return Math.round(cha);
      } catch (e) {
        return false;
      }
    },
    // 获取错误词分类数据
    async getSensitiveClass() {
      let res = await getSensitiveClassApi();
      this.taskTypeNameOption = res.rows;
    },
    // 获取敏感词和错误词专题数据
    async getTopdata() {
      let res = await getSensitiveApi();
      this.tasksenDataOption = res.rows;
    },
    changeRadio(val) {
      if (val == 2) {
        this.wordType = "敏感词";
      } else if (val == 3) {
        this.wordType = "错误词";
      }
    },
    // 获取刷新按钮后面数据
    async getreashP() {
      let res = await getreashPApi();
      this.reashP = res.msg;
    },
    drawerOpen() {
      this.drawer = true;
      this.showReview = false;
      this.showMed = false;
      //this.getDiffChange();//关闭掉，每次打开抽屉不用重新请求
    },
    // 刷新
    async refreshClick() {
      try {
        this.freshLoading = true;
        let res = await refresh({ id: null });
        if (res.code == 1000) {
          this.freshLoading = false;
          this.msgSuccess(res.msg);
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 获取媒体树
    getSiteTypeTree() {
      // type:1 网站；2 媒体
      siteTypeTree({ type: 1 }).then((res) => {
        this.$nextTick(() => {
          this.websiteDataTree = res.data;
          this.websiteDataTree.map((item) => {
            item.tag = false;
            item.checkAll = false;
            item.single = [];
            if (item.webSiteAndMediaAccountList.length != 0) {
              item.webSiteAndMediaAccountList.map((itemb) => {
                itemb.tag = false;
              });
            }
          });
        });
      });
      siteTypeTree({ type: 2 }).then((res) => {
        this.$nextTick(() => {
          this.mediaDataTree = res.data;
          this.mediaDataTree.map((item) => {
            item.tag = false;
            item.checkAll = false;
            item.single = [];
            if (item.mediaAccountList.length != 0) {
              item.mediaAccountList.map((itemb) => {
                itemb.tag = false;
              });
            }
          });
        });
      });
    },
    // 二级全选
    secondCheck() {
      this.secondAct = !this.secondAct;
      if (this.secondAct) {
        this.checkTaskIds = [];
        this.filterDataTree.map((item) => {
          item.tag = true;
          item.checkAll = true;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = true));
            let id = item.webSiteAndMediaAccountList.map((itemb) => itemb.id);
            item.single = id;
            this.checkTaskIds.push(...id);
            this.queryForm.assignIds = this.checkTaskIds;
            this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          }
          // else {
          //   this.checkTaskIds.push(item.id);
          //   this.queryForm.assignIds = this.checkTaskIds;
          //   this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          // }
        });
        this.$forceUpdate();
      } else {
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
            item.single = [];
          }
          this.checkTaskIds = [];
          this.queryForm.assignIds = this.checkTaskIds;
          this.checkType[2].checkArrLen = "";
        });
        this.$forceUpdate();
      }
    },
    //  draw全选（弃用）
    secondCheckAll(val) {
      // 系统全选
      // debugger;
      this.checkSysAll = !this.checkSysAll;
      if (this.checkSysAll) {
        this.checkSysAll = true;
        let aaa = [];
        for (let i = 0; i < this.getSysData.length; i++) {
          aaa.push(this.getSysData[i].id);
        }
        this.queryForm.checkTaskTypeId = aaa;
        this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
        this.isIndeterminateSys = false;
      } else {
        this.checkSysAll = false;
        this.queryForm.checkTaskTypeId = [];
        this.checkType[0].checkArrLen = 0;
        this.isIndeterminateSys = false;
      }
      // 自定义全选
      this.checkOurAll = !this.checkOurAll;
      if (this.checkOurAll) {
        let aaa = [];
        for (let i = 0; i < this.ourData.length; i++) {
          aaa.push(this.ourData[i].id);
        }
        this.queryForm.checkTaskId = aaa;
        this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
        this.isIndeterminateOur = false;
      } else {
        this.queryForm.checkTaskId = [];
        this.checkType[1].checkArrLen = 0;
        this.isIndeterminateOur = false;
      }
      // 网站||媒体全选
      this.secondAct = !this.secondAct;
      if (this.secondAct) {
        this.filterDataTree.map((item) => {
          item.tag = true;
          item.checkAll = true;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = true));
            let id = item.webSiteAndMediaAccountList.map((itemb) => itemb.id);
            item.single = id;
            this.checkTaskIds.push(...id);
            this.queryForm.assignIds = this.checkTaskIds;
            this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          } else {
            this.checkTaskIds.push(item.id);
            this.queryForm.assignIds = this.checkTaskIds;
            this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          }
        });
        this.$forceUpdate();
      } else {
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
            item.single = [];
          }
          this.checkTaskIds = [];
          this.queryForm.assignIds = this.checkTaskIds;
          this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        });
        this.$forceUpdate();
      }
    },
    // draw全选
    chooseMeEvent() {
      if (this.chooseMe == true) {
        // 系统全选
        this.checkSysAll = true;
        if (this.checkSysAll == true) {
          let aaa = [];
          for (let i = 0; i < this.getSysData.length; i++) {
            aaa.push(this.getSysData[i].id);
          }
          this.queryForm.checkTaskTypeId = aaa;
          this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
          this.isIndeterminateSys = false;
        }
        // 自定义全选
        this.checkOurAll = true;
        if (this.checkOurAll == true) {
          let aaa = [];
          for (let i = 0; i < this.ourData.length; i++) {
            aaa.push(this.ourData[i].id);
          }
          this.queryForm.checkTaskId = aaa;
          this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
          this.isIndeterminateOur = false;
        }
        // 网站||媒体全选
        this.secondAct = true;
        if (this.secondAct == true) {
          this.filterDataTree.map((item) => {
            item.tag = true;
            item.checkAll = true;
            if (item.webSiteAndMediaAccountList.length > 0) {
              item.webSiteAndMediaAccountList.map(
                (itemb) => (itemb.tag = true)
              );
              let id = item.webSiteAndMediaAccountList.map((itemb) => itemb.id);
              item.single = id;
              this.checkTaskIds.push(...id);
              this.queryForm.assignIds = this.checkTaskIds;
              this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
            } else {
              this.checkTaskIds.push(item.id);
              this.queryForm.assignIds = this.checkTaskIds;
              this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
            }
          });
        }
        this.$forceUpdate();
      } else {
        // 系统全不选
        this.checkSysAll = false;
        this.queryForm.checkTaskTypeId = [];
        this.checkType[0].checkArrLen = 0;
        this.isIndeterminateSys = false;
        // 自定义全不选
        this.checkOurAll = false;
        this.queryForm.checkTaskId = [];
        this.checkType[1].checkArrLen = 0;
        this.isIndeterminateOur = false;
        // 网站||媒体全部选
        this.secondAct = false;
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          if (item.webSiteAndMediaAccountList.length > 0) {
            item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
            item.single = [];
          }
          this.checkTaskIds = [];
          this.queryForm.assignIds = this.checkTaskIds;
          this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        });
        this.$forceUpdate();
      }
    },
    // 三级单选
    checkSingle(item, index, indexb) {
      this.showMed = true;
      this.filterDataTree[index].webSiteAndMediaAccountList[indexb].tag =
        !this.filterDataTree[index].webSiteAndMediaAccountList[indexb].tag;
      if (this.filterDataTree[index].webSiteAndMediaAccountList[indexb].tag) {
        this.filterDataTree[index].single.push(item);
        this.checkTaskIds.push(item);
        this.queryForm.assignIds = this.checkTaskIds;
        this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
      } else {
        if (this.checkTaskIds.indexOf(item) != -1) {
          this.checkTaskIds.splice(this.checkTaskIds.indexOf(item), 1);
          this.queryForm.assignIds = this.checkTaskIds;
          this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        }
        if (this.filterDataTree[index].single.indexOf(item) != -1) {
          this.filterDataTree[index].single.splice(
            this.filterDataTree[index].single.indexOf(item),
            1
          );
        }
      }
      if (
        this.filterDataTree[index].single.length ==
        this.filterDataTree[index].webSiteAndMediaAccountList.length
      ) {
        this.filterDataTree[index].checkAll = true;
      } else {
        this.filterDataTree[index].checkAll = false;
      }
      this.$forceUpdate();
    },
    // 全选三级
    checkAllThird(item, index) {
      this.showMed = true;
      this.filterDataTree[index].checkAll =
        !this.filterDataTree[index].checkAll;
      let id = this.filterDataTree[index].webSiteAndMediaAccountList.map(
        (item) => {
          return item.id;
        }
      );
      if (this.filterDataTree[index].checkAll) {
        // 全选
        this.checkTaskIds.push(...id);
        this.checkTaskIds = [...new Set(this.checkTaskIds)];
        this.queryForm.assignIds = this.checkTaskIds;
        this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        this.filterDataTree[index].single = id;
        this.filterDataTree[index].webSiteAndMediaAccountList.map(
          (item) => (item.tag = true)
        );
      } else {
        // 全不选
        this.filterDataTree[index].webSiteAndMediaAccountList.map(
          (item) => (item.tag = false)
        );
        this.filterDataTree[index].single = [];
        id.map((item) => {
          if (this.checkTaskIds.indexOf(item) != -1) {
            this.checkTaskIds.splice(this.checkTaskIds.indexOf(item), 1);
          }
        });
        this.queryForm.assignIds = this.checkTaskIds;
        // debugger;
        this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
      }
      this.$forceUpdate();
    },
    customClick() {
      for (let key in this.formCustom) {
        this.$set(this.formCustom, key, "");
      }
      this.dialogFormVisible = true;
      this.tabPosition = 1;
    },
    // 自定义专题确定按钮
    suredialogFormVisible() {
      this.$refs["formCustomRules"].validate((valid) => {
        if (valid) {
          if (this.tabPosition == 1) {
            // 添加正词
            let params = {
              properWord: this.formCustom.properWord,
            };
            addTrueWord(params).then((res) => {
              if (res.code == 200) {
                this.msgSuccess("新增正词成功");
                this.dialogFormVisible = false;
              } else if (res.code == 500) {
                this.msgError(res.msg);
                this.dialogFormVisible = true;
              }
            });
          } else if (this.tabPosition == 2) {
            // 添加敏感词
            if (this.formCustom.sensitiveWord && this.formCustom.checkTaskId) {
              let params = {
                problemType: 1,
                problemWord: this.formCustom.sensitiveWord,
                checkTaskId: this.formCustom.checkTaskId,
              };
              addsensitiveWord(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增敏感词成功");
                  this.dialogFormVisible = false;
                } else if (res.code == 500) {
                  this.msgError(res.msg);
                  this.dialogFormVisible = true;
                }
              });
            } else {
              this.$message.error("请输入完整信息");
            }
          } else {
            // 添加错误词
            if (
              this.formCustom.errorWord &&
              this.formCustom.suggestWord &&
              this.formCustom.checkTaskId
            ) {
              let params = {
                problemType: 2,
                problemWord: this.formCustom.errorWord,
                suggestWord: this.formCustom.suggestWord,
                checkTaskId: this.formCustom.checkTaskId,
              };
              addsensitiveWord(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增错误词成功");
                  this.dialogFormVisible = false;
                } else if (res.code == 500) {
                  this.msgError(res.msg);
                  this.dialogFormVisible = true;
                }
              });
            } else {
              this.$message.error("请输入完整信息");
            }
          }
        } else {
          this.$message.error("请输入完整信息");
        }
      });
      // this.$refs["form"].validate((valid) => {
      //   if (valid) {
      //   }
      // });
    },
    // 取消自定义专题按钮
    canceldialogFormVisible() {
      for (let key in this.formCustom) {
        this.$set(this.formCustom, key, "");
      }
      this.dialogFormVisible = false;
    },
    //   三级全选（疑似废弃）
    handleCheckAllChangeTwo(e) {
      if (e == true) {
        let aaa = [];
        for (let i = 0; i < this.getCheckLiThreeData.length; i++) {
          aaa.push(this.getCheckLiThreeData[i].id);
        }
        this.queryForm.checkTaskId = aaa;
        this.isIndeterminateTwo = false;
      } else {
        this.queryForm.checkTaskId = [];
        this.isIndeterminateTwo = false;
      }
    },
    //   媒体全选（疑似废弃）
    handleCheckAllChange(val) {
      if (val == true) {
        let aaa = [];
        for (let i = 0; i < this.getWebData.length; i++) {
          aaa.push(this.getWebData[i].id);
        }
        this.queryForm.assignIds = aaa;
        this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        this.isIndeterminate = false;
      } else {
        this.queryForm.assignIds = [];
        this.checkType[2].checkArrLen = 0;
        this.isIndeterminate = false;
      }
    },
    //   系统专题全选
    handleSystemAllChange(val) {
      if (val == true) {
        let aaa = [];
        for (let i = 0; i < this.getSysData.length; i++) {
          aaa.push(this.getSysData[i].id);
        }
        this.queryForm.checkTaskTypeId = aaa;
        this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
        this.isIndeterminateSys = false;
      } else {
        this.queryForm.checkTaskTypeId = [];
        this.checkType[0].checkArrLen = 0;
        this.isIndeterminateSys = false;
      }
    },
    //   自定义专题全选
    handleOurAllChange(val) {
      if (val == true) {
        let aaa = [];
        for (let i = 0; i < this.ourData.length; i++) {
          aaa.push(this.ourData[i].id);
        }
        this.queryForm.checkTaskId = aaa;
        this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
        this.isIndeterminateOur = false;
      } else {
        this.queryForm.checkTaskId = [];
        this.checkType[1].checkArrLen = 0;
        this.isIndeterminateOur = false;
      }
    },
    // 媒体确定选中个数
    closeShowReview() {
      this.queryForm.assignIds = this.checkTaskIds;
      this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
      this.showMed = false;
    },
    closeShowMed() {
      this.showMed = false;
    },
    closeShowSystem() {
      this.showSystem = false;
      this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
    },
    closeShowOur() {
      this.showReview = false;
    },
    //疑似废弃
    cancelShowReview() {
      this.showMed = false;
      this.filterDataTree.map((item) => {
        item.tag = false;
        item.checkAll = false;
        if (item.webSiteAndMediaAccountList.length > 0) {
          item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
          item.single = [];
        }
        this.checkTaskIds = [];
        this.secondAct = false;
        this.checkType[2].checkArrLen = "";
      });
      this.$forceUpdate();
      this.queryForm.assignIds = [];
      this.checkAllTwo = false;
    },
    // 取消
    cancelShowReviewTwo() {
      this.checkTaskIds = [];
      // 系统专题清空
      this.checkSysAll = false;
      this.queryForm.checkTaskTypeId = [];
      this.checkType[0].checkArrLen = "";
      // 自定义专题清空
      this.checkOurAll = false;
      this.queryForm.checkTaskId = [];
      this.checkType[1].checkArrLen = "";
      // 备案网站||媒体账号清空
      this.secondAct = false;
      this.filterDataTree.map((item) => {
        item.tag = false;
        item.checkAll = false;
        if (item.webSiteAndMediaAccountList.length > 0) {
          item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
          item.single = [];
        }
        this.checkTaskIds = [];
      });
      this.queryForm.assignIds = [];
      this.checkType[2].checkArrLen = "";
      this.$forceUpdate();
      this.drawer = false;
    },
    // 取消draw
    cancelShowReviewDialog() {
      this.checkTaskIds = [];
      // 系统专题清空
      this.checkSysAll = false;
      this.queryForm.checkTaskTypeId = [];
      this.checkType[0].checkArrLen = "";
      // 自定义专题清空
      this.checkOurAll = false;
      this.queryForm.checkTaskId = [];
      this.checkType[1].checkArrLen = "";
      // 备案网站||媒体账号清空
      this.secondAct = false;
      this.filterDataTree.map((item) => {
        item.tag = false;
        item.checkAll = false;
        if (item.webSiteAndMediaAccountList.length > 0) {
          item.webSiteAndMediaAccountList.map((itemb) => (itemb.tag = false));
          item.single = [];
        }
        this.checkTaskIds = [];
      });
      this.queryForm.assignIds = [];
      this.checkType[2].checkArrLen = "";
      this.$forceUpdate();
      this.drawer = false;
    },
    // 确定draw
    closeShowReviewTwo() {
      this.queryForm.assignIds = this.checkTaskIds;
      this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
      this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
      this.drawer = false;
    },
    cancelShowMed() {
      this.checkAll = false;
      this.isIndeterminate = false;
      this.queryForm.assignIds = [];
      this.checkType[2].checkArrLen = 0;
      this.showMed = false;
    },
    // 取消系统专题选中项目
    cancelShowSystem() {
      this.checkSysAll = false;
      this.isIndeterminateSys = false;
      this.queryForm.checkTaskTypeId = [];
      this.checkType[0].checkArrLen = "";
      this.showSystem = false;
    },
    // 取消自定义专题选中项目
    cancelShowOur() {
      this.checkOurAll = false;
      this.isIndeterminateOur = false;
      this.queryForm.checkTaskId = [];
      this.checkType[1].checkArrLen = 0;
      this.showReview = false;
    },
    async changeMedType(val) {
      if (val == this.queryForm.type) return;
      this.queryForm.type = val;
      this.checkTaskIds = [];
      this.queryForm.assignIds = [];
      this.checkType[2].checkArrLen = "";
      if (val == 0) {
        this.checkType[2].name = "备案网站";
      } else {
        this.$nextTick((_) => {
          this.checkType[2].name = "媒体账号";
        });
      }
      this.secondAct = false;
      // this.getDiffChange();
      this.getTreeData();
    },
    //   点击打开审校和媒体弹框（疑似废弃）
    changeCheckTypeOne(value) {
      let checkedCount = value.length;
      this.checkAllTwo = checkedCount === this.getCheckLiThreeData.length;
      this.isIndeterminateTwo =
        checkedCount > 0 && checkedCount < this.getCheckLiThreeData.length;
      this.queryForm.checkTaskId = value;
    },
    // 自定义反选（疑似废弃）
    changeCheckTypeTwo(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.getWebData.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.getWebData.length;
      this.queryForm.assignIds = value;
      this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
    },
    // 系统反选
    changeSystemTypeTwo(value) {
      let checkedCount = value.length;
      // this.checkAll = checkedCount === this.getSysData.length;
      this.isIndeterminateSys =
        checkedCount > 0 && checkedCount < this.getSysData.length;
      this.queryForm.checkTaskTypeId = value;
      this.checkType[0].checkArrLen = this.queryForm.checkTaskTypeId.length;
    },
    // 自定义专题反选
    changeOurTypeTwo(value) {
      let checkedCount = value.length;
      // this.checkOurAll = checkedCount === this.ourData.length;
      this.isIndeterminateOur =
        checkedCount > 0 && checkedCount < this.ourData.length;
      this.queryForm.checkTaskId = value;
      this.checkType[1].checkArrLen = this.queryForm.checkTaskId.length;
    },
    // 获取审校专题二级数据
    async getTreeData() {
      this.assignLoading = true;
      let params = {
        type: this.queryForm.type,
        isCollect: 1,
      };
      let resTree = await checkMedTree(params);
      this.filterDataTree = [];
      this.$nextTick(() => {
        this.filterDataTree = resTree.data;
        this.filterDataTree.map((item) => {
          item.tag = false;
          item.checkAll = false;
          item.single = [];
          if (
            item.webSiteAndMediaAccountList &&
            item.webSiteAndMediaAccountList.length != 0
          ) {
            item.webSiteAndMediaAccountList.map((itemb) => {
              itemb.tag = false;
            });
          }
        });
      });
      this.assignLoading = false;
    },
    //   点击切换按钮颜色
    async btnListClick(index) {
      this.currtIndex = index;
      if (
        (this.queryForm.timeRound == 5 && !this.queryForm.startTime) ||
        (this.queryForm.timeRound == 5 && !this.queryForm.endTime) ||
        (this.queryForm.timeRound == 5 &&
          this.queryForm.startTime > this.queryForm.endTime)
      ) {
        this.$message.error("请选择自定义时间范围");
        return;
      }
      let params = {
        type: this.queryForm.type,
        startTime: this.queryForm.startTime,
        endTime: this.queryForm.endTime,
        checkTaskId:
          this.queryForm.infoType.join() != 4 &&
          this.queryForm.infoType.join() != 5
            ? this.queryForm.checkTaskId.join()
            : "",
        assignIds: this.queryForm.assignIds.join(),
        timeRound: this.queryForm.timeRound,
        checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
        infoType: this.queryForm.infoType,
      };

      if (index == 0) {
        //   查询
        this.loading = true;
        this.OverviewFlag =
          this.queryForm.infoType.join() != 4 &&
          this.queryForm.infoType.join() != 5; // 确定当前信息类型是否为文章或附件，切换总体概况显示
        this.showReportView =
          this.queryForm.infoType.join() != 4 &&
          this.queryForm.infoType.join() != 5; //报告预览按钮的显隐
        // this.getTotalData();
        this.showReview = false;
        this.showMed = false;
        this.getListData();
      } else if (index == 1) {
        //   导出
        if (
          this.queryForm.infoType.join() != 4 &&
          this.queryForm.infoType.join() != 5
        ) {
          this.textListExport(params);
        } else {
          this.picListExport(params);
        }
      } else if (index == 2) {
        //   报告预览
        sessionStorage.setItem("reportQueryForm", JSON.stringify(params));
        if (this.totalDataError != 0) {
          const newRoute = this.$router.resolve({
            path: "/indexPageSee",
            query: {
              questionTab: this.questionTab,
              infoType: this.queryForm.infoType.join(","),
            },
          });
          window.open(newRoute.href, "_blank");
        } else {
          this.$message.warning("无数据，暂未生成报告");
        }
      }
    },
    async getListData() {
      if (
        (this.queryForm.timeRound == 5 && !this.queryForm.startTime) ||
        (this.queryForm.timeRound == 5 && !this.queryForm.endTime) ||
        (this.queryForm.timeRound == 5 &&
          this.queryForm.startTime > this.queryForm.endTime)
      ) {
        this.$message.error("请选择自定义时间范围");
        this.loading = false;
      } else {
        if (this.queryForm.type == 0) {
          this.checkwebName = "检测网站数";
          this.showwebcloumn = true;
        } else {
          this.checkwebName = "检测媒体账号数";
          this.showwebcloumn = false;
        }

        let params = {
          type: this.queryForm.type,
          startTime:
            this.queryForm.timeRound == 5 ? this.queryForm.startTime : "",
          endTime: this.queryForm.timeRound == 5 ? this.queryForm.endTime : "",
          checkTaskId:
            this.queryForm.infoType.join() != 4 &&
            this.queryForm.infoType.join() != 5
              ? this.queryForm.checkTaskId.join()
              : "",
          pageIndex: this.pageNum,
          pageSize: this.pageSize,
          assignIds: this.queryForm.assignIds.join(),
          timeRound: this.queryForm.timeRound,
          // wrongType: val,
          checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
          // pageNum: this.pageNum,
          infoType: this.queryForm.infoType,
        };
        sessionStorage.setItem("queryForm", JSON.stringify(params));

        let apiFunction = searchDataApi;
        if (
          this.queryForm.infoType.join() != 4 &&
          this.queryForm.infoType.join() != 5
        ) {
          if (this.questionTab == "11") {
            apiFunction = searchDataApi;
          } else if (this.questionTab == "12") {
            apiFunction = searchHistory;
          }
          this.getTotalData();
        } else {
          apiFunction = mediaSearchDataApi;
          this.loadingTwo = true;
        }
        apiFunction(params)
          .then((res) => {
            this.loading = false;
            if (res.code === 200) {
              if (
                this.queryForm.infoType.join() != 4 &&
                this.queryForm.infoType.join() != 5
              ) {
                this.tableData = res.rows;
                this.total = res.total;
              } else {
                this.tableData = res.data.home.list||[];
                this.total = res.data.home.total;
                this.totalDataList = {
                  errorTotalCount: res.data.errorTotalCount,
                  siteCount: res.data.siteCount,
                  solrCount: res.data.solrCount,
                  suspectTotalCount: res.data.suspectTotalCount,
                  notPassTotalCount: res.data.notPassTotalCount,
                };
                this.loadingTwo = false;
              }
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((err) => {
            this.loading = false;
          });
      }
    },
    //文章附件音频的导出
    async textListExport(params) {
      this.exportLoading = true;

      // params.infoType = this.queryForm.infoType

      let resTotal;
      if (this.questionTab == "11") {
        resTotal = await refTotal(params);
      } else {
        resTotal = await historyExport(params);
      }
      // 条数<5000可以导出
      if (resTotal.code == 200) {
        let restotalData = resTotal.data;
        // 获取数据是否刷新接口（刷新完和未刷新完，可能数据不一样）
        let resRefsh = await refreshApi();
        if (resRefsh.code == 200 || resRefsh.code == 506) {
          // 刷新完成
          this.$confirm(
            `${
              resRefsh.code == 200
                ? "您确定要导出" + restotalData + "数据吗?"
                : resRefsh.code == 506
                ? "数据正在刷新，您确定要导出数据吗?"
                : ""
            }
                  `,
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
            }
          )
            .then(async () => {
              const exportId = nanoid() //用生nanoid成随机exportId
              this.getExportProgress(exportId)
              params = { ...params, exportId }
              // 11当前问题  else 历史问题
              if (this.questionTab == "11") {
                exportAllApi(params).then((res) => {
                  if (res.code == 200) {
                    this.exportLoading = false;
                    this.download(res.msg);
                  } else {
                    this.exportLoading = false;
                    this.msgError(res.msg);
                  }
                });
              } else {
                historyExportnew(params).then((res) => {
                  if (res.code == 200) {
                    this.exportLoading = false;
                    this.download(res.msg);
                  } else {
                    this.exportLoading = false;
                    this.msgError(res.msg);
                  }
                });
              }
            }).catch((e) => {
              if (e == "cancel") {
                this.exportLoading = false;
              }
            });
        } else {
          this.msgError(res.msg);
          this.exportLoading = false;
        }
      } else {
        // 条数>5000,不让导出
        this.msgError(resTotal.msg);
        this.exportLoading = false;
      }
    },
    //图片视频的导出
    picListExport(params) {
      this.$confirm("您确定要导出数据项吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        this.exportLoading = true;

        const exportId = nanoid() //用生nanoid成随机exportId
        this.getExportProgress(exportId)
        params = { ...params, exportId }

        exportCheckDataApi(params).then((res) => {
          this.exportLoading = false;
          const fileName=`${params.infoType.join() == 4 ? "音频" : "视频"}-${moment().format('YYYYMMDDHHmmss')}.xlsx`
          this.downloadBlob(res,fileName)
        })
      });
    },

    //获取相应exportId的导出进度
    getExportProgress(exportId){
        //显示进度条
        this.progressPercent = 0;
        this.$nextTick(() => {
          this.progressFlag = true;
          //每500ms执行一次函数（进度条加step=5%）
          setTimeout(() => {
            let timer = setInterval(() => {
              // 导出进度条
              getExportPercent(exportId).then(resProgess=>{
                if (resProgess.code == 200) {
                  this.progressPercent = parseInt(resProgess.msg);
                } else {
                  clearInterval(timer);//停止执行
                  this.progressFlag = false;
                  this.msgError(res.msg);
                }
                // 父组件数据加载完前进度条最多到stopVal的这个百分值
                if (this.progressPercent == "100") {
                  clearInterval(timer);
                  this.progressFlag = false;
                  return;
                }
              }).catch(err=>{
                clearInterval(timer);
                this.progressFlag = false;
              })
            }, 500);
          }, 200);
        });
    },

    // 序号连续
    getIndex(index) {
      return (this.pageNum - 1) * this.pageSize + index + 1;
    },
    // 类型选择
    clickLiTwo(index) {
      // this.curLiTwo = index;
      //   this.curLiThree = 0
      if (index == 0) {
        // 系统
        this.showSystem = true;
        this.showReview = false;
        this.showMed = false;
        this.curLiTwo = index;
      } else if (index == 1) {
        // 自定义
        this.showSystem = false;
        this.showReview = true;
        this.showMed = false;
        this.curLiTwo = index;
        // this.getReviewTwoData()
      } else if (index == 2) {
        // 媒体
        this.showSystem = false;
        this.showMed = true;
        this.showReview = false;
        // this.getDiffChange();//关闭掉，每次打开弹窗不用重新请求
      }
    },
    // 获取自定义专题数据
    async getOurData() {
      let res = await getBigTitleThreeApi();
      this.ourData = res.data;
      this.ourData.map((item) => {
        this.checkOurAllId.push(item.id);
      });
      this.handleOurAllChange(true);
    },
    // （疑似废弃）
    async getDiffChange() {
      let params = {
        pageNum: 1,
        pageSize: 50,
        isCollect: 1,
      };
      let apiFunction = getWebDataApi;
      if (this.queryForm.type == 0) {
        // 网站
        apiFunction = getWebDataApi;
      } else if (this.queryForm.type == 1) {
        // 媒体账号
        apiFunction = getMedDataApi;
      }
      try {
        const res = await apiFunction(params);
        this.getWebData = res.rows;
      } catch (err) {
        console.log(err);
      }
    },
    checkLiThrees(index, item) {
      this.filterDataTree[index].tag = !this.filterDataTree[index].tag;
      if (item.webSiteAndMediaAccountList.length == 0) {
        if (this.filterDataTree[index].tag) {
          // this.checkTaskIds.push(item.id);
          // this.queryForm.assignIds = this.checkTaskIds;
          // this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
        } else {
          if (this.checkTaskIds.indexOf(item.id) != -1) {
            this.checkTaskIds.splice(this.checkTaskIds.indexOf(item.id), 1);
            this.queryForm.assignIds = this.checkTaskIds;
            // this.checkType[2].checkArrLen = this.queryForm.assignIds.length;
          }
        }
      }
      this.$forceUpdate();
    },
    //是否被包含,是返回true,不是返回false a包含b
    isContained(a, b) {
      if (!(a instanceof Array) || !(b instanceof Array)) return false;
      if (a.length < b.length) return false;
      var aStr = a.toString();
      console.info(aStr);
      for (var i = 0, len = b.length; i < len; i++) {
        console.info(aStr.indexOf(b[i]));
        if (aStr.indexOf(b[i]) == -1) return false;
      }
      return true;
    },
    // 点击切换错误类型
    clickerrorLi(index) {
      this.errorLi = index;
    },
    // 进入当前问题详情页
    goDetail(row) {
      const newPage = this.$router.resolve({
        path: "/conentReview/indexDetail/index",
        query: {
          titName: row.siteName,
          assignIds: row.siteId,
          questionTab: this.questionTab,
          infoType: this.queryForm.infoType.join(","),
        },
      });
      window.open(newPage.href, "_blank");
    },
    // 进入历史问题详情页
    goDetailHistory(row) {
      const newPage = this.$router.resolve({
        path: "/conentReview/indexDetail/index",
        query: {
          titName: row.siteName,
          assignIds: row.siteId,
          questionTab: this.questionTab,
          infoType: this.queryForm.infoType.join(","),
        },
      });
      window.open(newPage.href, "_blank");
    },
    // 进入图片音视频情页
    goDetailTwo(row) {
      const newPage = this.$router.resolve({
        path: "/conentReview/indexDetail2/index",
        query: {
          // titName: row.siteName,
          assignIds: row.siteId,
          // questionTab: this.questionTab,
          // infoType: this.queryForm.infoType.join(','),

          // host: row.host,
          // startTime: this.queryForm.startTime,
          // endTime: this.queryForm.endTime,
          dataType: this.queryForm.infoType.join(","), //当前
        },
      });
      window.open(newPage.href, "_blank");
    },
    // 获取当前问题历史问题-总体概况数据
    async getTotalData() {
      this.loadingTwo = true;
      let params = {
        type: this.queryForm.type,
        startTime: this.curLi == 5 ? this.queryForm.startTime : "",
        endTime: this.curLi == 5 ? this.queryForm.endTime : "",
        timeRound: this.queryForm.timeRound,
        checkTaskId:
          this.queryForm.infoType.join() != 4 &&
          this.queryForm.infoType.join() != 5
            ? this.queryForm.checkTaskId.join()
            : "",
        assignIds: this.queryForm.assignIds.join(),
        checkTaskTypeId: this.queryForm.checkTaskTypeId.join(),
        infoType: this.queryForm.infoType,
      };
      let apiFunction = getTotalDataApi;
      if (
        this.queryForm.infoType.join() != 4 &&
        this.queryForm.infoType.join() != 5
      ) {
        if (this.questionTab == "11") {
          apiFunction = getTotalDataApi;
        } else if (this.questionTab == "12") {
          apiFunction = getHistoryCount;
        }
      } else {
        apiFunction = getMediaCount;
      }

      try {
        const res = await apiFunction(params);
        this.loadingTwo = false;
        this.totalDataList = res;
        this.totalDataError =
          res.severeInfoCount + res.generalInfoCount + res.weakInfoCount;
      } catch (err) {
        this.loadingTwo = false;
        console.log(err);
      }
    },
    changeNature(value) {
      // 多选逻辑
      // if (this.queryForm.infoType.includes(value)) {
      //   const indexToRemove = this.queryForm.infoType.indexOf(value)
      //   this.queryForm.infoType.splice(indexToRemove, 1)
      // } else {
      //   this.queryForm.infoType.push(value)
      // }

      if (this.queryForm.infoType.join() == [value].join()) {
        return;
      }
      // 单选逻辑
      this.queryForm.infoType = [value];
      this.getSystemData();
      if (value == 1 || value == 2) {
      } else {
      }
    },
  },
};
</script>
<style>
.el-message-box__header {
  border-bottom: solid 1px #ccc;
}
</style>

<style scoped lang="scss">
// .isGray{
//   color: #CCCCCC;
//   background:#FFFFFF ;
// }
// .gray{
//   color:#FFFFFF;
//   background: #1890ff;
// }
.processbox {
  width: 50%;
  height: 140px;
  background: #fff;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  box-sizing: border-box;
  border-radius: 5px;

  .progressSpan {
    width: 100%;
    overflow: hidden;
    margin-bottom: 10px;
    display: block;
  }
}

.addType {
  width: 100%;
  overflow: hidden;
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  padding: 0 20px;
  color: #1265ed;
  cursor: pointer;
}

.addTopName {
  padding: 6px 20px;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
}

.addType:hover {
  background: #f5f7fa;
}

.pData {
  margin: 0;
  padding: 0;
  display: inline-block;
  height: 28px;
  line-height: 28px;
  margin-left: 10px;
  font-size: 12px;
  color: #999;
}

.checkMoreDialog {
  ::v-deep .el-checkbox__label {
    font-size: 16px;
    color: #333;
  }
}

.butDif {
  margin-top: 4px;
}

::v-deep .el-checkbox {
  height: 36px;
  line-height: 36px;
}

.titleBox {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 30px;
}

.elasticTitle {
  display: inline-block;
  padding: 0;
  margin: 0;
}

.addelasticTitle {
  margin-bottom: 30px !important;
}

.third-kinds {
  max-height: 50vh;
  overflow-y: auto;

  span {
    display: inline-block;
    padding: 2px 10px;
    border: 1px solid #f7f7f7;
    margin-bottom: 10px;
    margin-right: 5px;
    cursor: pointer;

    &.active {
      border: 1px solid #3d9ffe;
    }
  }

  .third-item {
    display: inline-block;
  }
}

/* 设置滚动条的样式 */
.third-kinds::-webkit-scrollbar {
  width: 5px;
  border-radius: 8px;
}

/* 滚动条滑块 */
.third-kinds::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: #8b8b8b;
}

::v-deep .el-form--inline .el-form-item {
  margin-bottom: 0px;
}

.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  // min-height: 750px;
  min-height: calc(100vh - 64px);
  position: relative;

  .homeBox {
    width: 100%;
    background: #fff;
    border-top: solid 3px #3d9ffe;
    border-radius: 10px;
    padding: 15px 0px;
    box-sizing: border-box;
    margin-bottom: 20px;

    .queryForm {
      padding: 0px 10px;
      box-sizing: border-box;
    }

    .checkTypeUl {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      margin: 0;
      padding: 0;
      margin-top: 4px;
      position: relative;

      li {
        margin-right: 16px;
        list-style: none;
        color: #606266;
        cursor: pointer;
        height: 28px;
        line-height: 28px;
        padding: 0 6px;
        border-radius: 3px;
        border: solid 1px transparent;
      }

      li.active {
        border: solid 1px #3d9ffe;
        color: #ffffff;
        background-color: #3d9ffe;
      }

      li.activeErrorLi {
        border: solid 1px #3d9ffe;
        color: #3d9ffe;
      }
    }
  }
}

.totalView {
  width: 100%;
  overflow: hidden;
  background-color: #ffffff;
  margin-bottom: 20px;
  border-radius: 10px;
  padding-bottom: 20px;
}

.homeBoxTwo {
  width: 100%;
  overflow: hidden;
  background: #fff;
  border-radius: 10px;

  .homeTable {
    width: 100%;
    overflow: hidden;
    padding: 10px 20px;
    box-sizing: border-box;
  }
}

.addshowCheckTypeOne {
  border: none !important;
  // padding: 0 20px !important;
  padding: 0 10px 0 30px !important;
  position: unset !important;
  margin-bottom: 20px;
  box-shadow: none !important;
}

.showCheckTypeOne {
  max-height: 250px;
  overflow-y: auto;
  width: 500px;
  position: absolute;
  background: #fff;
  // border: solid 1px #ccc;
  z-index: 10;
  padding: 10px;
  padding-left: 30px;
  box-sizing: border-box;
  border-radius: 5px;
  margin-top: 10px;
  box-shadow: 5px 5px 12px rgba(0, 0, 0, 0.2);

  &::-webkit-scrollbar {
     width: 8px; /* 调整滚动条宽度 */
   }

   &::-webkit-scrollbar-thumb {
     background-color: rgba(0, 0, 0, 0.2); /* 调整滚动条颜色和透明度 */
     border-radius: 5px; /* 与容器 border-radius 相似，使过渡更平滑 */
   }

  .second-check {
    display: inline-block;
    padding: 2px 10px;
    border: 1px solid #f7f7f7;
    margin-bottom: 20px;
    line-height: 24px;
    cursor: pointer;
    font-size: 12px;
    height: 28px;

    &.active {
      color: #3d9ffe;
      border: 1px solid #3d9ffe;
    }
  }

  ul.showTypeOne {
    margin: 0;
    padding: 0;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;

    li {
      list-style: none;
      margin-right: 10px;
      // padding: 0 6px;
      border-radius: 3px;
      // height: 26px;
      line-height: 26px;
      border: solid 1px #ccc;
      cursor: pointer;
      margin-bottom: 10px;
      font-size: 12px;
    }

    li.active {
      border-color: #3d9ffe;
      color: #3d9ffe;
    }
  }
}

.showTypeSpan {
  display: inline-block;
  padding: 3px 6px;
  height: 26px;
}

.customThem {
  position: absolute;
  left: 7px;
  top: 400px;
  width: 80px;
  background: #fff;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  padding: 8px 0px;
  box-sizing: border-box;

  p {
    display: block;
    line-height: 26px;
    margin: 0;
    padding: 0;
  }
}

.customThem:hover {
  background: #3d9ffe;

  p {
    color: #fff;
  }
}

.el-popover {
  height: 200px;
  overflow: auto;
}
::v-deep .timePick {
  .active {
    color: #ffffff !important;
    background-color: #3d9ffe;
  }
}

.checkTypeUlCount {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
  margin-top: 4px;
  position: relative;

  li {
    list-style: none;

    .checkArrButton {
      display: inline-block;
      vertical-align: super;
      margin-right: 6px;
      color: #606266;
      cursor: pointer;
      height: 28px;
      line-height: 28px;
      padding: 0 6px;
      border-radius: 3px;
      border: solid 1px transparent;
    }
    .checkArrlength {
      display: inline-block;
      vertical-align: super;
      text-align: center;
      background: #3d9ffe;
      height: 20px;
      min-width: 20px;
      line-height: 20px;
      color: #fff;
      border-radius: 10px;
      padding: 0 5px;
      margin-right: 10px;
    }
  }

  li.active {
    .checkArrButton {
      border: solid 1px #3d9ffe;
      color: #ffffff;
      background-color: #3d9ffe;
    }
  }
}
.partTitle {
  font-size: 18px;
  border-left: solid 3px #3d9ffe;
  margin: 20px 0 20px 25px;
  font-weight: normal;
  padding-left: 10px;
}
.dividerLine {
  border-bottom: 1px solid #e7e7e7;
  width: 100%;
  margin-bottom: 20px;
}
</style>
