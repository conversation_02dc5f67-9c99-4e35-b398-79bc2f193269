父组件使用：
<SelectAdd ref="selectAdd" :actIndustryIds="actIndustryIds" v-model="ruleForm.activityIndustryName" @saveActive="saveActive" @chooseActive="chooseActive"/>

actIndustryIds：下拉分类列表
ruleForm.activityIndustryName：分类值
saveActive：保存新增的分类函数
例如：
// 添加分类
saveActive(addActive){
    if(addActive){
        // 调接口，actIndustryIds重新赋值，以及其他的一些操作
        sureAddDataApi({type:this.type,industryName:addActive}).then((res)=>{
        if(res.code==200){
            this.$message.success(res.msg);
            this.$refs.selectAdd.resetAddActive(); // 给子组件的分类输入框值重置为空
            // this.addActive = null;
            getCategoryLists({ type:this.type }).then(res=>{
                this.actIndustryIds = res.data;
                this.asideLists = [{industryName:'全部'},...res.data]
                this.actIndustryIds.map((item)=>{
                    if(item.industryName==this.ruleForm.activityIndustryName){
                    this.$set(item,'tag',true)
                }else{
                    this.$set(item,'tag',false)
                }
                })
            })
        }
    })
    }else{
        this.msgInfo('请输入分类名')
    }
    
},
chooseActive：选中鼠标所选分类（并给actIndustryIds数组对象的tag重新赋值）
// 例如 
chooseActive(item,index){
                this.$forceUpdate();
                this.actIndustryIds.map((itema)=>{
                    if(itema==item){
                        itema.tag = true
                    }else{
                        itema.tag = false
                    }
                })
            },