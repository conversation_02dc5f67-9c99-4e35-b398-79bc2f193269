<template>
  <div id="wechatList" class="listBox" v-infinite-scroll="load" :infinite-scroll-disabled="disabled" infinite-scroll-distance="20"
        style="overflow:auto">
    <div class="listBoxTotal" v-if="total">24小时{{pushFlag}}错误 共 <span>{{ total }}</span>条</div>
    <div>
      <div v-show="listData.length==0&&!loading" style="text-align: center;">本轮预警信息已被修正，请忽略。</div>
      <ul v-show="listData.length>0">
        <li
          v-for="(item, index) in listData"
          :key="index"
          @click="goDetail(item)"
        >
          <div class="title-copy">
            <h3>{{ item.title }}</h3>
            <img src="@/assets/images/copy.png" alt="" @click.stop="copyContent(item)">
          </div>
          <p v-html="item.content" class="pOne emRed"></p>
          <p class="pTwo">建议词:{{ item.suggestWord }}</p>
          <dl>
            <dt
              style="
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ item.siteName }}
            </dt>
            <dd style="white-space: nowrap">{{ item.time }}</dd>
          </dl>
        </li>
      </ul>
      <p class="noMore" v-show="loading">加载中...</p>
      <p class="noMore" v-show="noMore && !loading">——没有更多了——</p>
    </div>
  </div>
</template>

<script>
import axios from "axios";
import {copyAritical} from "@/utils/index"
import { queryWechatList, getSignature } from "@/api/conentReview/detailPage";
export default {
  data() {
    return {
      listData: [],
      total: "",
      warnTime:"",
      baseUrlLocal: "",
      loading: false,
      historyPageMark: false,
      pushFlag: '',
      pageNum: 1,
      pageSize: 10,
    };
  },
  computed: {
      noMore() {
          return this.listData.length >= this.total
      },
      disabled() {
          return this.loading || this.noMore
      }
  },
  created() {
    // this.getBaseUrl();
  },
  async mounted() {
    await this.getwechatList();
    await this.getWeixin()
  },
  methods: {
    async getWeixin() {
      var url = decodeURIComponent(location.href.split('#')[0]);
      let res = await getSignature({ url: url })
          wx.config({
              debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
              appId: res.data.appId, // 必填，公众号的唯一标识
              timestamp: res.data.timestamp, // 必填，生成签名的时间戳
              nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
              signature: res.data.signature,// 必填，签名，见附录1
              jsApiList: [
              // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
              // 'checkJsApi',//checkJsApi接口是客户端6.0.2新引入的一个预留接口，第一期开放的接口均可不使用checkJsApi来检测。
              'onMenuShareAppMessage', //获取“分享给朋友”按钮点击状态及自定义分享内容接口（即将废弃）
              'onMenuShareTimeline', //获取“分享到朋友圈”按钮点击状态及自定义分享内容接口（即将废弃）
              // 'onMenuShareQQ',//获取“分享到QQ”按钮点击状态及自定义分享内容接口（即将废弃）
              'updateAppMessageShareData',//自定义“分享给朋友”及“分享到QQ”按钮的分享内容（1.4.0）
              'updateTimelineShareData'//自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容（1.4.0）
            ] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
          })
        let shareConfig = {
            imgUrl: "https://iapi.boryou.com:33003/smartcheck-prod/public/logo/7d502abe5b783ec9c8afac4675af95f6.png", //分享图，默认当相对路径处理，所以使用绝对路径的的话，“http://”协议前缀必须在。
            desc: this.warnTime||'', //摘要,如果分享到朋友圈的话，不显示摘要。
            title: `24小时${this.pushFlag}错误 共 ${ this.total }条`, //分享卡片标题
            link: res.data.url, //分享出去后的链接，这里可以将链接设置为另一个页面。
            success: function () { //分享成功后的回调函数
              // alert('cg')
            },
            cancel: function() {
              // 用户取消分享后执行的回调函数
              // alert('sb')
            }
          }
          wx.ready(function () {
            //自定义“分享到朋友圈”及“分享到 QQ 空间”按钮的分享内容
            wx.updateTimelineShareData(shareConfig);
            //自定义分享给朋友、以及分享给qq好友
            wx.updateAppMessageShareData(shareConfig);
            // alert('3')
            // 获取“分享给朋友”按钮点击状态及自定义分享内容接口（即将废弃）
            wx.onMenuShareAppMessage(shareConfig);
            // 获取“分享到朋友圈”按钮点击状态及自定义分享内容接口（即将废弃）
            wx.onMenuShareTimeline(shareConfig)
            //分享到微博
            // wx.onMenuShareWeibo(shareConfig)
          })
    },
    // 复制内容
    copyContent(item) {
      copyAritical(item)
    },
    load() {
        this.pageNum = this.pageNum + 1;
        this.getwechatList();
    },
    getBaseUrl() {
      console.log(process.env)
      if (process.env.VUE_APP_BASE_API == "/prod-api") {
        // 生产环境
        this.baseUrlLocal = "https://smarteye.boryou.com/prod-api";
      } else if (process.env.VUE_APP_BASE_API == "/stage-api") {
        this.baseUrlLocal = "http://192.168.3.210:18089/stage-api";
      } else {
        this.baseUrlLocal = "https://www.boryou.com/stage-api";
      }
    },
    // 获取列表数据
    async getwechatList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        warnId: this.$route.query.warnId || '',
        userName: this.$route.query.userName || '',
        startTime: this.$route.query.startTime || '',
        endTime: this.$route.query.endTime || '',
        p: this.$route.query.p || ''
      };
        try {
          let res = await queryWechatList(params)
          if(res.code == 200){
            this.listData = [...this.listData, ...res.data];
            this.total = res.total;
            this.warnTime = res.warnTime
            this.pushFlag = res.data.history == '1' ? '历史发文' : '新增发文'
          }else{
             this.$message.error(res.msg);
          }
        } finally {
          this.loading = false;
          if (sessionStorage.getItem("scrollTop")) {
            let scrollTop = sessionStorage.getItem("scrollTop");
            document.querySelector("#wechatList").scrollTop = scrollTop;
            sessionStorage.removeItem("scrollTop");
          }
        }
    },
    goDetail(item) {
      //存储滚动条位置
      sessionStorage.setItem(
        "scrollTop",
        document.querySelector("#wechatList").scrollTop
      );
      this.$router.push({
        path: "/wechatListDetail",
        query: {
          id: item.id,
          time: item.time,
          solrId: item.solrId,
          checkTaskId: item.checkTaskId,
          userName: this.$route.query.userName,
          p: this.$route.query.p
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.listBox {
  width: 100%;
  height: 100%;
  overflow: scroll;
  padding: 20px;
  box-sizing: border-box;
  .listBoxTotal {
    width: 100%;
    overflow: hidden;
    margin: 10px 0px;
    border-radius: 25px;
    border: solid 1px #ccc;
    box-shadow: 0px 0px 8px #ccc;
    height: 48px;
    line-height: 48px;
    text-align: center;
    span {
      display: inline-block;
      color: red;
    }
  }
  ul {
    width: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;
    li {
      list-style: none;
      padding: 10px;
      box-sizing: border-box;
      border: solid 1px #ccc;
      box-shadow: 0px 0px 4px #ccc;
      margin-bottom: 10px;
      border-radius: 10px;

      h3 {
        font-size: 14px;
        margin: 0;
        padding: 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-bottom: 10px;
        font-weight: 800;
      }
      p {
        margin: 0px;
        padding: 0;
        font-size: 14px;
        margin-bottom: 10px;
      }
      .pOne {
        color: #666;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
      .pTwo {
        color: #67c23a;
      }
      dl {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 14px;
      }
    }
  }
  .noMore {
    width: 100%;
    overflow: hidden;
    text-align: center;
    margin: 10px 0;
  }
}
.title-copy{
  display: flex;
  align-items: center;
  justify-content: space-between;
  h3{
    flex: 1;
  }
  img{
    width: 20px;
    height: 20px;
    margin-bottom: 10px;
  }
}
</style>
