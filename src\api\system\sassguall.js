import request from '@/utils/request'

// // 查询备案网站列表
// export function handleQueryApi (query) {
//     return request({
//         url: '/manage/siteType/list',
//         method: 'get',
//         params: query
//     })
// }
// 查询项目管理列表
export function selectProjectListApi (data) {
    return request({
        url: '/sass/selectProjectList',
        method: 'post',
        data: data
    })
}
// 删除
export function delSassApi (data) {
    return request({
        url: '/sass/delProjectList',
        method: 'post',
        data: data
    })
}
// 搜索用户
export function getUserListApi (query) {
    return request({
        url: '/system/user/listContext',
        method: 'get',
        params: query
    })
}

export function bandMoreApi (data) {
    return request({
        url: '/sass/bindUsers',
        method: 'post',
        data: data
    })
}
// 获取已绑用户
export function getBindUserApi (data) {
    return request({
        url: '/sass/selectUsersByContext',
        method: 'post',
        data: data
    })
}
export function getPersonSaasApi (data) {
    return request({
        url: '/sass/selectProjectByContext',
        method: 'post',
        data: data
    })
}
export function editSaasDialogApi (data) {
    return request({
        url: '/sass/updateProject',
        method: 'post',
        data: data
    })
}

// 新增个性化设置
export function addSassDialogApi (data) {
    return request({
        url: '/sass/insertProjectList',
        method: 'post',
        data: data
    })
}
