<template>
  <div class="home">
    <div class="homeFrom">
      <el-form :model="queryForm" :inline="true">
        <el-form-item prop="warnName" label="预警名称：">
          <el-input
            v-model.trim="queryForm.warnName"
            placeholder="请输入预警名称"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
          >
            <!-- v-hasPermi="['system:themClass:query']" -->
            搜索
          </el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">
            重置
          </el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" style="margin-bottom: 10px">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="addList"
          >
            <!-- v-hasPermi="['system:themClass:add']" -->
            新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            @click="delList(false)"
            size="mini"
          >
            <!-- v-hasPermi="['system:themClass:del']" -->
            批量删除
          </el-button>
        </el-col>
      </el-row>

      <el-table :data="themList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column prop="warnName" label="预警名称"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <el-switch
              class="switch"
              style="display: block; height: 100%"
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              active-text="开"
              inactive-text="关"
              :active-value="0"
              :inactive-value="1"
              @change="handleSwitch(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="seeDialog(scope.row)">
              <!-- v-hasPermi="['system:themClass:edit']" -->
              修改
            </el-button>
            <el-button type="text" @click="delList(scope.row)" size="small">
              <!-- v-hasPermi="['system:themClass:del']" -->
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="handleQuery"
      />
    </div>

    <!-- 新增修改弹框 -->
    <el-dialog
      :title="title"
      width="1200px"
      :visible.sync="dialogFormVisible"
      :before-close="closeAdd"
    >
      <el-form :model="form" label-width="100px" :rules="rules" ref="formRef">
        <div class="blockTop">
          <div class="blockTop-title">预警内容设置</div>
        </div>
        <el-form-item prop="warnName" label="预警名称：">
          <el-input
            v-model.trim="form.warnName"
            placeholder="请输入预警名称"
          ></el-input>
        </el-form-item>
        <el-form-item prop="word" label="添加筛选词：">
          <el-input
            v-model="form.word"
            placeholder="请输入筛选词,多个预警之间用分号隔开,如:预警1;预警2"
            @input="handleFilter"
          ></el-input>
        </el-form-item>
      
        <!-- 预警内容设置
        <el-divider class="my-divider"></el-divider> -->
        <el-form-item label="预警内容：">
          <TopicTypeSelect ref="topicTypeSelect"></TopicTypeSelect>
        </el-form-item>

        <div class="blockTop">
          <div class="blockTop-title">预警接收设置</div>
        </div>
        <el-form-item label="预警范围：">
            <el-radio-group v-model="form.warningType">
                <el-radio label="0">新增发文</el-radio>
                <el-radio label="1">历史发文</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="预警间隔：">
              <div v-if="form.warningType == 1">{{ historyTime }}</div>
              <el-select v-model="form.intervalTime" placeholder="请选择" v-else>
                  <el-option label="30分钟" :value="30"></el-option>
                  <el-option label="1小时" :value="60"></el-option>
                  <el-option label="2小时" :value="120"></el-option>
                  <el-option label="3小时" :value="180"></el-option>
                  <el-option label="4小时" :value="240"></el-option>
                  <el-option label="5小时" :value="300"></el-option>
                  <el-option label="8小时" :value="480"></el-option>
                  <el-option label="12小时" :value="720"></el-option>
                  <el-option label="24小时" :value="1440"></el-option>
              </el-select>
          </el-form-item>
        <div class="bindForm" style="padding: 20px 30px">
          <div class="tableForm">
            <!-- <div class="tableForm-title">接收方式</div> -->

            <span class="receiveMain-option">
               <div>
                <!-- 接收人：请选择需要接收消息的联系人 -->
               </div>
              <el-button type="text" style="color: #247CFF;" @click="resetContacts">重新设置接收人</el-button>
            </span>


            <el-table :data="wechatList">
              <el-table-column
                type="index"
                label="序号"
                width="80"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="wechatUserName"
                label="微信名"
                align="center"
                width="180"
              >
              </el-table-column>
              <el-table-column
                prop="headImgUrl"
                label="头像"
                align="center"
                width="180"
              >
                <template slot-scope="scope">
                  <!-- <img
                  v-if="scope.row.headImgUrl"
                  style="width: 35px"
                  :src="scope.row.headImgUrl"
                  alt="暂无头像"
                /> -->
                  <el-image
                    v-if="scope.row.headImgUrl"
                    style="width: 35px; height: 35px"
                    :z-index="9999"
                    :src="scope.row.headImgUrl"
                    :preview-src-list="[scope.row.headImgUrl]"
                  >
                  </el-image>
                  <span v-else>暂无头像</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="receiveMessage"
                label="接受预警"
                align="center"
                width="180"
              >
                <template slot-scope="scope">{{
                  scope.row.receiveMessage == "1" ? "是" : "否"
                }}</template>
              </el-table-column>
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <el-link
                    type="primary"
                    @click="changeRow(scope)"
                    style="margin-right: 10px"
                    >{{
                      scope.row.receiveMessage == "1" ? "取消预警" : "接受预警"
                    }}</el-link
                  >
                  <el-link type="danger" @click="delRow(scope)">删除</el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="boxRight">
            <div class="qrcodeBox" v-loading="!wechatimg" @click="refreshImg">
              <qrcode-vue
                v-if="wechatimg"
                :value="wechatimg"
                :size="130"
              />
            </div>
            <p>扫码绑定接收微信号，至多可绑定{{maxWechat}}个微信号</p>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button
          :loading="commitLoading"
          type="primary"
          @click="sureAdd"
          size="small"
        >
          确 定
        </el-button>
        <el-button @click="closeAdd" size="small">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="sendMsgDialog" title="设置接收人" width="50%" @close="cancelMsgDialog">
      <el-form ref="sendMsgForm" @submit.prevent>
        <el-form-item label="接收人：" prop="contacts">
          <span style="display: flex;justify-content: space-between;">
            <div class="yellowTips">
              <img src="@/assets/images/yellowWarn.svg" alt="">
              <span>请选择需要接收消息的联系人</span>
            </div>
            <el-input style="margin: 0 40px;" v-model.trim="searchWord" placeholder="请输入联系人" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getContactsList"></el-button>
            </el-input>
            <el-button type="primary" plain icon="el-icon-plus" @click="addNewUser">添加</el-button>
          </span>
        </el-form-item>
        <el-table ref="contactsTableRef" :data="contactsList" :row-key="(row) => row.id"
          @selection-change="handleSelectionChangeContact">

          <el-table-column type="selection" width="50" align="center" :reserve-selection="true"></el-table-column>
          <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
          <el-table-column prop="wxUserName" label="微信名" align="center" >
          </el-table-column>
          <el-table-column prop="headImgUrl" label="头像" align="center" >
            <template slot-scope="scope">
              <el-image v-if="scope.row.headImgUrl" style="width: 35px; height: 35px" :z-index="9999"
                :src="scope.row.headImgUrl" :preview-src-list="[scope.row.headImgUrl]">
              </el-image>
              <span v-else>暂无头像</span>
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" label="操作">
            <template #default="scope">
              <el-popconfirm title="确定删除该联系人吗？" @confirm="delContactsRow(scope.row)" style="margin-left: 10px;">
                <el-button slot="reference" type="text" style="color: #f56c6c;">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column> -->

        </el-table>

      </el-form>
      <template #footer>
        <div style="text-align: center;">
          <el-button type="primary" @click="submitSendMsgForm">确定</el-button>
          <el-button @click="cancelMsgDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>



    <el-dialog :visible.sync="QRcodeDialog" title="微信扫码" width="300px" @close="cancelQRcodeDialog">
      <div style="text-align: center;width: 100%;">
        <div class="qrcodeBox" v-loading="!wechatimg_contact" @click="getMaPic_contact">
          <qrcode-vue v-if="wechatimg_contact" :value="wechatimg_contact" :size="130" />
        </div>
        <p>扫码绑定微信</p>
      </div>
      <template #footer>
        <div style="text-align: center;">
          <el-button @click="QRcodeDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import TopicTypeSelect from "./topicTypeSelect";
import QrcodeVue from "qrcode.vue";
import {
  handleQueryApi,
  sureAddApi,
  getNewformIdApi,
  editAddApi,
  delListApi,
  seeListApi,
  getMaPicApi,
  getMaPicUrlApi,
  getWechatUseApi,
  deleteNosavedApi,
  checkScanStatus,
  getContactApi,
  bindContactApi,
  bindContactToWarnApi,
} from "@/api/system/wechatWarning";

export default {
  components: { TopicTypeSelect, QrcodeVue },
  filters: {
    convertSemicolon(str) {
      if (!str) return "";
      return str.replace(/；/g, ";");
    },
  },
  data() {
    return {
      queryForm: {
        warnName: "",
      },
      pageNum: 1,
      pageSize: 10,
      themList: [],
      total: 0,
      dialogFormVisible: false,
      form: {
        warnName: "",
        warnId: "",
        warningType: '0',
        intervalTime: 480,
      },
      historyTime: '每日8时和14时',
      title: "新增微信预警",
      ids: [],
      single: true,
      // 非多个禁用
      multiple: true,
      rules: {
        warnName: [
          { required: true, trigger: "blur", message: "名称不能为空" },
          { pattern: /^.{0,30}$/, message: "长度不能超过30个字符" },
        ],
        // word: [{ required: true, trigger: "blur", message: "备注不能为空" }],
      },
      wechatList: [{}],

      wechatimg: "",

      wechatQRCodeId: null,
      timerTwo: null,

      commitLoading: false, // 提交按钮loading

      maxWechat: 60,// 至多可绑定多少个微信号


      sendMsgDialog: false,
      searchWord: '',
      contactsList: [],
      selectedRows: [],

      QRcodeDialog: false,
      wechatimg_contact: "",
      wechatQRCodeId_contact: null,
      timerTwo_contact: null,
    };
  },
  created() {
    this.handleQuery();
    this.timerTwo = setInterval(() => {
      this.getWechatUse();
    }, 2000);

    this.timerTwo_contact = setInterval(() => {
      this.getWechatUse_contact();
    }, 2000);
  },
  methods: {
    handleFilter(e) {
      if (!e) return;
      this.form.word = e.replace(/；/g, ";");
    },
    //   搜索
    async handleQuery() {
      let params = {
        warnName: this.queryForm.warnName,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      let res = await handleQueryApi(params);
      this.themList = res.rows;
      this.total = res.total;
    },
    // 重置
    resetQuery() {
      this.queryForm.warnName = "";
    },
    // 点击新增弹框
    async addList() {
      this.title = "新增微信预警";
      this.form = {
        warnName: "",
        warnId: "",
        wechat: "",
        warningType: '0',
        intervalTime: 480,
      };
      this.wechatList = [];
      const res = await getNewformIdApi();

      if (res.code == 200) {
        this.dialogFormVisible = true;
        this.form.warnId = res.rows;
        this.getMaPic();
        this.$nextTick(() => {
          this.$refs.formRef.clearValidate();
          this.$refs.topicTypeSelect.handleAllChange({
            //组件赋值
            checkTaskTypeId: [],
            checkTaskId: [],
          });
        });
      }
    },
    // 新增确定按钮
    sureAdd() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // if(this.wechatList&&this.wechatList.length>this.maxWechat){
          //   return this.$message.error(`至多可绑定${this.maxWechat}个微信号`);
          // }
          this.commitLoading = true;
          if (this.title == "修改微信预警") {
            // 修改
            let params = {
              warnName: this.form.warnName,
              word: this.form.word,
              topic: JSON.stringify({
                system: this.$refs.topicTypeSelect.queryForm.checkTaskTypeId,
                custom: this.$refs.topicTypeSelect.queryForm.checkTaskId,
              }),
              warnId: this.form.warnId,
              warningType: this.form.warningType,
              intervalTime: this.form.warningType == '0' ? this.form.intervalTime : '',
            };
            editAddApi(params).then((res) => {
              this.commitLoading = false;
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.dialogFormVisible = false;
                this.handleQuery();
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
              } else {
                this.$message.error(res.msg);
              }
            });
          } else {
            //   新增
            let params = {
              warnName: this.form.warnName,
              word: this.form.word,
              topic: JSON.stringify({
                system: this.$refs.topicTypeSelect.queryForm.checkTaskTypeId,
                custom: this.$refs.topicTypeSelect.queryForm.checkTaskId,
              }),
              warnId: this.form.warnId,
              warningType: this.form.warningType,
              intervalTime: this.form.warningType == '0' ? this.form.intervalTime : '',
            };
            sureAddApi(params).then((res) => {
              this.commitLoading = false;
              if (res.code == 200) {
                this.dialogFormVisible = false;
                this.$message.success("新增成功");
                this.handleQuery();
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
              } else {
                this.$message.error(res.msg);
              }
            });
          }
        } else {
          this.$message.error("请输入正确信息");
        }
      });
    },
    // 取消
    closeAdd() {
      this.dialogFormVisible = false;
      if (this.title == "新增微信预警") {
        deleteNosavedApi({ warnId: this.form.warnId });
      }
    },
    // 查看
    async seeDialog(row) {
      this.dialogFormVisible = true;
      this.title = "修改微信预警";
      await this.setDialogForm(row);
      this.getMaPic();
      this.$refs.formRef.clearValidate();
    },
    // 设置弹框数据
    async setDialogForm(row) {
      const res = await seeListApi({ warnId: row.warnId });
      this.form = res.rows;
      console.log('this.form :>> ', this.form);
      // this.form.intervalTime = 480
  
      // this.$nextTick(() => {
      this.wechatList = JSON.parse(res.rows.wechat);
      //组件赋值
      if (res.rows.topic) {
        const topicTypeSelectData = JSON.parse(res.rows.topic);
        this.$refs.topicTypeSelect.handleAllChange({
          checkTaskTypeId: topicTypeSelectData.system,
          checkTaskId: topicTypeSelectData.custom,
        });
      }
      // });
    },
    // 设置弹框数据-微信列表
    async setDialogWechat(row) {
      const res = await seeListApi({ warnId: row.warnId });
      this.form.wechat = res.rows?.wechat;
      this.wechatList = JSON.parse(res.rows.wechat);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.warnId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 刪除
    delList(row) {
      const ids = row ? [row.warnId] : this.ids;
      if (!ids||ids.length == 0) {
        this.$message.error("请选择要删除的数据");
        return;
      }
      this.$confirm("是否确认删除该数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delListApi({ ids, delFlag: 1 });
        })
        .then((res) => {
          if (res.code == 200) {
            this.handleQuery();
            this.msgSuccess("删除成功");
          } else {
            this.$message.error(res.msg);
          }
        });
    },
    // 切换状态
    handleSwitch(row) {
      let params = {
        warnId: row.warnId,
        status: row.status,
      };
      if (row.status) {
        params.status = 1;
      } else {
        params.status = 0;
      }
      editAddApi(params).then((res) => {
        if (res.code == 200) {
          this.$message.success("切换成功");
          this.handleQuery();
        }
      });
    },

    // 切换预警状态
    async changeRow(scope) {
      console.log(this.form.wechat, "this.form.wechat");
      let newwechat = JSON.parse(this.form.wechat);
      newwechat[scope.$index] = {
        ...scope.row,
        receiveMessage: scope.row.receiveMessage == "1" ? "0" : "1",
      };
      let params = {
        warnId: this.form.warnId,
        wechat: JSON.stringify(newwechat),
      };
      console.log(params, "params");

      editAddApi(params).then((res) => {
        if (res.code == 200) {
          this.$message.success("修改成功");
          this.setDialogWechat({ warnId: this.form.warnId });
        }
      });
    },
    // 获取用户微信预警绑定列表
    // async getUserWeChat() {
    //   let query = {
    //     wechat: this.form.wechat,
    //   };
    //   if (!this.form.wechat) {
    //     this.wechatList = [];
    //     return;
    //   }
    //   let res = await getUserWeChatApi(query);
    //   this.wechatList = res.rows;
    // },
    // 删除
    async delRow(scope) {
      let newwechat = JSON.parse(this.form.wechat);
      newwechat.splice(scope.$index, 1);
      let params = {
        warnId: this.form.warnId,
        wechat: JSON.stringify(newwechat),
      };
      this.$confirm("是否确认删除该数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return editAddApi(params);
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success("删除成功");
            this.setDialogWechat({ warnId: this.form.warnId });
          } else {
            this.$message.error(res.msg);
          }
        });
    },
    // 获取二维码
    async getMaPic() {
      let params = {
        position: "USER_WARN",
        warnId: this.form.warnId,
      };
      let res = await getMaPicUrlApi(params);
      this.wechatimg = res.data.url;
      this.wechatQRCodeId = res.data.code;
    },
    // 获取微信是否已扫码
    async getWechatUse() {
      if (!this.dialogFormVisible || !this.wechatQRCodeId) return;
      let params = {
        code: this.wechatQRCodeId,
        warnId: this.form.warnId,
      };
      // let res = await getWechatUseApi(params);
      let res = await checkScanStatus(params);
      console.log('res :>> ', res);
      if (res.data.status == "BIND_SUCCESS") {
        // 已扫码
        // this.getUserWeChat();
        // await this.setDialogForm({ warnId: this.form.warnId });
        let req = {
          warnId: this.form.warnId,
          contact: [{
            wxUserName: res.data.userInfo.nickname,
            wxOpenId: res.data.userInfo.openid,
            headImgUrl: res.data.userInfo.headImgUrl,
          }]
        }
        bindContactToWarnApi(req).then((res) => {
          if (res.code == 200) {
            this.setDialogWechat({ warnId: this.form.warnId });
            this.getMaPic();
            this.$message.success('绑定成功');
          } else {
            this.$message.error(res.msg);
          }
        });

      } else {
        // this.$message.error(res.msg)
      }
    },
    refreshImg() {
      this.getMaPic();
    },

    //重新设置接收人
    async resetContacts() {
      this.sendMsgDialog = true
      await this.getContactsList()
      // this.toggleSelection(this.wechatContactsList)
      this.toggleSelection(this.wechatList)
    },
    //同步接收人表单的已选项
    toggleSelection(list) {
      this.$refs.contactsTableRef.clearSelection();
      if (!list || list?.length == 0) return
      this.contactsList.forEach(row => {
        list?.forEach(rows => {
          if (row.id === rows.id) {
            this.$refs.contactsTableRef.toggleRowSelection(row, true)
          }
        })
      })
    },
    //关闭接收人弹窗
    cancelMsgDialog() {
      this.sendMsgDialog = false
      this.searchWord = ''
    },
    //获取联系人列表
    async getContactsList() {
      let param = {
        wxUserName: this.searchWord
      }
      await getContactApi(param).then(res => {
        this.contactsList = res.data
      })
    },
    //table选中项改变
    handleSelectionChangeContact(val) {
      this.selectedRows = val
    },
    // 提交表单
    submitSendMsgForm() {
      if(!this.selectedRows.length){
        return this.$message.error('请选择联系人')
      }
      let rows = JSON.parse(JSON.stringify(this.selectedRows))
      // this.wechatContactsList = rows

      let param = {
        warnId: this.form.warnId,
        contact: rows
      }

      bindContactToWarnApi(param).then(async(res) => {
        if (res.code == 200) {
          this.$message.success("绑定成功");
          
          await this.setDialogWechat({ warnId: this.form.warnId });
          this.cancelMsgDialog()
        } else {
          this.$message.error(res.msg);
        }
      });
    },

    //删除联系人
    async delContactsRow(row) {
      // try {
      //   await delContactsApi(row.id)
      //   this.$message.success('删除成功')
      //   this.getContactsList()
      // } catch (err) {

      // }
    },

    addNewUser(){
      this.wechatimg_contact = ''
      this.wechatQRCodeId_contact = ''
      this.getMaPic_contact()
      this.QRcodeDialog = true
    },
    cancelQRcodeDialog(){
      this.QRcodeDialog = false
      this.resetContacts()
    },

    // 获取添加联系人二维码
    async getMaPic_contact() {
      console.log('this.QRcodeDialog :>>');
      let params = {
        position: "USER_WARN",
      };
      let res = await getMaPicUrlApi(params);
      this.wechatimg_contact = res.data.url;
      this.wechatQRCodeId_contact = res.data.code;
    },
    // 获取添加联系人是否已扫码
    async getWechatUse_contact() {
      if (!this.QRcodeDialog || !this.wechatQRCodeId_contact) return;
      let params = {
        code: this.wechatQRCodeId_contact,
      };
      let res = await checkScanStatus(params);
      console.log('res :>> ', res);
      if (res.data.status == "BIND_SUCCESS") {
        // 已扫码
        // this.getUserWeChat();
        // await this.setDialogForm({ warnId: this.form.warnId });
        let req = {
          wxUserName: res.data.userInfo.nickname,
          wxOpenId: res.data.userInfo.openid,
          headImgUrl: res.data.userInfo.headImgUrl,
        }
        bindContactApi(req).then((res) => {
          if (res.code == 200) {
            this.$message.success("添加成功");
            this.getMaPic_contact();
          } else {
            this.$message.error(res.msg);
          }
        });
      } else {
        // this.$message.error(res.msg)
      }
    },
  },

  beforeDestroy() {
    //清除定时器
    clearInterval(this.timerTwo);
    this.timerTwo = null;

    clearInterval(this.timerTwo_contact);
    this.timerTwo_contact = null;
  },
};
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: 889px;
  ::v-deep .homeFrom {
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;

    /* switch按钮样式 */
    .switch .el-switch__label {
      position: absolute;
      top: 2px;
      left: 13px;
      display: none;
      color: #fff !important;
    }

    /*打开时文字位置设置*/
    .switch .el-switch__label--right {
      z-index: 1;
    }

    /* 调整打开时文字的显示位子 */
    .switch .el-switch__label--right span {
      margin-right: 9px;
    }

    /*关闭时文字位置设置*/
    .switch .el-switch__label--left {
      z-index: 1;
    }

    /* 调整关闭时文字的显示位子 */
    .switch .el-switch__label--left span {
      margin-left: 9px;
    }

    /*显示文字*/
    .switch .el-switch__label.is-active {
      display: block;
    }

    /* 调整按钮的宽度 */
    .switch.el-switch .el-switch__core,
    .el-switch .el-switch__label {
      width: 50px !important;
      margin: 0;
    }

  }
}
.my-divider {
  margin-top: 10px;
}

.tableForm {
  width: calc(100% - 200px);
  display: inline-block;
  vertical-align: top;
  padding: 20px;
  background: #ffffff;
  box-shadow: 0px 7px 6px 1px rgba(174, 174, 174, 0.16);
  border-radius: 5px;
  .tableForm-title {
    font-size: 16px;
    font-family: PingFang SC, PingFang SC;
    color: #333333;
    margin: 0 0 20px 10px;
  }
  .receiveMain-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
.tableForm {
  ::v-deep .el-table {
    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
      th {
        background: #f8fcff;
      }
    }
  }
}

.boxRight {
  width: 180px;
  overflow: hidden;
  text-align: center;
  display: inline-block;
  // float: right;
  margin-left: 20px;
  .qrcodeBox{
    width: 170px;
    background-color: #ffffff;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 3px 3px 6px 1px rgba(0,0,0,0.16)
  }

  // img {
  //   width: 150px;
  //   height: 150px;
  //   overflow: hidden;
  //   cursor: pointer;
  // }
}
.blockTop {
  border: 1px solid #ddebff;
  padding: 10px 20px;
  background: #f5f9ff;
  margin-bottom: 20px;
  .blockTop-title {
    height: 14px;
    display: inline-block;
    border-left: 3px solid #2e54ec;
    line-height: 14px;
    font-size: 14px;
    font-family: PingFang SC, PingFang SC;
    font-weight: bold;
    color: #333333;
    padding-left: 5px;
  }
}

.yellowTips {
  white-space: nowrap;
  line-height: 32px;
  background: #FFFBE6;
  border-radius: 4px;
  border: 1px solid #FAAD14;
  padding: 0px 10px;

  img {
    vertical-align: middle;
    height: 17px;
    margin-right: 5px;
  }

  span {
    vertical-align: middle;
  }
}
</style>
