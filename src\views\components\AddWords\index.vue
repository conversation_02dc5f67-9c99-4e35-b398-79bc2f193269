<template>
    <!-- 自定义专题弹框 -->
    <el-dialog title="添加自定义词" width="600px" :visible.sync="dialogVisible" :before-close="cancelDialog">
        <div slot="title" class="header-title">
            <span class="title-age">添加自定义词</span>
            <el-tooltip class="item" effect="light" placement="top-start" popper-class="tip-class">
                <div slot="content" style="line-height: 24px; font-size: 12px">
                    用户可自定义添加正确词、敏感词或者错
                    <br />误词并进行应用监测；添加敏感词和错
                    <br />误词时需提前维护对应的自定义专题
                </div>
                <span class="title-name">
                    <i class="el-icon-question icon-report"
                        style="color: #e6a23c; margin-left: 10px; cursor: pointer"></i>
                </span>
            </el-tooltip>
        </div>
        <el-form :model="formCustom" label-width="120px" :rules="rules" ref="formCustomRules">
            <el-form-item label-width="0" style="text-align: center">
                <el-radio-group v-model="tabPosition">
                    <el-radio-button label="1">添加正词</el-radio-button>
                    <el-radio-button label="2">添加敏感词</el-radio-button>
                    <el-radio-button label="3">添加错误词</el-radio-button>
                </el-radio-group>
            </el-form-item>
            <el-form-item label-width="0">
                <p v-show="tabPosition == 1">
                    说明：将词语添加为正词后，再出现该词语时，首页问题汇总、审校专题、
                    文稿审校版块都将不会提示错误。
                </p>
                <p v-show="tabPosition == 2">
                    说明：将词语添加为敏感词后，再出现该词语时，首页问题汇总、审校专题版块将会提示敏感，文稿审校版块将会提示错误但不会建议正确词语。
                </p>
                <p v-show="tabPosition == 3">
                    说明：将未提示错误的词语添加入错误词库后，再出现该词语时，首页问题汇总、审校专题
                    版块将会提示错误，文稿审校版块将会提示错误并给出建议正确词。
                </p>
            </el-form-item>
            <el-form-item label="正词：" :prop="tabPosition == 1 ? 'properWord' : ''" v-show="tabPosition == 1">
                <el-input type="textarea" placeholder="请输入正词,多个请用空格隔开" v-model="formCustom.properWord" maxlength="500"
                    show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="敏感词：" :prop="tabPosition == 2 ? 'sensitiveWord' : ''" v-show="tabPosition == 2">
                <el-input type="textarea" placeholder="请输入敏感词,多个请用空格隔开" v-model="formCustom.sensitiveWord"
                    maxlength="500" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="错误词" :prop="tabPosition == 3 ? 'errorWord' : ''" v-show="tabPosition == 3">
                <el-input type="textarea" v-model="formCustom.errorWord" placeholder="请输入错误词,多个请用空格隔开；错误词与正确词需要一一对应"
                    maxlength="500" show-word-limit />
            </el-form-item>
            <el-form-item label="建议词" :prop="tabPosition == 3 ? 'suggestWord' : ''" v-show="tabPosition == 3">
                <el-input type="textarea" v-model="formCustom.suggestWord" placeholder="请输入建议词，多个请用空格隔开；建议词和错误词需要一一对应"
                    maxlength="500" show-word-limit />
            </el-form-item>
            <el-form-item :label="`${tabPosition == 2 ? '敏感词' : '错误词'}专题`" v-show="tabPosition == 2 || tabPosition == 3"
                :prop="(tabPosition == 2 ? 'checkTaskId' : '') ||
        (tabPosition == 3 ? 'checkTaskId' : '')
        ">
                <el-select v-model="formCustom.checkTaskId" :placeholder="`请选择${tabPosition == 2 ? '敏感词' : '错误词'}专题`"
                    style="width: 100%">
                    <el-option v-for="item in tasksenDataOption" :key="item.id" :label="item.name"
                        :value="item.id"></el-option>
                </el-select>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="suredialogFormTopicVisible">确 定</el-button>
            <el-button @click="cancelDialog">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { getSensitiveApi, addsensitiveWord, addTrueWord } from "@/api/system/autodict";
export default {
    data() {
        return {
            tabPosition: 1,
            formCustom: {
                // 正词
                properWord: "",
                // 敏感词
                sensitiveWord: "",
                // 错误词
                errorWord: "",
                // 建议词
                suggestWord: "",
                // 错误词专题
                checkTaskId: "",
            },
            // 错误词专题数据
            tasksenDataOption: [],
            // 表单校验
            rules: {
                properWord: [
                    { required: true, message: "正词不能为空", trigger: "change" },
                    {
                        max: 500,
                        message: "正词长度不能超过500个字符",
                        trigger: "blur",
                    },
                ],
                sensitiveWord: [
                    { required: true, message: "敏感词不能为空", trigger: "change" },
                    {
                        max: 500,
                        message: "敏感词长度不能超过500个字符",
                        trigger: "blur",
                    },
                ],
                errorWord: [
                    { required: true, message: "错误词不能为空", trigger: "change" },
                    {
                        max: 500,
                        message: "错误词长度不能超过500个字符",
                        trigger: "blur",
                    },
                ],
                suggestWord: [
                    { required: true, message: "建议词不能为空", trigger: "change" },
                ],
                checkTaskId: [
                    { required: true, message: "错误词专题不能为空", trigger: "change" },
                ],
            },
        }
    },
    props: {
        dialogVisible: {
            type: Boolean,
            default: function () {
                return false;
            },
        },
    },
    created() {
        this.getTopdata();
    },
    methods: {
        defaultType(params) {
            const { wordType, ...word } = params
            this.tabPosition = wordType;
            this.formCustom = { ...this.formCustom, ...word };
        },
        cancelDialog() {
            for (let key in this.formCustom) {
                this.$set(this.formCustom, key, "");
            }
            this.$emit('closeDialog');
        },
        // 获取敏感词和错误词专题数据
        async getTopdata() {
            let res = await getSensitiveApi({ type: 3});
            this.tasksenDataOption = res.rows;
        },
        // 自定义专题确定按钮
        suredialogFormTopicVisible() {
            this.$refs["formCustomRules"].validate((valid) => {
                if (valid) {
                    if (this.tabPosition == 1) {
                        // 添加正词
                        let params = {
                            properWord: this.formCustom.properWord,
                        };
                        addTrueWord(params).then((res) => {
                            if (res.code == 200) {
                                this.msgSuccess("新增正词成功");
                                this.cancelDialog()
                            } else {
                                this.msgError(res.msg);
                            }
                        });
                    } else if (this.tabPosition == 2) {
                        // 添加敏感词
                        if (this.formCustom.sensitiveWord && this.formCustom.checkTaskId) {
                            let params = {
                                problemType: 1,
                                problemWord: this.formCustom.sensitiveWord,
                                checkTaskId: this.formCustom.checkTaskId,
                            };
                            addsensitiveWord(params).then((res) => {
                                if (res.code == 200) {
                                    this.msgSuccess("新增敏感词成功");
                                    this.cancelDialog()
                                } else {
                                    this.msgError(res.msg);
                                }
                            });
                        } else {
                            this.$message.error("请输入完整信息");
                        }
                    } else {
                        // 添加错误词
                        if (
                            this.formCustom.errorWord &&
                            this.formCustom.suggestWord &&
                            this.formCustom.checkTaskId
                        ) {
                            let params = {
                                problemType: 2,
                                problemWord: this.formCustom.errorWord,
                                suggestWord: this.formCustom.suggestWord,
                                checkTaskId: this.formCustom.checkTaskId,
                            };
                            addsensitiveWord(params).then((res) => {
                                if (res.code == 200) {
                                    this.msgSuccess("新增错误词成功");
                                    this.cancelDialog()
                                } else {
                                    this.msgError(res.msg);
                                }
                            });
                        } else {
                            this.$message.error("请输入完整信息");
                        }
                    }
                } else {
                    this.$message.error("请输入完整信息");
                }
            });
        },
        // customClick() {
        //     for (let key in this.formCustom) {
        //         this.$set(this.formCustom, key, "");
        //     }
        //     this.dialogVisible = true;
        //     this.tabPosition = 1;
        // },
    },
}
</script>

<style></style>