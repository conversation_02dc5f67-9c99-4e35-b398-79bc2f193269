import request from '@/utils/request'

// 获取路由
export const textCheckApi = (data) => {
    return request({
        url: '/textCheck/result',
        method: 'post',
        data: data
    })
}
// 下载
export const exportDocApi = (data) => {
    return request({
        url: '/textCheck/exportDoc',
        method: 'post',
        data: data
    })
}

// 查看示例
export const getExampleDataApi = () => {
    return request({
        url: '/textCheck/case',
        method: 'get',
    })
}
// 一键复制
export const textCopy = (data) => {
    return request({
        url: '/textCheck/textCopy',
        method: 'post',
        data: data
    })
}
// 微信链接导入
export const weChatArticleLink = (data) => {
    return request({
        url: '/textCheck/weChatArticleLink',
        method: 'post',
        data: data
    })
}




// 上传图片
export function submitUploadApi (data) {
    return request({
        url: '/onlineTest/uploadFile',
        method: 'post',
        data: data
    })
}
// 图片开始检测
export function checkPicApi (data) {
    return request({
        url: '/check/pic',
        method: 'post',
        data: data
    })
}

// 上传视频
export function upVideoApi (data) {
    return request({
        url: '/check/video',
        method: 'post',
        data: data
    })
}
// 音频转文本
export function checkAudio (data) {
    return request({
        url: '/check/audio',
        method: 'post',
        data: data
    })
}


//检测列表

// 获取问题汇总数据
export function getCheckListApi (data) {
    return request({
        url: '/check/video/list',
        method: 'post',
        data: data
    })
}
// 删除
export function delvideoListApi (data) {
    return request({
        url: '/check/video/delete',
        method: 'post',
        data: data
    })
}
// 查看
export function getSeeContentApi (data) {
    return request({
        url: '/onlineTest/videoSuccessResult',
        method: 'post',
        data: data
    })
}

//获取今日检测剩余字数
export function getRemainingCountApi (data) {
    return request({
        url: '/check/text/number',
        method: 'post',
        data: data
    })
}
//获取今日检测剩余字数-frame
export function getRemainingCountApiFrame (defaultToken) {
    return request({
        url: '/plugin/number',
        method: 'post',
        headers:{Authorization:'Bearer ' +defaultToken}   
    })
}
// 文本检查
export const textCheckApiFrame = (data,defaultToken) => {
    return request({
        url: '/plugin/check',
        method: 'post',
        data: data,
        headers:{Authorization:'Bearer ' +defaultToken}   
    })
}
// 一键复制
export const textCopyFrame = (data,defaultToken) => {
    return request({
        url: '/plugin/copy',
        method: 'post',
        data: data,
        headers:{Authorization:'Bearer ' +defaultToken}  
    })
}

// 查询传递的文本 
export const textGet = (data, defaultToken) => {
    return request({
        url: '/plugin/text/get',
        method: 'post',
        data: data,
        headers: { Authorization: 'Bearer ' + defaultToken }
    })
}
// 下载插件 
export function pluginDownload(){
    return request({
        url: '/plugin/download',
        method: 'post',
        responseType: 'blob'
    })
}