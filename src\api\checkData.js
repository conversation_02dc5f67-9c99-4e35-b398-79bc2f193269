import request from '@/utils/request'

// 获取数据检测列表数据
export function checkDataListApi (data) {
    return request({
        url: '/errorCheck/errorStatistics',
        method: 'post',
        data: data
    })
}
// 数据检测列表数据导出
export function exportCheckDataApi (data) {
    return request({
        url: '/errorCheck/errorStatisticsExport',
        method: 'post',
        data: data,
          responseType: 'blob'
    })
}

// 获取数据检测详情
export function getError (data) {
  return request({
      url: '/query/detail',
      method: 'post',
      data: data
  })
}

// 首页总数统计
export function errorStatisticsCount (data) {
  return request({
      url: '/errorCheck/errorStatisticsCount',
      method: 'post',
      data: data
  })
}

//文本错误详情
export function getTextDetail (data) {
  return request({
      url: '/errorCheck/getTextDetail',
      method: 'post',
      data: data
  })
}

