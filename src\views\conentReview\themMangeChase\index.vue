<template>
  <div class="home">
    <div class="homeLeft">
      <div class="homeLeftBox">
        <!-- <span v-if="curPartId==-1">个人信息泄露</span> -->
        <span>{{ typeName }}</span>
        <div class="homBut">
          <el-button
            size="mini"
            @click="handleDeleteAll"
            v-if="typeName !== '个人信息泄露'"
            >批量过滤</el-button
          >
          <el-button size="mini" @click="exportAll">导出</el-button>
        </div>
      </div>
      <el-table
        :data="getListDataList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column label="标题" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <span
              style="display: inline-block; cursor: pointer"
              @click="goTitleDetail(scope.row)"
              >{{ scope.row.title }}</span
            >
          </template>
        </el-table-column>
        <el-table-column prop="siteName" label="站点名称"></el-table-column>
        <el-table-column prop="time" label="时间"></el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="getListData"
      />
    </div>
    <div class="homeRight" v-show="curPartId > 0">
      <span>审校专题</span>
      <ul>
        <li
          v-for="(item, index) in reviewData"
          @click="changeLi(item)"
          :class="item.id == curLi ? 'activeLi' : ''"
          :key="index"
        >
          {{ item.name }}
        </li>
      </ul>
    </div>
    <!-- 过滤信息备注弹框 -->
    <el-dialog title="备注信息" :visible.sync="dialogFormVisible" width="30%">
      <el-form :model="form" :inline="true" class="formEl">
        <el-form-item
          label="备注:"
          prop="remark"
          label-width="120px"
          style="width: 100%"
        >
          <el-input
            v-model.trim="form.remark"
            type="textarea"
            style="width: 100%"
            placeholder="备注可不填"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="sureRemark">确 定</el-button>
        <el-button @click="cancelsureRemark">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  reviewTopApi,
  getListDataApi,
  deletsFDialogApi,
  exportAllApi,
  exportAllApipeopleInfo,
} from "@/api/conentReview/themMangeChase";
import { getInfoDataApi } from "@/api/conentReview/themManTopic";
import {
  getBigTitleTwoApi,
  getBigTitleThreeApi,
} from "@/api/conentReview/themManTopic";
export default {
  name: "themMangeChase",
  data() {
    return {
      loading: false,
      reviewData: [],
      pageSize: 10,
      pageNum: 1,
      getListDataList: [],
      curLi: this.$route.query.id,
      total: 0,
      typeName: "",
      multipleSelection: [],
      // 个人信息泄漏
      curPartId: this.$route.query.id,
      tabPosition: this.$route.query.tabPosition,
      dialogFormVisible: false,
      form: {
        remark: "",
      },
    };
  },
  created() {
    this.reviewTop();
    this.getListData();
  },
  methods: {
    // 备注信息查看和修改
    async sureRemark() {
      if (this.multipleSelection && this.multipleSelection.length) {
        let idsStr = this.multipleSelection.map((item) => item.id);
        let params = {
          infoIds: idsStr,
          remark: this.form.remark,
        };
        let res = await deletsFDialogApi(params);
        if (res.code == 200) {
          this.dialogFormVisible = false;
          this.$message.success("过滤成功");
          this.getListData();

          this.form.remark = "";
        } else {
          this.dialogFormVisible = false;
          this.msgError(res.msg);
          this.form.remark = "";
        }
      } else {
        this.msgError("请选择要过滤的数据");
      }
    },
    // 取消过滤
    cancelsureRemark() {
      this.form.remark = "";
      this.dialogFormVisible = false;
    },
    //   导出
    exportAll() {
      let idsList = this.multipleSelection.map((item) => item.id);
      let sorlId = this.multipleSelection.map((item) => item.solrId);
      let timesList = this.multipleSelection.map((item) => item.time);
      if (this.curPartId == -1) {
        // 导出个人信息泄露
        if (idsList && idsList.length) {
          // 选中数据导出
          let params = {
            startTime: this.$route.query.startTime,
            endTime: this.$route.query.endTime,
            type: this.$route.query.type,
            assignIds: this.$route.query.assignIds,
            ids: sorlId.toString(),
            times: timesList.toString(),
          };
          this.$confirm("您确定要导出选中的数据吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
          })
            .then(function () {
              return exportAllApipeopleInfo(params);
            })
            .then((response) => {
              debugger;
              this.download(response.msg);
            });
        } else {
          // 导出全部
          let params = {
            startTime: this.$route.query.startTime,
            endTime: this.$route.query.endTime,
            type: this.$route.query.type,
            assignIds: this.$route.query.assignIds,
          };
          this.$confirm("您确定要导出全部数据吗?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
          })
            .then(function () {
              return exportAllApipeopleInfo(params);
            })
            .then((response) => {
              this.download(response.msg);
            });
        }
        // let params = {
        //   startTime: this.$route.query.startTime,
        //   endTime: this.$route.query.endTime,
        //   type: this.$route.query.type,
        //   assignIds: this.$route.query.assignIds,
        // };
      } else {
        // 导出审校专题数据
        if (this.tabPosition == 0) {
          // 导出系统专题
          let params = {
            checkTaskTypeId: this.curLi,
            startTime: this.$route.query.startTime,
            endTime: this.$route.query.endTime,
            type: this.$route.query.type,
            assignIds: this.$route.query.assignIds,
            ids: idsList.toString(),
          };
          this.$confirm(
            `您确定要导出${idsList.length > 0 ? "当前选中" : "全部"}数据吗?`,
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
            }
          )
            .then(function () {
              return exportAllApi(params);
            })
            .then((response) => {
              this.download(response.msg);
            });
        } else if (this.tabPosition == 1) {
          // 导出自定义专题
          let params = {
            checkTaskId: this.curLi,
            startTime: this.$route.query.startTime,
            endTime: this.$route.query.endTime,
            type: this.$route.query.type,
            assignIds: this.$route.query.assignIds,
            ids: idsList.toString(),
          };
          this.$confirm(
            `您确定要导出${idsList.length > 0 ? "当前选中" : "全部"}数据吗?`,
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
            }
          )
            .then(function () {
              return exportAllApi(params);
            })
            .then((response) => {
              this.download(response.msg);
            });
        }
      }
    },
    //   详情页
    goTitleDetail(item) {
      // this.$router.push({
      //   path: "/conentReview/detailPage/index",
      //   query: {
      //     id: item.id,
      //     itemTime: item.time,
      //     itemSolrId: item.solrId,
      //     itemCheckTaskId: item.checkTaskId,
      //   },
      // });
      const newPage = this.$router.resolve({
        path: "/conentReview/detailPage/index",
        query: {
          id: item.id,
          itemTime: item.time,
          itemSolrId: item.solrId,
          itemCheckTaskId: item.checkTaskId,
        },
      });
      window.open(newPage.href, "_blank");
    },
    // 表格数据删除
    deletsFDialog(row) {
      this.multipleSelection.push(row);
      this.handleDeleteAll();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 批量删除
    handleDeleteAll() {
      // let _this = this;
      // if (this.multipleSelection.length == 0) {
      //   this.$message.error("您还未选中任何数据！");
      // } else {
      //   this.$confirm("此操作将永久删除数据, 是否继续?", "提示", {
      //     confirmButtonText: "确定",
      //     cancelButtonText: "取消",
      //     type: "warning",
      //   }).then(() => {
      //     let arr = [];
      //     _this.multipleSelection.forEach((item) => {
      //       arr.push(item.id);
      //     });
      //     var formData = new FormData();
      //     formData.append("ids", arr.join());
      //     deletsFDialogApi(formData).then((res) => {
      //       if (res.code == 200) {
      //         this.$message({
      //           type: "success",
      //           message: "删除成功!",
      //         });
      //         _this.pageNum = 1;
      //         _this.multipleSelection = [];
      //         _this.getListData();
      //       } else {
      //         this.$message.error("删除失败");
      //       }
      //     });
      //   });
      // }
      this.dialogFormVisible = true;
    },
    //   获取审校专题数据
    async reviewTop() {
      if (this.tabPosition == 0) {
        // 系统专题
        let res = await getBigTitleTwoApi();
        this.reviewData = res.data;
      } else if (this.tabPosition == 1) {
        // 自定义专题
        let res = await getBigTitleThreeApi();
        this.reviewData = res.data;
      }
      // let res = await reviewTopApi();
      // this.reviewData = res.data;

      if (this.curPartId == -1) {
        this.typeName = "个人信息泄露";
      } else {
        for (let i = 0; i < this.reviewData.length; i++) {
          if (this.$route.query.id == this.reviewData[i].id) {
            this.typeName = this.reviewData[i].name;
          }
        }
      }
    },
    // 获取页面列表数据
    async getListData() {
      this.loading = true;
      // 个人信息泄漏模块
      if (this.curPartId == -1) {
        // alert("个人信息泄漏模块");
        let params = {
          pageSize: this.pageSize,
          pageNum: this.pageNum,
          startTime: this.$route.query.startTime,
          endTime: this.$route.query.endTime,
          type: this.$route.query.type,
          assignIds: this.$route.query.assignIds,
        };
        let res = await getInfoDataApi(params);
        this.getListDataList = res.rows;
        this.total = res.total;
        this.loading = false;
      } else {
        // 系统
        if (this.tabPosition == 0) {
          // alert("系统");
          let params = {
            pageSize: this.pageSize,
            pageNum: this.pageNum,
            startTime: this.$route.query.startTime,
            endTime: this.$route.query.endTime,
            type: this.$route.query.type,
            checkTaskTypeId: this.curLi,
            assignIds: this.$route.query.assignIds,
            // checkTaskTypeId: this.$route.query.assignIds,
          };
          let res = await getListDataApi(params);
          this.getListDataList = res.rows;
          this.total = res.total;
          this.loading = false;
        } else if (this.tabPosition == 1) {
          // alert("自定义");
          // 自定义
          let params = {
            checkTaskId: this.curLi,
            pageSize: this.pageSize,
            pageNum: this.pageNum,
            startTime: this.$route.query.startTime,
            endTime: this.$route.query.endTime,
            type: this.$route.query.type,
            assignIds: this.$route.query.assignIds,
            // checkTaskTypeId: this.curLi,
            // type: this.curLi,
          };
          let res = await getListDataApi(params);
          this.getListDataList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      }
    },
    changeLi(item) {
      this.curLi = item.id;
      this.typeName = item.name;
      this.getListData();
    },
    // 分页
    handleSizeChange(val) {
      this.pageSize = val;
      this.getListData();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getListData();
    },
  },
};
</script>
<style >
.el-message-box__header {
  border-bottom: solid 1px #ccc;
}
</style>
<style scoped lang="scss">
.formEl ::v-deep.el-form-item--medium .el-form-item__content {
  width: 60%;
}
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 16%;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .homeLeft {
    width: 78%;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
    .homeLeftBox {
      width: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      background: #f5f5f5;
      margin-bottom: 20px;
      .homBut {
        display: inline-block;
        padding-top: 6px;
        padding-right: 10px;
      }
      span {
        display: inline-block;
        color: #fff;
        background: #ed9e2f;
        height: 38px;
        line-height: 38px;
        padding: 0 10px;
        font-size: 14px;
      }
    }
  }
  .homeRight {
    background: #fff;
    width: 20%;
    span {
      display: block;
      width: 100%;
      height: 40px;
      line-height: 40px;
      background: #2899dd;
      color: #fff;
      text-align: center;
      font-size: 14px;
    }
    ul {
      margin: 0;
      padding: 0;
      li {
        list-style: none;
        border-bottom: solid 1px #ccc;
        height: 40px;
        line-height: 40px;
        padding: 0 10px;
        box-sizing: border-box;
        font-size: 14px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      li.activeLi {
        color: #e66100;
      }
    }
  }
}
</style>