import request from '@/utils/request'

// 当前问题- 获取报告预览数据
export function getReportDataApi(data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data
    })
}
// 当前问题- 下载报告
export function createReport(data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data: data
    })
}
// 历史问题-报告预览数据
export function getReportHistoryDataApi(data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data
    })
}
// 历史问题-下载报告
export function createReportHistory(data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data: data
    })
}
