<template>
  <div class="home">
    <div class="homeFrom">
      <el-form :model="queryForm" :inline="true">
        <el-form-item prop="targetWord" label="问题词：">
          <el-input
            v-model.trim="queryForm.targetWord"
            placeholder="请输入名称"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button type="primary" @click="addList" class="mb8" size="mini"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            @click="delList"
            class="mb8"
            size="mini"
            :disabled="multiple"
            >批量删除</el-button
          >
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            >批量导入</el-button
          >
        </el-col> -->
      </el-row>

      <el-table :data="themList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="targetWord" label="问题词"> </el-table-column>
        <el-table-column prop="filterWord" label="过滤词"> </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="seeDialog(scope.row)"
              >修改</el-button
            >
            <el-button type="text" @click="delList(scope.row)" size="small"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="handleQuery"
      />
    </div>

    <!-- 新增修改弹框 -->
    <el-dialog :title="title" width="600px" :visible.sync="dialogFormVisible">
      <el-form :model="form" label-width="80px" :rules="rules" ref="formRef">
        <el-form-item label="问题词：" prop="targetWord">
          <!-- <el-input v-model="form.targetWord"></el-input>
           -->
          <el-select
            v-model="form.targetWord"
            filterable
            remote
            reserve-keyword
            placeholder="请输入问题词"
            :remote-method="remoteMethod"
            :loading="loading"
          >
            <el-option
              v-for="(item, index) in options"
              :key="index"
              :label="item.problemWord"
              :value="item.problemWord"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="过滤词" prop="filterWord">
          <el-input v-model.trim="form.filterWord"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="sureAdd" size="small"
          >确 定</el-button
        >
        <el-button @click="closeAdd" size="small">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 批量导入 -->
    <el-dialog title="批量导入过滤词" :visible.sync="dialogVisible" width="30%">
      <div style="text-align: center">
        <el-upload
          class="upload-demo"
          :action="uploadFileUrl"
          :on-preview="handlePreview"
          :on-success="handleUploadSuccess"
          :on-remove="handleRemove"
          :before-remove="beforeRemove"
          :limit="1"
          :headers="headers"
          :on-exceed="handleExceed"
          :file-list="fileList"
        >
          <el-button size="small">点击上传</el-button>
          <!-- <div slot="tip"
               class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
        </el-upload>
        <div style="text-align: center; margin-top: 40px">
          <el-button type="primary" @click="fileWebDown" size="small"
            >过滤词模板下载
            <!-- <el-tooltip
              class="item"
              effect="light"
              content="用户名称是登录账号名，如果是多个用户的话，用英文逗号隔开"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip> -->
          </el-button>
          <el-button type="primary" size="small" @click="importTemp"
            >确定</el-button
          >
          <el-button type="success" size="small" @click="ClosedialogVisible"
            >取消</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  handleFilterApi,
  AddfilterApi,
  editfilterApi,
  delfilterApi,
  seefilterApi,
  saveImportfilterApi,
  filefilterDownApi,
  getquesListApi,
} from "@/api/system/filterWord";
import { getToken } from "@/utils/auth";
export default {
  data() {
    return {
      fileList: [],
      uploadFileUrl:
        process.env.VUE_APP_BASE_API + "/task/filterWord/importWord", // 上传的文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      dialogVisible: false,
      queryForm: {
        targetWord: "",
      },
      pageNum: 1,
      pageSize: 10,
      themList: [],
      total: 0,
      dialogFormVisible: false,
      form: {
        targetWord: "",
        filterWord: "",
        id: "",
      },
      title: "新增过滤词",
      ids: [],
      single: true,
      // 非多个禁用
      multiple: true,
      rules: {
        targetWord: [
          { required: true, trigger: "blur", message: "问题词不能为空" },
        ],
        filterWord: [
          { required: true, trigger: "blur", message: "过滤词不能为空" },
        ],
      },
      options: [],
      loading: false,
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    async remoteMethod(query) {
      if (query !== "") {
        this.loading = true;
        let formData = new FormData();
        formData.append("problemWord", query);
        let res = await getquesListApi(formData);
        let queslist = res.data;
        if (res.code == 200) {
          setTimeout(() => {
            this.loading = false;
            this.options = queslist.filter((item) => {
              return (
                item.problemWord.toLowerCase().indexOf(query.toLowerCase()) > -1
              );
            });
          }, 200);
        } else {
          this.msgError(res.msg);
        }
      } else {
        this.options = [];
      }
    },
    // 批量导入按钮
    handleExport() {
      this.dialogVisible = true;
    },
    handlePreview(file) {
      console.log(file);
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      this.resCode = res.code;
      if (res.code == 200) {
        this.$message.success(res.msg);
      } else {
        this.$message.error(res.msg);
      }
      this.$emit("input", res.url);
    },
    //   文件上传
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    //   导入
    async importTemp() {
      if (this.resCode == 200) {
        let res = await saveImportfilterApi();
        if (res.code == 200) {
          this.$message.success("导入过滤词成功");
        } else {
          this.$message.error("导入过滤词失败");
        }
      }
      this.fileList = [];
      this.dialogVisible = false;
      this.handleQuery();
    },
    ClosedialogVisible() {
      this.fileList = [];
      this.dialogVisible = false;
    },
    // 过滤词模板下载
    fileWebDown() {
      filefilterDownApi();
    },
    //   搜索
    async handleQuery() {
      let params = {
        targetWord: this.queryForm.targetWord,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      let res = await handleFilterApi(params);
      this.themList = res.rows;
      this.total = res.total;
    },
    // 重置
    resetQuery() {
      this.queryForm.targetWord = "";
    },
    // 点击新增弹框
    addList() {
      this.dialogFormVisible = true;
      this.title = "新增过滤词";
      this.form.targetWord = "";
      this.form.filterWord = "";
    },
    // 新增确定按钮
    sureAdd() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.form.id) {
            // 修改
            let params = {
              targetWord: this.form.targetWord,
              filterWord: this.form.filterWord,
              id: this.form.id,
            };
            editfilterApi(params).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.dialogFormVisible = false;
                this.handleQuery();
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
                this.options = [];
              }
            });
          } else {
            //   新增
            let params = {
              targetWord: this.form.targetWord,
              filterWord: this.form.filterWord,
            };
            AddfilterApi(params).then((res) => {
              if (res.code == 200) {
                this.dialogFormVisible = false;
                this.$message.success("新增成功");
                this.options = [];
                this.handleQuery();
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
              }
            });
          }
        } else {
          this.$message.error("请输入正确信息");
        }
      });
    },
    // 取消
    closeAdd() {
      this.dialogFormVisible = false;
      this.options = [];
    },
    // 查看
    async seeDialog(row) {
      this.dialogFormVisible = true;
      this.title = "修改过滤词";
      let res = await seefilterApi(row.id);
      this.form = res.data;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 刪除
    delList(row) {
      const ids = row.id || this.ids;
      console.log("ids", ids);
      this.$confirm("是否确认删除该数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delfilterApi(ids);
        })
        .then((res) => {
          if (res.code == 200) {
            this.handleQuery();
            this.msgSuccess("删除成功");
          } else {
            this.$message.error(res.msg);
          }
        });
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: 889px;
  .homeFrom {
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
  }
}
::v-deep .upload-demo {
  display: flex;
  width: 49%;
  margin: 0 auto;
  .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;
    border: solid 1px #ccc;
    height: 32px;
    line-height: 32px;
    margin-left: 6px;
    border-radius: 5px;
    width: 200px;
    overflow: hidden;
  }
  .el-upload-list__item:first-child {
    margin-top: 0;
    height: 32px;
    line-height: 32px;
  }
}
</style>