<template>
  <div class="export-loading">
    <div class="export-con">
      <span class="export-title">正在导出</span>
      <el-progress
        :text-inside="true"
        :stroke-width="22"
        :percentage="percent"
        status="warning"
      ></el-progress>
      <!-- <el-progress
        :strokeWidth="17"
        stroke-linecap="square"
        :show-info="false"
        :percent="percent"
        status="active"
        strokeColor="#c43b3b"
      /> -->
    </div>
  </div>
</template>
<script>
export default {
  props: {
    cPercent: {
      type: Number,
      required: true,
    },
  },
  watch: {
    cPercent: {
      handler(newName, oldName) {
        this.percent = newName;
      },
      immediate: true,
    },
  },
  data() {
    return {
      percent: 0,
    };
  },
};
</script>
<style lang="scss" scoped>
.export-loading {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
}
.export-con {
  width: 380px;
  height: 50px;
  padding: 13px 15px 0px 80px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -25px;
  margin-left: -190px;
  background: #fff;
  border-radius: 5px;
  text-align: center;
  .export-title {
    position: absolute;
    left: 15px;
    top: 13px;
  }
}
::v-deep .ant-progress-inner {
  background-color: #d7d7d7;
}
</style>