import request from '@/utils/request'
// 获取审校专题数据
export function reviewTopApi () {
    return request({
        url: '/task/checkTaskInfo/listCheckTask',
        method: 'get'
    })
}
// 获取页面列表数据
export function getListDataApi (data) {
    return request({
        url: '/task/checkTaskInfo/list',
        method: 'post',
        data: data
    })
}
// 删除数据
export function deletsFDialogApi (data) {
    return request({
        url: '/task/checkTaskInfo/update',
        method: 'post',
        data: data
    })
}
// 当前问题-导出
export function exportAllApi (data) {
    return request({
        url: '/task/checkTaskInfo/export',
        method: 'post',
        data
    })
}
// 历史问题-导出
export function historyExport (data) {
    return request({
        url: '/home/<USER>',
        method: 'post',
        data
    })
}
// 导出个人信息泄露
export function exportAllApipeopleInfo (query) {
    return request({
        url: '/task/checkTaskInfo/exportPersonInfo',
        method: 'get',
        params: query
    })
}