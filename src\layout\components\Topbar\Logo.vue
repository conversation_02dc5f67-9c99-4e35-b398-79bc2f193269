<template>
  <div
    class="sidebar-logo-container"
    :class="{ collapse: collapse }"
    :style="{
      backgroundColor:
        sideTheme === 'theme-dark' ? variables.menuBg : variables.menuLightBg,
    }"
  >
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        to="/"
      >
        <img v-if="logo" :src="logo" class="sidebar-logo" />
        <h1
          v-else
          class="sidebar-title"
          :style="{
            color:
              sideTheme === 'theme-dark'
                ? variables.sidebarTitle
                : variables.sidebarLightTitle,
          }"
        >
          {{ this.$store.state.user.sysName }}
        </h1>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="">
        <!-- <img v-if="logo" :src="logo" class="sidebar-logo"> -->
        <!-- <h1 class="sidebar-title" :style="{ color: sideTheme === 'theme-dark' ? variables.sidebarTitle : variables.sidebarLightTitle }">{{ title }} </h1> -->
        <img
          class="loginTitleBlack"
          :src="
            $store.state.user.logoImg == ''
              ? logoImgTwo
              : $store.state.user.logoImg
          "
          alt=""
        />
        <!-- :src='logoImgTwo' -->
        <!-- :src="imgTheme" -->
      </router-link>
    </transition>
  </div>
</template>

<script>
import logoImg from "@/assets/logo/logo.png";
import variables from "@/assets/styles/variables.scss";

export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  data: {
    imgTheme: require("@/assets/login/loginTitleBlack.png"),
  },
  computed: {
    variables() {
      return variables;
    },
    sideTheme() {
      return this.$store.state.settings.sideTheme;
    },
  },
  watch: {
    sideTheme: {
      handler(nv, ov) {
        if (nv == "theme-dark") {
          this.imgTheme = require("@/assets/login/loginTitleBlack.png");
        } else {
          this.imgTheme = require("@/assets/login/loginTitle.png");
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      // title: "博约-智能媒体审校系统",
      logo: logoImg,
      logoImgTwo: require("@/assets/login/loginTitle2.png"),
    };
  },
};
</script>

<style lang="scss" scoped>
.loginTitleBlack {
  // padding: 19px 0px;
  // width: 380px;
  vertical-align: middle;
  // max-width: 26rem;
  max-height: 35px;
}
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 60px;
  line-height: 60px;
  background: #2b2f3a;
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo {
      width: 32px;
      height: 32px;
      vertical-align: middle;
      margin-right: 12px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      color: #fff;
      font-weight: 600;
      line-height: 50px;
      font-size: 14px;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
