<template>
  <div class="app-container">
    <div class="home">
      <el-radio-group v-model="tabPosition" style="margin-bottom: 30px" @change="changeTab">
        <el-radio-button label="1">备案网站</el-radio-button>
        <el-radio-button label="2">媒体账号</el-radio-button>
      </el-radio-group>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-folder" size="mini" plain :disabled="multiple"
            @click="moreGroup">批量分组</el-button>
        </el-col>
      </el-row>
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px"
        @submit.native.prevent>
        <el-form-item label="信源名称：" prop="name" v-show="tabPosition == 1">
          <el-input v-model.trim="queryParams.name" placeholder="请输入信源名称" clearable size="small" />
        </el-form-item>
        <el-form-item label="备案域名：" prop="host" v-show="tabPosition == 1">
          <el-input v-model.trim="queryParams.host" placeholder="请输入备案域名" clearable size="small" />
        </el-form-item>
        <el-form-item label="备案许可证号：" prop="recordLicenseNumber" v-if="tabPosition == 1">
          <el-input v-model.trim="queryParams.recordLicenseNumber" placeholder="请输入备案许可证号" clearable size="small" />
        </el-form-item>
        <el-form-item label="信源名称：" prop="accountname" v-show="tabPosition == 2">
          <el-input v-model.trim="queryParams.accountname" placeholder="请输入信源名称" clearable size="small" />
        </el-form-item>
        <el-form-item label="信源id：" prop="accountId" v-show="tabPosition == 2">
          <el-input v-model.trim="queryParams.accountId" placeholder="请输入账号id" clearable size="small" />
        </el-form-item>
        <el-form-item label="信源类型：" prop="mediaType" v-show="tabPosition == 2">
          <el-select v-model="queryParams.mediaType" placeholder="请选择信源类型" clearable size="small">
            <el-option v-for="dict in mediaTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属用户：" prop="customer" v-show="checkPermi(['monitor:platformrev:ownuser'])">
          <el-select v-model="queryParams.customer" placeholder="请选择所属用户" clearable size="small" filterable
            @change="getgroupOption">
            <el-option v-for="(dict, index) in userList" :key="index" :label="`${dict.userName}(${dict.nickName})`"
              :value="index">{{ dict.userName }} ({{ dict.nickName }})</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属分组：" prop="siteTypeId" v-show="showGroup">
          <el-select v-model="queryParams.siteTypeId" placeholder="请选择所属分组" clearable size="small">
            <el-option v-for="dict in groupOption" :key="dict.id" :label="dict.typeName" :value="dict.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8" style="display: none">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['platform:website:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['platform:website:remove']">批量删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['platform:website:export']">批量导入</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="websiteList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" :index="getIndex" width="80"></el-table-column>
        <el-table-column label="信源名称" align="center" prop="name" key="1" v-if="tabPosition == 1" />
        <el-table-column label="备案域名" align="center" prop="host" key="2" v-if="tabPosition == 1" />
        <el-table-column label="备案许可证号" align="center" prop="recordLicenseNumber" key="3" v-if="tabPosition == 1" />
        <el-table-column label="信源名称" align="center" prop="name" v-if="tabPosition == 2" key="4" />
        <el-table-column label="信源id" align="center" prop="accountId" v-if="tabPosition == 2" key="5" />
        <!-- <el-table-column label="账号昵称" align="center" prop="nickName" v-if="tabPosition == 2" key="6" /> -->
        <el-table-column label="所属分组" align="center" prop="siteTypeName" />
        <el-table-column label="信源类型" align="center" prop="mediaType" :formatter="mediaTypeFormat" v-if="tabPosition == 2"
          key="7" />
        <el-table-column label="是否采集" align="center" prop="isCollect" :formatter="isCollectFormat"
          v-if="checkPermi(['monitor:platformrev:iscllect'])" key="8" />
        <!-- 最新采集时间用户不能看，运营可以看 -->
        <el-table-column label="最新采集时间" align="center" prop="newestTime" v-if="false" key="9" />
        <el-table-column label="所属用户" align="center" prop="userName" v-if="checkPermi(['monitor:platformrev:ownuser'])"
          key="10">
          <template slot-scope="scope">
            {{ scope.row.userName }}{{scope.row.nickName?'(':''}}{{ scope.row.nickName }}{{scope.row.nickName?')':''}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" key="11">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-folder"
              @click="editMoreGroup(scope.row)">修改分组</el-button></template></el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" v-if="false" key="11">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['platform:website:edit']">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
              v-hasPermi="['platform:website:remove']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
      <!-- 批量分组及修改弹框 -->
      <el-dialog :title="titleGroup" :visible.sync="moreGroupOpen" width="600px" append-to-body
        :before-close="cancelGroup">
        <el-form :model="groupForm" :rules="groupRules" label-width="120px" ref="groupRef">
          <el-form-item label="选择分组" prop="siteTypeId">
            <SelectAdd ref="selectAdd" :actIndustryIds="groupOption" v-model="groupForm.siteTypeId" @saveActive="saveActive" @chooseActive="chooseActive"/>
            <!-- <el-select v-model="groupForm.siteTypeId" placeholder="请选择所属分组" clearable size="small"
              @change="$forceUpdate()" style="width: 100%">
              <el-option v-for="dict in groupOption" :key="dict.id" :label="dict.typeName" :value="dict.id" />
            </el-select> -->
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center">
          <el-button type="primary" @click="submitGroupForm">确 定</el-button>
          <el-button @click="cancelGroup">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 添加或修改备案网站对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body :before-close="cancel">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="信源名称" prop="name" v-show="tabPosition == 1">
            <el-input v-model.trim="form.name" placeholder="请输入信源名称" />
          </el-form-item>
          <el-form-item label="首页链接" prop="homeUrl" v-show="tabPosition == 1">
            <el-input v-model.trim="form.homeUrl" placeholder="请输入首页链接" />
          </el-form-item>
          <el-form-item label="域名" prop="host" v-show="tabPosition == 1">
            <el-input v-model.trim="form.host" placeholder="请输入域名" />
          </el-form-item>
          <el-form-item label="备案许可号" prop="recordLicenseNumber" v-show="tabPosition == 1">
            <el-input v-model.trim="form.recordLicenseNumber" placeholder="请输入备案许可号" />
          </el-form-item>
          <el-form-item label="信源名称：" prop="accountname" v-show="tabPosition == 2">
            <el-input v-model.trim="form.accountname" placeholder="请输入信源名称" />
          </el-form-item>
          <el-form-item label="信源id：" prop="accountId" v-show="tabPosition == 2">
            <el-input v-model.trim="form.accountId" placeholder="请输入信源id" />
          </el-form-item>
          <el-form-item label="信源昵称：" prop="nickName" v-show="tabPosition == 2">
            <el-input v-model.trim="form.nickName" placeholder="请输入信源昵称" />
          </el-form-item>
          <el-form-item label="媒体类型：" prop="mediaType" v-show="tabPosition == 2">
            <el-select v-model="form.mediaType" placeholder="请选择媒体类型">
              <el-option v-for="dict in mediaTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
                :value="parseInt(dict.dictValue)"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否采集" prop="isCollect">
            <el-radio-group v-model="form.isCollect">
              <el-radio v-for="(item, index) in isCollectList" :key="index" :value="item.value" :label="item.value">{{
                item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="所属用户：" prop="customer">
            <el-select v-model="form.customer" placeholder="请选择所属用户" filterable @change="getgroupOptionTwo">
              <el-option v-for="(dict, index) in userList" :key="index" :label="`${dict.userName}(${dict.nickName})`"
                :value="parseInt(dict.customer)">{{ dict.userName }} ({{ dict.nickName }})</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属分组：" prop="siteTypeId" v-show="showGroupForm">
            <el-select v-model="form.siteTypeId" placeholder="请选择所属分组" clearable size="small" @change="$forceUpdate()">
              <el-option v-for="dict in groupOptionTwo" :key="dict.id" :label="dict.typeName" :value="dict.id" />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 批量导入 -->
      <el-dialog :title="titleType" :visible.sync="dialogVisible" width="30%">
        <div style="text-align: center">
          <el-upload class="upload-demo" :action="tabPosition == 1 ? uploadFileUrl : uploadFileUrlTwo"
            :on-preview="handlePreview" :on-success="handleUploadSuccess" :on-remove="handleRemove"
            :before-remove="beforeRemove" :limit="1" :headers="headers" :on-exceed="handleExceed" :file-list="fileList">
            <el-button size="small">点击上传</el-button>
            <!-- <div slot="tip"
               class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
          </el-upload>
          <div style="text-align: center; margin-top: 40px">
            <el-button type="primary" @click="fileWebDown" size="small">{{ tabPosition == 1 ? "备案网站模板下载" : "媒体账号模板下载" }}
              <el-tooltip class="item" effect="light" content="用户名称是登录账号名，如果是多个用户的话，用英文逗号隔开" placement="top">
                <i class="el-icon-question" />
              </el-tooltip>
            </el-button>
            <el-button type="primary" size="small" @click="importTemp">确定</el-button>
            <el-button type="success" size="small" @click="ClosedialogVisible">取消</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listWebsite,
  getWebsite,
  delWebsite,
  addWebsite,
  updateWebsite,
  exportWebsite,
  fileWebDownApi,
  saveImportWebsiteApi,
  getOwnListApi,
  submitFenFormApi,
} from "@/api/platManage/filWeb";
import { listUser, listUserNew } from "@/api/system/user";
import {
  listMediacount,
  getMediacount,
  delMediacount,
  addMediacount,
  updateMediacount,
  exportMediacount,
  fileMedDownApi,
  saveImportMediaAccountApi,
  groupOptionApi,
  submitGroupFormApi,
} from "@/api/platManage/medMang";
import { IsURL } from "@/utils/validate.js";
import { getToken } from "@/utils/auth";
import { checkPermi } from "@/utils/permission.js";
import { getInfo } from "@/api/login";
import { addSitePlanApi } from "@/api/system/sitePlan";
export default {
  name: "Website",
  components: {},
  data () {
    return {
      roles: [],
      showGroup: false,
      groupOption: [],
      groupOptionTwo: [],
      disMul: true,
      userList: [],
      titleType: "批量导入备案网站",
      mediaTypeOptions: [],
      tabPosition: 1,
      fileList: [],
      uploadFileUrl:
        process.env.VUE_APP_BASE_API + "/platform/website/importWebsite", // 上传的文件服务器地址
      uploadFileUrlTwo:
        process.env.VUE_APP_BASE_API +
        "/platform/mediacount/importMediaAccount", // 上传的文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      dialogVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      idsName: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 备案网站表格数据
      websiteList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否采集字典
      isCollectOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // 信源名称
        name: null,
        // 备案域名
        host: null,
        // 备案许可证号
        recordLicenseNumber: null,
        // 账号名称
        accountname: "",
        // 账号id
        accountId: "",
        // 账号类型
        mediaType: "",
        // 所属用户
        customer: '',
        // 所属分组
        siteTypeId: "",
      },
      showGroupForm: false,
      // 表单参数
      form: {
        name: "",
        homeUrl: "",
        host: "",
        recordLicenseNumber: "",
        accountname: "",
        accountId: "",
        nickName: "",
        mediaType: "",
        isCollect: "1",
        customer: null,
        siteTypeId: "",
      },
      isCollectList: [
        { name: "是", value: "1" },
        { name: "否", value: "0" },
      ],
      // 表单校验
      rules: {
        name: [
          { required: true, message: "信源名称不能为空", trigger: "blur" },
        ],
        homeUrl: [
          { required: true, message: "首页链接不能为空", trigger: "blur" },
        ],
        host: [{ required: true, trigger: "blur" }],
        // recordLicenseNumber: [
        //   { required: true, message: '备案许可号不能为空', trigger: 'blur' },
        // ],
        isCollect: [{ required: true, message: "是否采集", trigger: "blur" }],
      },
      resCode: "",
      moreGroupOpen: false,
      groupForm: {
        siteTypeId: "",
      },
      customer: "",
      groupOption: [],
      groupRules: {
        siteTypeId: [
          { required: true, message: "所属分组不能为空", trigger: "blur" },
        ],
      },
      titleGroup: "批量分组",
      lineId: "",
      nowCustor: '',
      curcustomer: ''
    };
  },
  async created () {
    this.getList();
    await this.getInfoData();
    this.getDicts("by_website_collect").then((response) => {
      this.isCollectOptions = response.data;
    });
    this.getDicts("by_media_media_type").then((response) => {
      this.mediaTypeOptions = response.data;
    });
   await this.getInfoData();//修复第一次加载时userList未获取的bug，不完善
  },
  watch: {
    tabPosition: {
      handler (newv, oldv) {
        if (newv == 1) {
          this.rules = {
            name: [
              { required: true, message: "信源名称不能为空", trigger: "blur" },
            ],
            homeUrl: [
              { required: true, message: "首页链接不能为空", trigger: "blur" },
            ],
            host: [{ required: true, trigger: "blur" }],
            // recordLicenseNumber: [
            //   { required: true, message: '备案许可号不能为空', trigger: 'blur' },
            // ],
            isCollect: [
              { required: true, message: "是否采集", trigger: "blur" },
            ],
          };
        } else if ((newv = 2)) {
          this.rules = {
            accountname: [
              { required: true, message: "信源名称不能为空", trigger: "blur" },
            ],
            accountId: [
              { required: true, message: "账号id不能为空", trigger: "blur" },
            ],
            nickName: [
              { required: true, message: "账号昵称不能为空", trigger: "blur" },
            ],
            mediaType: [
              {
                required: true,
                message: "媒体类型不能为空",
                trigger: "change",
              },
            ],
          };
        }
      },
      immediate: true,
    },
    // "form.customer": {
    //   async handler(val) {
    //     if (val) {
    //       let params = {
    //         // type: this.tabPosition,
    //         customer: val,
    //       };
    //       let res = await groupOptionApi(params);
    //       this.groupOptionTwo = res.rows;
    //       this.showGroupForm = true;
    //       if ((this.title = "添加备案网站")) {
    //         this.form.type = this.groupOptionTwo[0].id;
    //       }
    //     } else {
    //       this.showGroupForm = false;
    //     }
    //   },
    //   immediate: true,
    // },
    // "queryParams.customer": {
    //   handler (val) {
    //     if (val) {
    //       this.showGroup = true;
    //       this.getgroupOption(this.queryParams.customer);
    //     }
    //   },
    //   immediate: true,
    // },
  },
  methods: {
    checkPermi,
    // 修改分组
    async editMoreGroup (row) {
      this.lineId = row.id;
      this.moreGroupOpen = true;
      this.titleGroup = "修改分组";
      this.groupOption = await this.getfenGroup(row.customer);
      this.groupForm.siteTypeId = await this.getOwnList(row.id, this.customer);
      this.groupOption.map((item)=>{
      if(item.id ==this.groupForm.siteTypeId){
        this.groupForm.siteTypeId = item.typeName
                item.tag = true
      }else{
        item.tag = false
      }
      })
    },
    async getOwnList (siteId, customer) {
      let aa = "";
      let params = {
        siteId: siteId,
        customer: customer,
      };
      let res = await getOwnListApi(params);
      if (res.code == 200) {
        aa = res.data[0].id;
      }
      return aa;
    },
    // 批量分组按钮
    async moreGroup () {
      this.moreGroupOpen = true;
      this.titleGroup = "批量分组";
      this.groupOption = await this.getfenGroup(this.customer);
    },
    // 添加分类
    saveActive(addActive){
       let customerId = null
       this.userList.map((item)=>{
        if(item.userId==this.queryParams.customer){
          customerId= item.customer
          return
        }
      })
      if(addActive){
          addSitePlanApi({
              typeName: addActive,
              customer: customerId}).then((res)=>{
            if(res.code==200){
                this.$message.success(res.msg);
                this.$refs.selectAdd.resetAddActive();
                // this.addActive = null;
            let params = {
            // type: this.tabPosition,
            customer: this.customerId,
          };
          groupOptionApi({customer:this.customer}).then(res=>{
            this.groupOption = res.rows
            this.groupOption.map((item)=>{
                if(item.typeName==this.groupForm.siteTypeId){
                this.$set(item,'tag',true)
                }else{
                    this.$set(item,'tag',false)
                }
            })
                })
              }
          })
      }else{
          this.msgInfo('请输入分组')
      }
    },
    // 选中分类
    chooseActive(item,index){
        this.$forceUpdate();
        this.groupOption.map((itema)=>{
            if(itema==item){
                itema.tag = true
            }else{
                itema.tag = false
            }
        })
    },

    // 获取分组数据
    async getfenGroup (val) {
      let aa = [];
      let params = {
        customer: val,
      };
      let res = await groupOptionApi(params);
      if (res.code == 200) {
        aa = res.rows;
      }
      return aa;
    },
    // 确定批量分组按钮
    submitGroupForm () {
      this.$refs["groupRef"].validate(async (valid) => {
        if (valid) {
          if (this.titleGroup == "批量分组") {
            let siteTypeId =this.selectDictId(this.groupOption,this.groupForm.siteTypeId)
            let params = {
              siteTypeId: siteTypeId,
              sourceId: this.ids.join(),
              customer: this.customer,
            };
            console.log(params,'params');
            let res = await submitGroupFormApi(params);
            if (res.code == 200) {
              this.msgSuccess("批量分组成功");
              this.moreGroupOpen = false;
              this.getList();
              this.groupForm.siteTypeId = "";
            } else {
              this.msgError(res.msg);
            }
          } else if (this.titleGroup == "修改分组") {
            let siteTypeId =this.selectDictId(this.groupOption,this.groupForm.siteTypeId)
            let params = {
              siteTypeIdList: [siteTypeId],
              sourceId: this.lineId,
              customer: this.customer,
            };
            let res = await submitFenFormApi(params);
            if (res.code == 200) {
              this.msgSuccess("修改成功成功");
              this.moreGroupOpen = false;
              this.getList();
              this.groupForm.siteTypeId = "";
            } else {
              this.msgError(res.msg);
            }
          }
        } else {
          this.msgError("请输入正确信息");
        }
      });
    },
    // 取消批量分组
    cancelGroup () {
      this.groupForm.siteTypeId = "";
      this.moreGroupOpen = false;
    },
    // 获取用户信息
    async getInfoData () {
      let res = await getInfo();
      this.roles = res.roles;
      this.customer = res.user.customer;
      for (let i = 0; i < this.roles.length; i++) {
        if (
          this.roles[i] == "admin" ||
          this.roles[i] == "yunying" ||
          this.roles[i] == "shichang"
        ) {
          // 管理员
        } else {
          // 普通用户
          this.showGroup = true;
          let aa = null
          this.userList.map((item, index) => {
            if (item.customer == res.user.customer) {
              aa = index
              this.nowCustor = item.customer
            }
          })
          this.queryParams.customer = aa
          // = parseInt(res.user.customer);
          this.$forceUpdate()

          console.log( this.userList,res.user.customer,this.nowCustor)

          this.getgroupOption(this.nowCustor);
        }
      }

      this.getUserList(res.user.customer);
    },
    // 所属分组切换
    async getgroupOption (val) {
      this.userList.map((item, index) => {
        if (index == val) {
          this.curcustomer = item.customer
        }
      })
      if (val) {
        this.queryParams.siteTypeId = "";
        this.showGroup = true;
        let params = {
          // type: this.tabPosition,
          customer: this.curcustomer
        };
        let res = await groupOptionApi(params);
        if (res.code == 200) {
          this.groupOption = res.rows;
        }
      } else {
        this.showGroup = false;
      }
    },
    async getgroupOptionTwo (val) {
      if (val) {
        this.form.siteTypeId = "";
        this.showGroupForm = true;
        let params = {
          customer: this.form.customer,
        };
        let res = await groupOptionApi(params);
        if (res.code == 200) {
          this.groupOptionTwo = res.rows;
          if ((this.title = "添加备案网站")) {
            this.form.siteTypeId = this.groupOptionTwo[0].id;
          } else {
            this.form.siteTypeId = "";
          }
        }
      } else {
        this.showGroupForm = false;
      }
    },

    // 获取所属用户
    async getUserList (resCustomer) {
      let params = {
        customer: resCustomer
      }
      // let res = await listUser();
      let res = await listUserNew(params);
      this.userList = res.rows;

    },
    // 媒体类型字典翻译
    mediaTypeFormat (row, column) {
      return this.selectDictLabel(this.mediaTypeOptions, row.mediaType);
    },
    // 切换备案网站、媒体账号
    changeTab (val) {
      // this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: null,
        host: null,
        recordLicenseNumber: null,
        accountname: "",
        // 账号id
        accountId: "",
        mediaType: "",
        customer: this.queryParams.customer,
        siteTypeId: "",
      },
      this.resCode = "";
      this.getList();
      // this.showGroup = false;
      this.getInfoData();
    },
    getIndex (index) {
      return (
        (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
      );
    },
    //   导入
    async importTemp () {
      if (this.tabPosition == 1) {
        if (this.resCode == 200) {
          let res = await saveImportWebsiteApi();
          if (res.code == 200) {
            this.$message.success("导入备案网站成功");
          } else {
            this.$message.error("导入备案网站失败");
          }
        }
      } else if (this.tabPosition == 2) {
        if (this.resCode) {
          let res = await saveImportMediaAccountApi();
          if (res.code == 200) {
            this.$message.success("导入媒体账号成功");
          } else {
            this.$message.error("导入媒体账号失败");
          }
        }
      }
      this.fileList = [];
      this.dialogVisible = false;
      this.getList();
    },
    ClosedialogVisible () {
      this.fileList = [];
      this.dialogVisible = false;
    },
    //   文件上传
    handleRemove (file, fileList) {
      console.log(file, fileList);
    },
    // 上传成功回调
    handleUploadSuccess (res, file) {
      this.resCode = res.code;
      if (res.code == 200) {
        this.$message.success(res.msg);
      } else {
        this.$message.error(res.msg);
      }
      this.$emit("input", res.url);
    },
    handlePreview (file) {
      console.log(file);
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length
        } 个文件`
      );
    },
    beforeRemove (file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    /** 查询备案网站列表 */
    getList () {
      this.loading = true;
      if (this.tabPosition == 1) {
        // 备案网站
        let params = {
          name: this.queryParams.name,
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          host: this.queryParams.host,
          recordLicenseNumber: this.queryParams.recordLicenseNumber,
          customer: this.curcustomer,
          siteTypeId: this.queryParams.siteTypeId,
        };
        listWebsite(params).then((response) => {
          this.websiteList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      } else if (this.tabPosition == 2) {
        // 媒体账号
        let params = {
          name: this.queryParams.accountname,
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          accountId: this.queryParams.accountId,
          mediaType: this.queryParams.mediaType,
          customer: this.curcustomer,
          siteTypeId: this.queryParams.siteTypeId,
        };
        listMediacount(params).then((response) => {
          this.websiteList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    // 是否采集字典翻译
    isCollectFormat (row, column) {
      return this.selectDictLabel(this.isCollectOptions, row.isCollect);
    },

    // 取消按钮
    cancel () {
      this.open = false;
      this.showGroupForm = false;
      this.reset();
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        name: null,
        accountname: "",
        homeUrl: null,
        host: null,
        recordLicenseNumber: null,
        areaCode: null,
        isCollect: "1",
        customer: null,
        siteTypeId: "",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map((item) => item.id);
      this.idsName = selection.map((item) => item.name);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset();
      this.open = true;
      if (this.tabPosition == 1) {
        this.title = "添加备案网站";
      } else if (this.tabPosition == 2) {
        this.title = "添加媒体账号";
      }
    },
    /** 修改按钮操作 */
    async handleUpdate (row) {
      this.reset();
      const id = row.id || this.ids;
      if (this.tabPosition == 1) {
        let res = await getWebsite(id);
        if (res.code == 200) {
          this.form = res.data;
          this.form.customer = parseInt(res.data.customer);
          this.form.isCollect = res.data.isCollect.toString();
          if (this.form.customer) {
            let params = {
              customer: this.form.customer,
            };
            let resType = await groupOptionApi(params);
            if (resType.code == 200) {
              this.groupOptionTwo = resType.rows;
              this.showGroupForm = true;
              this.form.siteTypeId = res.data.siteTypeId;
            }
          }

          this.open = true;
          this.title = "修改备案网站";
        }
      } else {
        getMediacount(id).then((res) => {
          this.form = res.data;
          this.form.customer = parseInt(res.data.customer);
          this.form.accountname = res.data.name;
          this.form.isCollect = res.data.isCollect.toString();
          this.form.mediaType = parseInt(res.data.mediaType);
          if (res.data.siteTypeId) {
            this.showGroupForm = true;
            this.form.siteTypeId = res.data.siteTypeId;
          }
          this.open = true;
          this.title = "修改媒体账号";
        });
      }
    },
    /** 提交按钮 */
    submitForm () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            if (this.tabPosition == 1) {
              let params = {
                id: this.form.id,
                name: this.form.name,
                homeUrl: this.form.homeUrl,
                host: this.form.host,
                recordLicenseNumber: this.form.recordLicenseNumber,
                isCollect: this.form.isCollect,
                customer: this.form.customer,
                siteTypeId: this.form.siteTypeId,
              };
              updateWebsite(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("修改备案网站成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.msgError(res.msg);
                  this.open = false;
                }
              });
            } else if (this.tabPosition == 2) {
              let params = {
                id: this.form.id,
                name: this.form.accountname,
                accountId: this.form.accountId,
                nickName: this.form.nickName,
                mediaType: this.form.mediaType,
                isCollect: this.form.isCollect,
                customer: this.form.customer,
                siteTypeId: this.form.siteTypeId,
              };
              updateMediacount(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("修改媒体账号成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.msgError(res.msg);
                  this.open = false;
                }
              });
            }
          } else {
            // 新增备案网站
            if (this.tabPosition == 1) {
              let params = {
                name: this.form.name,
                homeUrl: this.form.homeUrl,
                host: this.form.host,
                recordLicenseNumber: this.form.recordLicenseNumber,
                isCollect: this.form.isCollect,
                customer: this.form.customer,
                siteTypeId: this.form.siteTypeId,
              };
              addWebsite(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增备案网站成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.msgError(res.msg);
                  this.open = false;
                }
              });
              //   新增媒体账号
            } else if (this.tabPosition == 2) {
              let params = {
                name: this.form.accountname,
                accountId: this.form.accountId,
                nickName: this.form.nickName,
                mediaType: this.form.mediaType,
                isCollect: this.form.isCollect,
                customer: this.form.customer,
                siteTypeId: this.form.siteTypeId,
              };
              addMediacount(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增媒体账号成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.msgError(res.msg);
                  this.open = false;
                }
              });
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      const ids = row.id || this.ids;
      let idsName = this.idsName.join(",");
      //   const name = row.name || idsName;
      this.$confirm(
        `是否确认删除该${this.tabPosition == 1 ? "备案网站" : "媒体账号"
        }的数据项?`,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(async () => {
        if (this.tabPosition == 1) {
          let res = await delWebsite(ids);
          if (res.code == 200) {
            this.getList();
            this.msgSuccess("删除备案网站成功");
          } else {
            this.msgError(res.msg);
          }
        } else {
          let res = await delMediacount(ids);
          if (res.code == 200) {
            this.getList();
            this.msgSuccess("删除媒体账号成功");
          } else {
            this.msgError(res.msg);
          }
        }
      });
    },
    /** 导出按钮操作 */
    handleExport () {
      this.dialogVisible = true;
      if (this.tabPosition == 1) {
        this.titleType = "批量导入备案网站";
      } else {
        this.titleType = "批量导入媒体账号";
      }
      //   const queryParams = this.queryParams
      //   this.$confirm('是否确认导出所有备案网站数据项?', '警告', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning',
      //   })
      //     .then(function () {
      //       return exportWebsite(queryParams)
      //     })
      //     .then((response) => {
      //       this.download(response.msg)
      //     })
    },
    // 备案网站模板下载
    fileWebDown () {
      if (this.tabPosition == 1) {
        fileWebDownApi();
      } else {
        fileMedDownApi();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.app-container {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: calc(100vh - 60px);
}

.home {
  width: 100%;
  overflow: hidden;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
}

::v-deep .upload-demo {
  display: flex;
  width: 49%;
  margin: 0 auto;

  .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;
    border: solid 1px #ccc;
    height: 32px;
    line-height: 32px;
    margin-left: 6px;
    border-radius: 5px;
    width: 200px;
    overflow: hidden;
  }

  .el-upload-list__item:first-child {
    margin-top: 0;
    height: 32px;
    line-height: 32px;
  }
}
</style>
