import request from '@/utils/request'
// 获取数据网站 新媒体数据
export function getWebDataApi (query) {
    return request({
        url: '/task/checkTaskInfo/listWebsite',
        method: 'get',
        params: query
    })
}
// 获取新媒体数据
export function getMedDataApi (query) {
    return request({
        url: '/task/checkTaskInfo/listMediaAccount',
        method: 'get',
        params: query
    })
}
// 获取所有要展示的大标题
export function getBigTitleApi (query) {
    return request({
        url: '/task/checkTaskInfo/listCheckTask',
        method: 'get',
        params: query
    })
}
// 系统
export function getBigTitleTwoApi (data) {
    return request({
        url: '/task/checkTaskType/listCheckTaskType',
        method: 'post',
        data: data
    })
}
// 自定义
export function getBigTitleThreeApi (query) {
    return request({
        url: '/task/checkTask/listCheckTask',
        method: 'get',
        params: query
    })
}

// 获取所要展示的小标题
export function getLittleDataApi (data) {
    return request({
        url: '/task/checkTaskInfo/list',
        method: 'post',
        data: data
    })
}
// 获取个人信息泄漏模块数据
export function getInfoDataApi (data) {
    return request({
        url: '/task/checkTaskInfo/listPersonInfo',
        method: 'post',
        data: data
    })
}
// 获取查询专题管理列表页数据
export function getManageListDataApi (query) {
    return request({
        url: '/task/checkTaskType/list',
        method: 'get',
        params: query
    })
}