<template>
  <div class="top-right-btn">
    <el-row>
      <el-tooltip
        class="item"
        effect="dark"
        :content="showSearch ? '隐藏搜索' : '显示搜索'"
        placement="top"
      >
        <el-button
          size="mini"
          circle
          icon="el-icon-search"
          @click="toggleSearch()"
        />
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="刷新" placement="top">
        <el-button
          size="mini"
          circle
          icon="el-icon-refresh"
          @click="refresh()"
        />
      </el-tooltip>
      <el-tooltip
        class="item"
        effect="dark"
        content="显隐列"
        placement="top"
        v-if="columns"
      >
        <el-button
          size="mini"
          circle
          icon="el-icon-menu"
          @click="showColumn()"
        />
      </el-tooltip>
    </el-row>
    <el-dialog :title="title" :visible.sync="open" append-to-body width="580px">
      <el-transfer
        :titles="['显示', '隐藏']"
        v-model="value"
        :data="columns"
        ref="transfer"
        @change="dataChange"
      ></el-transfer>
      <div class="transFoot">
        <el-button type="primary" @click="sureSet">确定</el-button>
        <el-button type="primary" @click="cancelSet">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "RightToolbarUser",
  data() {
    return {
      // 显隐数据
      value: [],
      // 弹出层标题
      title: "显示/隐藏",
      // 是否显示弹出层
      open: false,
    };
  },
  props: {
    showSearch: {
      type: Boolean,
      default: true,
    },
    columns: {
      type: Array,
      default: () => [],
    },
    // columnFor: {}
  },
  watch: {
    columns(val) {
      if (val && val.length) {
        val.forEach((item) => {
          if (item.visible == false) {
            this.value.push(item.key);
            this.value = [...new Set(this.value)];
          }
        });
      }
    },
  },
  methods: {
    // 搜索
    toggleSearch() {
      this.$emit("update:showSearch", !this.showSearch);
    },
    // 刷新
    refresh() {
      this.$emit("queryTable");
    },
    // 右侧列表元素变化
    dataChange(data) {},
    // 打开显隐列dialog
    showColumn() {
      this.open = true;
      this.columns.forEach((item) => {
        if (item.visible == false) {
          this.value.push(item.key);
        }
      });
      this.value = [...new Set(this.value)];
    },
    // 表格设置按钮
    sureSet() {
      for (var item in this.columns) {
        const key = this.columns[item].key;
        this.columns[item].visible = !this.value.includes(key);
      }
      // let self = this;
      // let columnTo = {};
      // self.columns.forEach((item) => {
      //   let key = item.label;
      //   columnTo[key] = item.visible;
      // });
      console.log(self.columnFor);
      this.$emit("sureTabSet", this.columns);
      this.$emit("update:columns", this.columns);
      // this.$emit("update:columnFor", columnTo);
      this.open = false;
    },
    // 取消设置
    cancelSet() {
      this.open = false;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-transfer__button {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}
::v-deep .el-transfer__button:first-child {
  margin-bottom: 10px;
}
.transFoot {
  width: 100%;
  margin: 20px;
  text-align: center;
}
</style>
