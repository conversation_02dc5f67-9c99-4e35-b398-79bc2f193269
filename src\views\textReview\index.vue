<template>
  <div class="fileBox">
    <el-radio-group v-show="shouldDisplayRadioGroup" v-model="tabPosition" class="radio-group">
      <el-radio-button label="1" v-hasPermi="['textReview:text']">文本</el-radio-button>
      <el-radio-button label="2" v-hasPermi="['textReview:pic']">图片</el-radio-button>
      <el-radio-button label="3" v-hasPermi="['textReview:audio']">音频</el-radio-button>
      <el-radio-button label="4" v-hasPermi="['textReview:video']">视频</el-radio-button>
    </el-radio-group>

    <div v-if="tabPosition == 1">
      <keep-alive>
        <textCom :shouldDisplayRadioGroup="shouldDisplayRadioGroup"></textCom>
      </keep-alive>
    </div>
    <div v-if="tabPosition == 2">
      <keep-alive>
        <picCom></picCom>
      </keep-alive>
    </div>
    <div v-if="tabPosition == 3">
      <keep-alive>
        <audioCom></audioCom>
      </keep-alive>
    </div>
    <div v-if="tabPosition == 4">
      <keep-alive>
        <vidCom></vidCom>
      </keep-alive>
    </div>
  </div>
</template>
<script>
import textCom from "./components/textCom";
import picCom from "./components/picCom";
import audioCom from "./components/audioCom";
import vidCom from "./components/vidCom";
export default {
  name: "textReview",
  components: {
    textCom,
    picCom,
    audioCom,
    vidCom,
  },
  data() {
    return {
      tabPosition: "1",
      shouldDisplayRadioGroup: false,
    };
  },
  created() {
    this.initializeTabPosition();
  },
  methods: {
    initializeTabPosition() {
      const permissions = this.$store.getters.permissions;
      const allPermission = "*:*:*";
      const optionPermissions = [
        { label: "1", permisison: ["textReview:text"] },
        { label: "2", permisison: ["textReview:pic"] },
        { label: "3", permisison: ["textReview:audio"] },
        { label: "4", permisison: ["textReview:video"] },
      ];

      // 检查是否具有所有权限
      if (permissions.includes(allPermission)) {
        this.tabPosition = "1"; // 示例中默认选中“文本”选项，可根据实际需求调整
        this.shouldDisplayRadioGroup = true;
        return;
      }
      // 有权限的选项计数
      let permissionCount = 0;
      let firstMatchedOptionIndex = -1; // 记录第一个匹配项的索引

      for (let i = 0; i < optionPermissions.length; i++) {
        const option = optionPermissions[i];
        if (permissions.some((permission) => option.permisison.includes(permission))) {
          permissionCount++;
          if (firstMatchedOptionIndex === -1) {
            firstMatchedOptionIndex = i; // 记录第一个匹配项的索引
          }
        }
      }

      // 如果只有一个权限，或没有权限，则不显示 el-radio-group
      if (permissionCount === 1) {
        this.tabPosition = optionPermissions[firstMatchedOptionIndex].label; // 设置选中的标签位置
        this.shouldDisplayRadioGroup = false;
      } else {
        // 若找到多个匹配项，则显示 el-radio-group，并选择第一个匹配项
        this.tabPosition = optionPermissions[firstMatchedOptionIndex].label;
        this.shouldDisplayRadioGroup = true;
      }
    },
  }
};
</script>

<style lang="scss" scoped>
.fileBox {
  width: 100%;
  overflow: hidden;
  padding: 2.5vh 10vh;
  box-sizing: border-box;
  background: #f4f7fb;
  min-height: calc(100vh - 60px);
}
.radio-group {
  margin-bottom: 10px;
  width: 100%;
  background: #fff;
  padding: 10px 10px;
}
</style>
