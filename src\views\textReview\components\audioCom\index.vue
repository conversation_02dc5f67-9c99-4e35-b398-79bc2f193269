<template>
  <div class="videoBox">
    <div class="videoOneFloor">
    </div>
    <div class="upVidDiv">
      <p>上传文件支持格式：mp3,一次最多上传1个文件，单个文件不得大于5G
      </p>
      <el-upload class="upload-demo" action="#" :limit="1" multiple :auto-upload="false" accept=".mp3"
        :on-change="handleChange" :on-remove="handleRemove" :headers="headers" :file-list="fileList" :on-exceed="handleExceed">
        <el-button size="small" type="primary">选择文件</el-button>
      </el-upload>

    </div>
    <div class="checkDiv">
      <el-button :disabled="fileList.length == 0" type='primary' @click="upRadio"  :loading="loading">开始检测</el-button>
      <el-button type='primary' @click="clearVideo">清空</el-button>
    </div>
    <textUseAudio :goword='goword' v-show="goword" :audio="true"></textUseAudio>
    <div class="checkDiv" v-show="goword">
      <el-button type="primary" @click="clearVideo">继续检测</el-button>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
// import textUseAudio from '../textUseAudio'
import textUseAudio from '../textCom'
import { checkAudio } from "@/api/textReview"
export default {
  components: { textUseAudio },
  data () {
    return {
      form: {},
      fileList: [],
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      goword: '',
      yinWord: [],
      loading: false,
      //  uploadFileUrl: process.env.VUE_APP_BASE_API + "/errorDataTaskQuartz/uploadVideo", // 上传的文件服务器地址
      fileFormatList:['mp3']
    }
  },
  methods: {
    handleChange (file, fileList) {
      const { name } = file
      const extension = name.substring(name.lastIndexOf(".") + 1);
      if(!this.fileFormatList.includes(extension)){
        this.$msgbox({
          title: "警告",
          message: "音频格式只支持：mp3，请重新上传。",
          type: "warning",
        });
        fileList.pop();
        return;
      }
      //获取上传文件大小
      let fileSize = Number(file.size / 1024 / 1024);
      if (fileSize > 5120) {
        this.$msgbox({
          title: "警告",
          message: "音频大小不能超过5G，请重新上传。",
          type: "warning",
        });
        fileList.pop();   //新增的图片在最后一个，所以只要删除那个最后新增的>10M的图片就可以了
        return;
      }
      this.fileList = fileList
    },
    handleRemove (file, fileList) {
      this.fileList = fileList
      console.log("filelist", this.fileList)
    },

    handleExceed (files, fileList) {
      this.$message.warning(`最多只支持一个文件，若要替换请先删除已上传的文件！`);
    },

    // 点击上传
    async upRadio () {
      if (this.fileList.length === 0) {
        this.$message.warning('请选取文件')
        return
      }
      this.loading = true
      const formData = new FormData()
      this.fileList.forEach(file => {
        formData.append('file', file.raw)
      })


      // let res = await submitUploadApi(formData)
      // // 音频转文本
      // let params = res.data
      // this.yinWord = await upRideoApi(params)
      this.yinWord = await checkAudio(formData)

      // 调用文稿审校接口
      if (this.yinWord.code==200) {
        let wordGroup = []
        this.yinWord.data.map(item => {
          wordGroup.push(item.audioText)
        })
        this.goword = wordGroup.join()
      } else {
        this.msgError(this.yinWord.msg)
      }
        this.loading=false

    },
    clearVideo () {
      this.fileList = []
      this.goword = ''
    }
  },
}
</script>

<style lang="scss" scoped>

.videoBox {
  width: 100%;
  overflow: hidden;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;

  .upVidDiv {
    width: 100%;
    overflow: hidden;
    background: #EEEFF1;
    border-bottom: solid 1px #DAE1E9;
    padding: 20px;

    p {
      color: #737780;
      padding: 0 10px;
    }
  }

  .checkDiv {
    width: 100%;
    overflow: hidden;
    text-align: center;
    margin: 20px 0;
  }
}
</style>
