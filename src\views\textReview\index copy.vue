<template>
  <div class="home">
    <div class="homeLeft">
      <div class="homeBox">
        <div class="homeTop">
          <el-button type="primary" @click="textClick(1)" size="small">
            清空内容
          </el-button>
          <el-button type="primary" @click="textClick(2)" size="small">
            导入文件
          </el-button>
          <el-button type="primary" @click="textClick(3)" size="small">
            查看示例
          </el-button>
          <el-button type="warning" @click="customClick" size="small">
            添加自定义词
          </el-button>
          <!-- <el-button type="warning" @click="weChatLink" size="small">
            微信公众号测试链接
          </el-button> -->
          <!-- v-hasPermi="['textReview:custom']" -->
        </div>
        <div>
          <editor id="noticeContentOne" />
          <!-- <vue-ueditor-wrap
            v-model="msg"
            :config="editorConfig"
            editor-id="ueditorWrap"
          ></vue-ueditor-wrap> -->
        </div>
      </div>
      <div class="editBut">
        <el-button type size="small" @click="textUse(1)" :loading="copyLoading"
          >一键复制</el-button
        >
        <el-button
          type="primary"
          size="small"
          :loading="checkLoading"
          @click="textUse(2)"
          >文本检测</el-button
        >
      </div>
    </div>
    <div class="homeRight">
      <h2 class="hoRiH2">检测结果</h2>
      <div class="homeBoxCount" v-if="resultData">
        <div class="textCount" id="textCount">
          <p class="pDiff">
            检测字数统计:
            <span>{{ resultData.contentLength }}</span>
          </p>
          <div class="homeFlex">
            <div>
              <span>{{ resultData.infoCount.totalCount }}</span>
              <p>全部疑似错误</p>
            </div>
            <!-- <div>
              <span>{{ resultData.infoCount.serverCount }}</span>
              <p>严重错误</p>
            </div>
            <div>
              <span>{{ resultData.infoCount.generalCount }}</span>
              <p>一般错误</p>
            </div>-->
            <div>
              <span>
                {{
                  resultData.infoCount.serverCount +
                  resultData.infoCount.generalCount
                }}
              </span>
              <p>系统错误</p>
            </div>
            <div>
              <span>{{ resultData.infoCount.selfDefinedCount }}</span>
              <p>自定义错误</p>
            </div>
          </div>
          <p>
            未处理：
            <span>{{ resultData.infoCount.totalCount }}</span>
          </p>
          <div
            class="misTableCheck"
            v-for="(item, index) in this.tipsList"
            :key="index"
          >
            <h3 class="zhengzhiEdit" :id="tipsList[index].id">
              {{ tipsList[index].name }}
            </h3>
            <el-table
              :data="tipsList[index].wordList"
              style="width: 100%"
              v-if="isreloadTable"
            >
              <el-table-column label="序号" type="index"></el-table-column>
              <el-table-column label="识别结果">
                <template slot-scope="scope">
                  <el-popover
                    trigger="hover"
                    placement="top"
                    width="300"
                    :disabled="isShowPoppver(scope.row.mistakeWord, 8)"
                  >
                    <p>{{ scope.row.mistakeWord }}</p>

                    <div
                      slot="reference"
                      :class="scope.row.isAcitve ? 'addClass' : ''"
                    >
                      <!-- <p class="ellipsis" v-html="scope.row.mistakeWord"></p> -->
                      <div
                        @click="checkTableList(scope.row)"
                        :id="`list_${scope.row.jumpId}`"
                        :class="scope.row.isAcitve ? 'active' : ''"
                        style="
                          cursor: pointer;
                          display: -webkit-box;
                          overflow: hidden;
                          white-space: normal !important;
                          text-overflow: ellipsis;
                          word-wrap: break-word;
                          -webkit-line-clamp: 2;
                          -webkit-box-orient: vertical;
                        "
                      >
                        {{ scope.row.mistakeWord }}
                      </div>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column label="建议内容" prop="correctWord">
                <template slot-scope="scope">
                  <el-popover
                    trigger="hover"
                    placement="top"
                    :disabled="isShowPoppver(scope.row.correctWord, 8)"
                  >
                    <p style="max-width: 300px">{{ scope.row.correctWord }}</p>
                    <div slot="reference" class="ellipsis">
                      {{ scope.row.correctWord | ellipsis(24, 1) }}
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column label="释义" prop="remark">
                <template slot-scope="scope">
                  <el-popover
                    trigger="hover"
                    placement="top"
                    width="300"
                    :disabled="isShowPoppver(scope.row.remark, 10)"
                  >
                    <p style="max-width: 300px">{{ scope.row.remark }}</p>
                    <div slot="reference" class="ellipsis">
                      {{ scope.row.remark | ellipsis(20, 2) }}
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
              <el-table-column label width="200px" prop>
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    @click="addPoWord(scope.row, $event)"
                    :id="`add_${scope.row.jumpId}`"
                    type="primary"
                    >添加正确词</el-button
                  >
                  <el-button
                    v-if="scope.row.correctWord"
                    size="mini"
                    @click="replaceWord(scope.row, $event)"
                    type="primary"
                    >替换</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div v-else class="homeBoxCountTwo">暂无数据</div>
    </div>
    <!-- 导入文件弹窗 -->
    <el-dialog title="导入文件" :visible.sync="dialogVisible" width="30%">
      <el-upload
        class="upload-demo"
        drag
        accept=".doc, .txt, .docx"
        :action="uploadFileUrl"
        :on-preview="handlePreview"
        :on-success="handleUploadSuccess"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :limit="1"
        :on-change="handleChange"
        :headers="headers"
        :on-exceed="handleExceed"
        :file-list="fileList"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
        <div class="el-upload__tip" slot="tip">
          只能上传word/txt文件，且不超过10MB,单次导入最多200000字
        </div>
      </el-upload>

      <span slot="footer" class="dialog-footer">
        <el-button @click="canceldialogVisible">取 消</el-button>
        <el-button type="primary" @click="suredialogVisible">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 添加正词 -->
    <el-dialog width="30%" :visible.sync="dialogFormVisible">
      <div slot="title" class="header-title">
        <span class="name">
          添加正确词
          <el-tooltip placement="top-start" effect="light">
            <div
              slot="content"
              style="width: 200px; height: auto; padding: 5px"
            >
              <span>请添加您认为正确的词</span>
            </div>
            <span>
              <i class="el-icon-question"></i>
            </span>
          </el-tooltip>
          <!-- <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="请添加您认为正确的词"
          >
          </el-popover>-->
        </span>
      </div>
      <el-form :model="form" :inline="true">
        <el-form-item label="正确词：">
          <el-input
            type="textarea"
            v-model.trim="form.name"
            placeholder="请添加正确词以替代原文中的词"
            style="width: 350px"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="canceldialogFormVisible">取 消</el-button>
        <el-button type="primary" @click="suredialogFormVisible"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <!-- <div class="customThem" @click="customClick">
      <p>添加自定</p>
      <p>义词</p>
    </div>-->
    <!-- 自定义专题弹框 -->
    <el-dialog
      title="添加自定义词"
      width="600px"
      :visible.sync="dialogFormTopicVisible"
    >
      <div slot="title" class="header-title">
        <span class="title-age">添加自定义词</span>
        <el-tooltip
          class="item"
          effect="light"
          placement="top-start"
          popper-class="tip-class"
        >
          <div slot="content" style="line-height: 24px; font-size: 12px">
            用户可自定义添加正确词、敏感词或者错
            <br />误词并进行应用监测；添加敏感词和错
            <br />误词时需提前维护对应的自定义专题
          </div>
          <span class="title-name">
            <i
              class="el-icon-question icon-report"
              style="color: #e6a23c; margin-left: 10px; cursor: pointer"
            ></i>
          </span>
        </el-tooltip>
      </div>
      <el-form
        :model="formCustom"
        label-width="120px"
        :rules="rules"
        ref="formCustomRules"
      >
        <el-form-item label-width="0" style="text-align: center">
          <el-radio-group v-model="tabPosition" @change="changeRadio">
            <el-radio-button label="1">添加正词</el-radio-button>
            <el-radio-button label="2">添加敏感词</el-radio-button>
            <el-radio-button label="3">添加错误词</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label-width="0">
          <p v-show="tabPosition == 1">
            说明：将词语添加为正词后，再出现该词语时，首页问题汇总、审校专题、
            文稿审校版块都将不会提示错误。
          </p>
          <p v-show="tabPosition == 2">
            说明：将词语添加为敏感词后，再出现该词语时，首页问题汇总、审校专题版块将会提示敏感，文稿审校版块将会提示错误但不会建议正确词语。
          </p>
          <p v-show="tabPosition == 3">
            说明：将未提示错误的词语添加入错误词库后，再出现该词语时，首页问题汇总、审校专题
            版块将会提示错误，文稿审校版块将会提示错误并给出建议正确词。
          </p>
        </el-form-item>
        <el-form-item
          label="正词："
          :prop="tabPosition == 1 ? 'properWord' : ''"
          v-show="tabPosition == 1"
        >
          <el-input
            type="textarea"
            placeholder="请输入正词,多个请用空格隔开"
            v-model="formCustom.properWord"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item
          label="敏感词："
          :prop="tabPosition == 2 ? 'sensitiveWord' : ''"
          v-show="tabPosition == 2"
        >
          <el-input
            type="textarea"
            placeholder="请输入敏感词,多个请用空格隔开"
            v-model="formCustom.sensitiveWord"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item
          label="错误词"
          :prop="tabPosition == 3 ? 'errorWord' : ''"
          v-show="tabPosition == 3"
        >
          <el-input
            type="textarea"
            v-model="formCustom.errorWord"
            placeholder="请输入错误词,多个请用空格隔开；错误词与正确词需要一一对应"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          label="建议词"
          :prop="tabPosition == 3 ? 'suggestWord' : ''"
          v-show="tabPosition == 3"
        >
          <el-input
            type="textarea"
            v-model="formCustom.suggestWord"
            placeholder="请输入建议词，多个请用空格隔开；建议词和错误词需要一一对应"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          :label="`${wordType}专题`"
          v-show="tabPosition == 2 || tabPosition == 3"
          :prop="
            (tabPosition == 2 ? 'checkTaskId' : '') ||
            (tabPosition == 3 ? 'checkTaskId' : '')
          "
        >
          <el-select
            v-model="formCustom.checkTaskId"
            :placeholder="`请选择${wordType}专题`"
            style="width: 100%"
          >
            <el-option
              v-for="item in tasksenDataOption"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: center">
        <el-button type="primary" @click="suredialogFormTopicVisible"
          >确 定</el-button
        >
        <el-button @click="canceldialogFormTopicVisible">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 导入链接弹窗 -->
    <el-dialog title="导入链接" :visible.sync="weChatLinkVisible">
      <el-form
        label-width="60px"
        label-position="left"
        :rules="{
          properWord: [{ required: true, message: '链接不能为空', trigger: 'blur' }]
        }"
      >
        <el-form-item
          label="链接："
          prop="url"
        >
          <el-input
            v-model="weChatUrl"
            type="textarea"
            placeholder="请在此输入或粘贴链接"
          />
        </el-form-item>
        请将微信公众号文章地址粘贴到输入框，系统会自动解析网站文本内容
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelWeChatDialog">取 消</el-button>
        <el-button type="primary" :loading="weChatLoading" @click="sureWeChatDialog">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import {
  textCheckApi,
  exportDocApi,
  getExampleDataApi,
  textCopy,
  weChatArticleLink,
} from "@/api/textReview";
import {
  getSensitiveClassApi,
  getSensitiveApi,
  addsensitiveWord,
  addTrueWord,
} from "@/api/system/autodict";
import Editor from "@/components/EditorCheck";
export default {
  inject: ["reload"],
  data() {
    return {
      noticeContentOne: "",
      copyLoading: false,
      // 错误词专题数据
      tasksenDataOption: [],
      formCustom: {
        // 正词
        properWord: "",
        // 敏感词
        sensitiveWord: "",
        // 错误词
        errorWord: "",
        // 建议词
        suggestWord: "",
        // 错误词分类
        // taskTypeId: "",
        // 错误词专题
        checkTaskId: "",
      },
      wordType: "敏感词",
      tabPosition: 1,
      dialogFormTopicVisible: false,
      checkLoading: false,
      downLoading: false,
      noticeContentOne: "",
      noticeContentTwo: "",
      dialogVisible: false,
      fileList: [],
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/textCheck/importDoc", // 上传的文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      resultData: null,
      serverCount: "",
      generalCount: "",
      selfDefinedCount: "",
      weakCount: "",
      totalCount: "",
      form: {
        name: "",
      },
      dialogFormVisible: false,
      addWrodId: "",
      jumpIdNow: "", // 正确词时的jumpId
      tipsList: [],
      isreloadTable: false,
      weChatLinkVisible: false,
      weChatUrl:'',
      weChatLoading:false,
      // 表单校验
      rules: {
        properWord: [
          { required: true, message: "正词不能为空", trigger: "blur" },
          {
            max: 500,
            message: "正词长度不能超过500个字符",
            trigger: "blur",
          },
        ],
        sensitiveWord: [
          { required: true, message: "敏感词不能为空", trigger: "blur" },
          {
            max: 500,
            message: "敏感词长度不能超过500个字符",
            trigger: "blur",
          },
        ],
        errorWord: [
          { required: true, message: "错误词不能为空", trigger: "blur" },
          {
            max: 500,
            message: "错误词长度不能超过500个字符",
            trigger: "blur",
          },
        ],
        suggestWord: [
          { required: true, message: "建议词不能为空", trigger: "blur" },
        ],
        // taskTypeId: [
        //   { required: true, message: "错误词分类不能为空", trigger: "blur" },
        // ],
        checkTaskId: [
          { required: true, message: "错误词专题不能为空", trigger: "blur" },
        ],
      },
      fileFormatList: ["doc", "txt", "docx"],
      uploadFileContent: "",
      msg: "",
      editorConfig: {
        UEDITOR_HOME_URL: "/UEditor/", // 访问 UEditor 静态资源的根路径，可参考常见问题1
        toolbars: [],
        // initialContent: "欢迎使用ueditor!",
        serverUrl: '', // 服务端接口
        // 如果自定义，最好给p标签如下的行高，要不输入中文时，会有跳动感
        // initialStyle:'p{line-height:1em}',//编辑器层级的基数,可以用来改变字体等
        iframeCssUrl: `/css/editor.css`, //给编辑区域的iframe引入一个css文件
        // //若实例化编辑器的页面手动修改的domain，此处需要设置为true
        // customDomain:true,
       
        autoHeightEnabled:false, // 是否自动长高,默认true
        // scaleEnabled:false,//是否可以拉伸长高,默认true(当开启时，自动长高失效)
        // minFrameWidth:800,    //编辑器拖动时最小宽度,默认800
        // minFrameHeight:220,  //编辑器拖动时最小高度,默认220

        retainOnlyLabelPasted: false, //粘贴只保留标签，去除标签所有属性
        autoClearEmptyNode: false, //getContent时，是否删除空的inlineElement节点（包括嵌套的情况）
        enableAutoSave: false, //启用自动保存
        enableContextMenu: false, //打开右键菜单功能
        elementPathEnabled: false, //是否启用元素路径，默认是显示
        wordCount: false, //是否开启字数统计
        // maximumWords: 10000, //允许的最大字符数
        wordCountMsg: "", //当前已输入 {#count} 个字符，您还可以输入{#leave} 个字符
        // 超出字数限制提示  留空支持多语言自动切换，否则按此配置显示
        wordOverFlowMsg: "", //<span style="color:red;">你输入的字符个数已经超出最大允许值，服务器可能会拒绝保存！</span>
        allowDivTransToP: false, //允许进入编辑器的div标签自动变成p标签
        rgb2Hex: true, //默认产出的数据中的color自动从rgb格式变成16进制格式
        xssFilterRules: true, // xss 过滤是否开启,inserthtml等操作
        inputXssFilter: true, //input xss过滤
        outputXssFilter: true, //output xss过滤
        // xss过滤白名单 名单来源: https://raw.githubusercontent.com/leizongmin/js-xss/master/lib/default.js
        whitList: {
          a: ["target", "href", "title", "class", "style"],
          abbr: ["title", "class", "style"],
          address: ["class", "style"],
          area: ["shape", "coords", "href", "alt"],
          article: [],
          aside: [],
          audio: [
            "autoplay",
            "controls",
            "loop",
            "preload",
            "src",
            "class",
            "style",
          ],
          b: ["class", "style"],
          bdi: ["dir"],
          bdo: ["dir"],
          big: [],
          blockquote: ["cite", "class", "style"],
          br: [],
          caption: ["class", "style"],
          center: [],
          cite: [],
          code: ["class", "style"],
          col: ["align", "valign", "span", "width", "class", "style"],
          colgroup: ["align", "valign", "span", "width", "class", "style"],
          dd: ["class", "style"],
          del: ["datetime"],
          details: ["open"],
          div: ["class", "style"],
          dl: ["class", "style"],
          dt: ["class", "style"],
          em: ["class", "style"],
          font: ["color", "size", "face"],
          footer: [],
          h1: ["class", "style"],
          h2: ["class", "style"],
          h3: ["class", "style"],
          h4: ["class", "style"],
          h5: ["class", "style"],
          h6: ["class", "style"],
          header: [],
          hr: [],
          i: ["class", "style"],
          img: [
            "src",
            "alt",
            "title",
            "width",
            "height",
            "id",
            "_src",
            "loadingclass",
            "class",
            "data-latex",
          ],
          ins: ["datetime"],
          li: ["class", "style"],
          mark: [],
          nav: [],
          ol: ["class", "style"],
          p: ["class", "style"],
          pre: ["class", "style"],
          s: [],
          section: [],
          small: [],
          span: ["class", "style"],
          sub: ["class", "style"],
          sup: ["class", "style"],
          strong: ["class", "style"],
          table: ["width", "border", "align", "valign", "class", "style"],
          tbody: ["align", "valign", "class", "style"],
          td: [
            "width",
            "rowspan",
            "colspan",
            "align",
            "valign",
            "class",
            "style",
          ],
          tfoot: ["align", "valign", "class", "style"],
          th: [
            "width",
            "rowspan",
            "colspan",
            "align",
            "valign",
            "class",
            "style",
          ],
          thead: ["align", "valign", "class", "style"],
          tr: ["rowspan", "align", "valign", "class", "style"],
          tt: [],
          u: [],
          ul: ["class", "style"],
          video: [
            "autoplay",
            "controls",
            "loop",
            "preload",
            "src",
            "height",
            "width",
            "class",
            "style",
          ],
        },
      },
    };
  },
  components: {
    Editor,
  },
  created() {
    if (this.$route.query.name == "normaluser") {
      this.getTime();
    }
    this.getTopdata();
  },
  filters: {
    ellipsis(value, limit, data) {
      if (!value) {
        if (data == 1) {
          return "无建议词";
        } else if (data == 2) {
          return "常见表述错误";
        }
      }
      // if (!value) return "无数据";
      else if (value.length > limit) {
        return value.slice(0, limit) + "...";
      } else {
        return value;
      }
    },
  },
  computed: {
    isShowPoppver() {
      return function (val, limit) {
        if (val && val.length >= limit) {
          return false;
        } else {
          return true;
        }
      };
    },
  },
  methods: {
    changeRadio(val) {
      if (val == 2) {
        this.wordType = "敏感词";
      } else if (val == 3) {
        this.wordType = "错误词";
      }
    },
    // 获取敏感词和错误词专题数据
    async getTopdata() {
      let res = await getSensitiveApi();
      this.tasksenDataOption = res.rows;
    },
    // 自定义专题确定按钮
    suredialogFormTopicVisible() {
      this.$refs["formCustomRules"].validate((valid) => {
        if (valid) {
          if (this.tabPosition == 1) {
            // 添加正词
            let params = {
              properWord: this.formCustom.properWord,
            };
            addTrueWord(params).then((res) => {
              if (res.code == 200) {
                this.msgSuccess("新增正词成功");
                this.dialogFormTopicVisible = false;
              } else {
                this.dialogFormTopicVisible = true;
                this.msgError(res.msg);
              }
            });
          } else if (this.tabPosition == 2) {
            // 添加敏感词
            if (this.formCustom.sensitiveWord && this.formCustom.checkTaskId) {
              let params = {
                problemType: 1,
                problemWord: this.formCustom.sensitiveWord,
                checkTaskId: this.formCustom.checkTaskId,
              };
              addsensitiveWord(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增敏感词成功");
                  this.dialogFormTopicVisible = false;
                } else {
                  this.msgError(res.msg);
                  this.dialogFormTopicVisible = true;
                }
              });
            } else {
              this.$message.error("请输入完整信息");
            }
          } else {
            // 添加错误词
            if (
              this.formCustom.errorWord &&
              this.formCustom.suggestWord &&
              this.formCustom.checkTaskId
            ) {
              let params = {
                problemType: 2,
                problemWord: this.formCustom.errorWord,
                suggestWord: this.formCustom.suggestWord,
                checkTaskId: this.formCustom.checkTaskId,
              };
              addsensitiveWord(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增错误词成功");
                  this.dialogFormTopicVisible = false;
                } else {
                  this.msgError(res.msg);
                  this.dialogFormTopicVisible = true;
                }
              });
            } else {
              this.$message.error("请输入完整信息");
            }
          }
        } else {
          this.$message.error("请输入完整信息");
        }
      });
    },
    // 取消自定义专题按钮
    canceldialogFormTopicVisible() {
      for (let key in this.formCustom) {
        this.$set(this.formCustom, key, "");
      }
      this.dialogFormTopicVisible = false;
    },
    customClick() {
      for (let key in this.formCustom) {
        this.$set(this.formCustom, key, "");
      }
      this.dialogFormTopicVisible = true;
      this.tabPosition = 1;
    },
    //微信公众号链接检测
    weChatLink() {
      this.weChatLinkVisible = true;
    },
    cancelWeChatDialog() {
      this.weChatLinkVisible = false;
      this.weChatUrl = ''
    },
    sureWeChatDialog() {
      this.weChatLoading = true
      const params = {
        url: this.weChatUrl,
      };
      weChatArticleLink(params).then((res) => {
        this.weChatLoading = false
        if(res.code=='200'){
          const iframeWindow = document.querySelector("#ueditorWrap #ueditor_0").contentWindow
          const {title,text} = res.data
          iframeWindow.document.querySelector("body").innerHTML = text;
        //   document.querySelector(
        //   "#noticeContentOne .ql-editor"
        // ).innerHTML = text;
          this.msgSuccess('链接解析成功');
          this.cancelWeChatDialog()
        }else{
          this.msgError(res.msg)
        }
      }).catch(err=>{
        this.weChatLoading = false
      })
      
    },
    getTime() {
      this.$store.dispatch("GetInfo").then((res) => {
        let expireTime = res.user.expireTime;
        if (expireTime) {
          let OldTime = /\d{4}-\d{1,2}-\d{1,2}/g.exec(expireTime);
          const myDate = new Date();
          const Y = myDate.getFullYear();
          const M = myDate.getMonth() + 1;
          const D = myDate.getDate();
          const curDay = Y + "-" + M + "-" + D;
          let timeResult = this.dateDiff(OldTime[0], curDay);
          if (timeResult > 0 && timeResult < 7) {
            // this.$message.warning(`账号还有${timeResult}天过期`);
            this.$notify({
              title: "警告",
              message: `账号还有${timeResult}天过期`,
              type: "warning",
              offset: 50,
            });
          }
        }
      });
    },
    //日期1减去日期2的天数.
    dateDiff(d1, d2) {
      var day = 24 * 60 * 60 * 1000;
      try {
        var dateArr = d1.split("-");
        var checkDate = new Date();
        checkDate.setFullYear(dateArr[0], dateArr[1] - 1, dateArr[2]);
        var checkTime = checkDate.getTime();

        var dateArr2 = d2.split("-");
        var checkDate2 = new Date();
        checkDate2.setFullYear(dateArr2[0], dateArr2[1] - 1, dateArr2[2]);
        var checkTime2 = checkDate2.getTime();

        var cha = (checkTime - checkTime2) / day;
        return cha;
      } catch (e) {
        return false;
      }
    },
    async textClick(val) {
      if (val == 1) {
        //   清空内容
        this.$confirm("是否确认清空内容?", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {

        // const iframeWindow = document.querySelector("#ueditorWrap #ueditor_0").contentWindow
        // iframeWindow.document.querySelector("body").innerHTML = "<p></p>"


          this.reload();
          // document.querySelector("#noticeContentOne .ql-editor").innerHTML = "";
          // this.noticeContentOne = "";
          // this.noticeContentTwo = "";
          this.resultData = null;
        });
      } else if (val == 2) {
        //   导入文件
        this.dialogVisible = true;
      } else if (val == 3) {
        //   查看示例
        // const iframeWindow = document.querySelector("#ueditorWrap #ueditor_0").contentWindow
        // iframeWindow.document.querySelector("body").innerHTML = `<p>学习贯习近平新时期中国特设社会主义思想主题教育工作会议3日在北京召开。中共中央总书记、国家主席、中央军委主席近平出席会议并发表重要讲话。他强调，强国建设、民族复兴的宏伟目标令人鼓舞、催人奋进，我们这一代共惨党人使命光荣、责任重大。我们要以这次主体教育为契机，加强党的的创新理论武装，不断提高全党马克思主意水平，不断提高党的执政能力和领导水平，为奋进新历程凝心聚力，踔厉奋发、勇毅前行，为全面建设社会主义国家现代化、全面推进中国民族伟大复兴而团结奋斗。</p>`;
        
        document.querySelector(
          "#noticeContentOne .ql-editor"
        ).innerHTML = `<p>学习贯习近平新时期中国特设社会主义思想主题教育工作会议3日在北京召开。中共中央总书记、国家主席、中央军委主席近平出席会议并发表重要讲话。他强调，强国建设、民族复兴的宏伟目标令人鼓舞、催人奋进，我们这一代共惨党人使命光荣、责任重大。我们要以这次主体教育为契机，加强党的的创新理论武装，不断提高全党马克思主意水平，不断提高党的执政能力和领导水平，为奋进新历程凝心聚力，踔厉奋发、勇毅前行，为全面建设社会主义国家现代化、全面推进中国民族伟大复兴而团结奋斗。</p>`;
        this.textUse(2);
      }
    },
    //   文件移除
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.uploadFileContent = "";
    },
    handleChange(file, fileList) {
      const { name } = file;
      const extension = name.substring(name.lastIndexOf(".") + 1);
      if (!this.fileFormatList.includes(extension)) {
        this.$msgbox({
          title: "警告",
          message: "文本格式只支持：doc,txt,docx，请重新上传。",
          type: "warning",
        });
        fileList.pop();
        return;
      }
      //获取上传文件大小
      let fileSize = Number(file.size / 1024);
      if (fileSize > 1024 * 10) {
        this.$msgbox({
          title: "警告",
          message: "文本大小不能超过10MB，请重新上传。",
          type: "warning",
        });
        fileList.pop(); //新增的图片在最后一个，所以只要删除那个最后新增的>10M的图片就可以了
        return;
      }
      this.fileList = fileList;
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code == 200) {
        this.$message.success("上传成功");
        this.uploadFileContent = res.msg;
      } else {
        this.$message.error(res.msg);
      }
    },
    handlePreview(file) {
      console.log(file);
    },
    handleExceed(files, fileList) {
      if (fileList.length) {
        this.$message.warning(`限制选择 1 个文件，请先删除文件再上传新文件`);
      } else {
        this.$message.warning(
          `限制选择 1 个文件，本次选择了 ${files.length} 个文件`
        );
      }
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    // 确定上传按钮
    suredialogVisible() {
      if (this.uploadFileContent.length != 0) {
        //判断上传列表是否为空或被删除
        // const iframeWindow = document.querySelector("#ueditorWrap #ueditor_0").contentWindow
        // iframeWindow.document.querySelector("body").innerHTML = this.uploadFileContent;
        // iframeWindow.document.querySelector("body").contentEditable = true;


        document.querySelector("#noticeContentOne .ql-editor").innerHTML =
          this.uploadFileContent;
        document.querySelector(
          "#noticeContentOne .ql-editor"
        ).contentEditable = true;
      }
      this.canceldialogVisible();
    },
    // 取消上传
    canceldialogVisible() {
      this.fileList = [];
      this.uploadFileContent = "";
      this.dialogVisible = false;
    },
    // 替换文本，添加空格,防止被富文本编辑器合并
    replaceString(str) {
      return str.replace(/<\/em><em/g, "</em>&#8203;<em");
    },
    // 文本校对
    async textUse(val) {
      // const iframeWindow = document.querySelector("#ueditorWrap #ueditor_0").contentWindow
      // let params = {
      //   content: iframeWindow.document.querySelector("body").innerHTML,
      // };


      let params = {
        content: document.querySelector("#noticeContentOne .ql-editor")
          .innerHTML,
      };
      if (val == 1) {
        // 一键复制
        if (params.content == "<p><br></p>") {
          return this.msgError("复制内容为空");
        }
        this.copyLoading = true;
        let res = await textCopy(params);
        if (res.code == 200) {
          const content = res.data;
          if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard
              .writeText(content)
              .then(() => {
                this.msgSuccess("复制成功");
              })
              .catch((error) => {
                this.msgError("复制失败");
              });
            this.copyLoading = false;
          } else {
            // 创建text area
            const textArea = document.createElement("textarea");
            textArea.value = content;
            // 使text area不在viewport，同时设置不可见
            textArea.style.position = "absolute";
            textArea.style.opacity = 0;
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            this.copyLoading = false;
            return new Promise((res, rej) => {
              // 执行复制命令并移除文本框
              document.execCommand("copy") ? res() : rej();
              textArea.remove();
              this.msgSuccess("复制成功");
            });
          }
        } else {
          this.msgError(res.msg);
        }
      } else if (val == 2) {
        // 文本检测

        this.isreloadTable = false;
        this.resultData = false;
        this.checkLoading = true;
        let res = await textCheckApi(params);
        if (res.code == 200) {
          this.$nextTick((_) => {
            this.isreloadTable = true;
          });
          this.checkLoading = false;
          this.resultData = res.data;
          this.serverCount = this.resultData.infoCount.serverCount;
          this.generalCount = this.resultData.infoCount.generalCount;
          this.selfDefinedCount = this.resultData.infoCount.selfDefinedCount;
          this.weakCount = this.resultData.infoCount.weakCount;
          this.totalCount = this.resultData.infoCount.totalCount;
          // iframeWindow.document.querySelector("body").innerHTML = this.resultData.signContent

          document.querySelector("#noticeContentOne .ql-editor").innerHTML =
            this.replaceString(this.resultData.signContent || "<p><br></p>");
          this.tipsList = [];
          this.tipsList = JSON.parse(JSON.stringify(this.resultData.tipsList));


          // const emTag = iframeWindow.document.getElementsByClassName("mET")
          // this.$nextTick((_) => {
          //   for (let i = 0; i < emTag.length; i++) {
          //     emTag[i].onclick = (e) => {
          //       let eleScrollFlag = 0; // 计数，判断匹配到的元素是否为第一个
          //       const insideEmTag = [...emTag]
          //       insideEmTag.forEach((back) => {
          //         // 设置em标签的背景色为默认
          //         back.removeAttribute("style");
          //       });
          //       console.log("onclick");
          //       if (this.tipsList) {
          //         this.tipsList.map((item, index) => {
          //           item.wordList.map((itemb, indexb) => {
          //             // 判断 wordList.posIdList 中有没有 emTag 对应的id
          //             const haveId = itemb.posIdList.includes(e.target.id);
          //             // 有相应的id
          //             if (haveId) {
          //               // 有相应id的 设置表格字体为红色
          //               this.$set(itemb, "isAcitve", true);

          //               console.log(itemb, "itemb");
          //               // 列表中第一个匹配元素滚动到可视区域
          //               if (eleScrollFlag == 0) {
          //                 const listEle = document.getElementById(
          //                   `list_${itemb.jumpId}`
          //                 );
          //                 listEle.scrollIntoView({
          //                   behavior: "smooth", // 平滑过渡
          //                   block: "start", // 上边框与视窗顶部平齐。默认值
          //                 });
          //                 eleScrollFlag++;
          //               }
          //             } else {
          //               // 没有相应的id 设置颜色为默认
          //               // item.wordList.map((itemb) => {
          //               this.$set(itemb, "isAcitve", false);
          //               // });
          //             }
          //           });
          //         });
          //       }
          //     };
          //   }
          // });


          const emTag = document
            .getElementsByClassName("mET");
          this.$nextTick((_) => {
            for (let i = 0; i < emTag.length; i++) {
              emTag[i].onclick = (e) => {
                let eleScrollFlag = 0; // 计数，判断匹配到的元素是否为第一个
                emTag.forEach((back) => {
                  // 设置em标签的背景色为默认
                  // document.getElementById(back.id).removeAttribute("style");
                  back.removeAttribute("style");
                });
                console.log("onclick");
                if (this.tipsList) {
                  this.tipsList.map((item, index) => {
                    item.wordList.map((itemb, indexb) => {
                      // 判断 wordList.posIdList 中有没有 emTag 对应的id
                      const haveId = itemb.posIdList.includes(e.target.id);
                      // 有相应的id
                      if (haveId) {
                        // 有相应id的 设置表格字体为红色
                        this.$set(itemb, "isAcitve", true);

                        console.log(itemb, "itemb");
                        // 列表中第一个匹配元素滚动到可视区域
                        if (eleScrollFlag == 0) {
                          const listEle = document.getElementById(
                            `list_${itemb.jumpId}`
                          );
                          listEle.scrollIntoView({
                            behavior: "smooth", // 平滑过渡
                            block: "start", // 上边框与视窗顶部平齐。默认值
                          });
                          eleScrollFlag++;
                        }
                      } else {
                        // 没有相应的id 设置颜色为默认
                        // item.wordList.map((itemb) => {
                        this.$set(itemb, "isAcitve", false);
                        // });
                      }
                    });
                  });
                }
              };
            }
          });

        } else {
          this.checkLoading = false;
          this.$message.error(res.msg);
        }
      }
    },
    // 点击右边设置左边高亮
    checkTableList(row) {
      // const iframeWindow = document.querySelector("#ueditorWrap #ueditor_0").contentWindow
      // const emTag = iframeWindow.document.getElementsByClassName("mET")

      const emTag = document.getElementsByClassName("mET");
      for (let i = 0; i < emTag.length; i++) {
        emTag[i].removeAttribute("style"); // 设置em标签的背景色为默认
      }

      this.tipsList.map((item) => {
        item.wordList.map((itemb) => {
          if (itemb.jumpId == row.jumpId) {
            this.$set(itemb, "isAcitve", true);
          } else {
            this.$set(itemb, "isAcitve", false);
          }
        });
      });

      let eleScrollFlag = 0;
      // 设置em标签的背景色为active
      // row.posIdList.map((item) => {
      //   if (iframeWindow.document.getElementById(item)) {
      //     iframeWindow.document.getElementById(item).style.background = "#ff4949";
      //     iframeWindow.document.getElementById(item).style.color = "#ffffff";
      //     if (eleScrollFlag == 0) {
      //       iframeWindow.document.getElementById(item).scrollIntoView({
      //         behavior: "smooth", // 平滑过渡
      //         block: "start", // 上边框与视窗顶部平齐。默认值
      //       });
      //       eleScrollFlag++;
      //     }
      //   }
      // });

      row.posIdList.map((item) => {
        if (document.getElementById(item)) {
          document.getElementById(item).style.background = "#ff4949";
          document.getElementById(item).style.color = "#ffffff";
          if (eleScrollFlag == 0) {
            document.getElementById(item).scrollIntoView({
              behavior: "smooth", // 平滑过渡
              block: "start", // 上边框与视窗顶部平齐。默认值
            });
            eleScrollFlag++;
          }
        }
      });
    },
    // 添加正词
    addPoWord(row, e) {
      // 判断所有元素是否存在
      // const iframeWindow = document.querySelector("#ueditorWrap #ueditor_0").contentWindow
      // const elementsExist = row.posIdList.every(
      //   (item) => iframeWindow.document.getElementById(item) !== null
      // );

      const elementsExist = row.posIdList.every(
        (item) => document.getElementById(item) !== null
      );
      if (elementsExist) {
        if (e.target.innerText == "添加正确词") {
          this.dialogFormVisible = true;
          this.addWrodId = row.posIdList;
          this.jumpIdNow = row.jumpId;
          this.form.name = "";
        } else {
          this.$message.warning("已添加");
        }
      } else {
        this.$message.warning("错误词在文本中已不完整，无法添加正确词");
      }
    },
    // 取消添加正词
    canceldialogFormVisible() {
      this.form.name = "";
      this.dialogFormVisible = false;
    },
    // 确定添加正确词
    suredialogFormVisible() {
      // const iframeWindow = document.querySelector("#ueditorWrap #ueditor_0").contentWindow
      // if (this.form.name) {
      //   this.addWrodId.map((item, index) => {
      //     if (index == 0) {
      //       // 创建一个新的文本节点
      //       const newText = iframeWindow.document.createTextNode(this.form.name);
      //       //文本节点替换第一个字符节点
      //       iframeWindow.document
      //         .getElementById(item)
      //         .parentElement.replaceChild(
      //           newText,
      //           iframeWindow.document.getElementById(item)
      //         );
      //     } else {
      //       iframeWindow.document
      //         .getElementById(item)
      //         .parentElement.removeChild(iframeWindow.document.getElementById(item));
      //     }
      //   });
      //   document.getElementById(`add_${this.jumpIdNow}`).innerText = "已添加";
      //   this.resultData.infoCount.totalCount--;
      //   this.dialogFormVisible = false;
      // } else {
      //   this.msgError("请输入正确词");
      // }



      if (this.form.name) {
        this.addWrodId.map((item, index) => {
          if (index == 0) {
            // 创建一个新的文本节点
            const newText = document.createTextNode(this.form.name);
            //文本节点替换第一个字符节点
            document
              .getElementById(item)
              .parentElement.replaceChild(
                newText,
                document.getElementById(item)
              );
          } else {
            document
              .getElementById(item)
              .parentElement.removeChild(document.getElementById(item));
          }
        });
        document.getElementById(`add_${this.jumpIdNow}`).innerText = "已添加";
        this.resultData.infoCount.totalCount--;
        this.dialogFormVisible = false;
      } else {
        this.msgError("请输入正确词");
      }
    },
    // 替换
    replaceWord(row, e) {
      // const iframeWindow = document.querySelector("#ueditorWrap #ueditor_0").contentWindow
      // const { correctWord, posIdList } = row;
      // if (e.target.innerText == "替换") {
      //   // 判断所有元素是否存在
      //   const elementsExist = posIdList.every(
      //     (item) => iframeWindow.document.getElementById(item) !== null
      //   );
      //   if (elementsExist) {
      //     posIdList.map((item, index) => {
      //       if (index == 0) {
      //         // 创建一个新的文本节点
      //         const newText = iframeWindow.document.createTextNode(correctWord);
      //         //文本节点替换第一个字符节点
      //         iframeWindow.document
      //           .getElementById(item)
      //           .parentElement.replaceChild(
      //             newText,
      //             iframeWindow.document.getElementById(item)
      //           );
      //       } else {
      //         iframeWindow.document
      //           .getElementById(item)
      //           .parentElement.removeChild(iframeWindow.document.getElementById(item));
      //       }
      //     });
      //     e.target.innerText = "已替换";
      //     this.resultData.infoCount.totalCount--;
      //   } else {
      //     this.$message.warning("错误词在文本中已不完整，无法替换");
      //   }
      // } else {
      //   this.$message.warning("替换过了");
      // }






      const { correctWord, posIdList } = row;
      if (e.target.innerText == "替换") {
        // 判断所有元素是否存在
        const elementsExist = posIdList.every(
          (item) => document.getElementById(item) !== null
        );
        if (elementsExist) {
          posIdList.map((item, index) => {
            if (index == 0) {
              // 创建一个新的文本节点
              const newText = document.createTextNode(correctWord);
              //文本节点替换第一个字符节点
              document
                .getElementById(item)
                .parentElement.replaceChild(
                  newText,
                  document.getElementById(item)
                );
            } else {
              document
                .getElementById(item)
                .parentElement.removeChild(document.getElementById(item));
            }
          });
          e.target.innerText = "已替换";
          this.resultData.infoCount.totalCount--;
        } else {
          this.$message.warning("错误词在文本中已不完整，无法替换");
        }
      } else {
        this.$message.warning("替换过了");
      }
    },
  },
};
</script>

<style>
.el-popover {
  /* max-height: 120px;
  overflow: auto; */
}
</style>

<style scoped lang="scss">
::v-deep #edui1_iframeholder{
  height: 55vh;
}
::v-deep .mET {
  font-style: normal !important;
  font-weight: normal;
  color: rgb(255, 0, 0);
  cursor: pointer;

  * {
    color: rgb(255, 0, 0) !important;
    background-color: #ffffff !important;
  }
}

.ellipsis {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.customThem {
  position: absolute;
  left: 7px;
  bottom: 45%;
  width: 80px;
  background: #fff;
  border-radius: 5px;
  cursor: pointer;
  text-align: center;
  padding: 8px 0px;
  box-sizing: border-box;

  p {
    display: block;
    line-height: 26px;
    margin: 0;
    padding: 0;
  }
}

.customThem:hover {
  background: #3d9ffe;

  p {
    color: #fff;
  }
}

.addClass {
  color: red;
}

::v-deep .ql-editor {
  height: 55vh;
  overflow-y: scroll;
}
::v-deep .view  {
  white-space: pre-wrap !important;
  height: 55vh;
  overflow-y: scroll;
  p {
    white-space: pre-wrap !important;
  }
}

// ::v-deep .el-textarea__inner {
//   border: none;
// }
.ql-container {
  height: 80%;
}

.homeBoxCountTwo {
  width: 100%;
  height: 100%;
  vertical-align: middle;
  line-height: 45vh;
  text-align: center;
}

.misTableCheck {
  width: 100%;
  overflow: hidden;

  h3.zhengzhiEdit {
    display: block;
    padding: 0;
    font-weight: normal;
    height: 2.5vh;
    line-height: 2.5vh;
    font-size: 14px;
    margin: 1vh;
  }

  p.descStyle {
    display: -webkit-box;
    overflow: hidden;
    white-space: normal !important;
    text-overflow: ellipsis;
    word-wrap: break-word;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.home {
  width: 100%;
  overflow: hidden;
  padding: 1.5vh 6vh;
  box-sizing: border-box;
  background: #f4f7fb;
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  // border: solid 1px #ccc;
  .homeBox {
    width: 100%;
    overflow: hidden;
    #noticeContentOne {
      font-size: 1em;
    }
  }

  .homeTop {
    width: 100%;
    overflow: hidden;
    padding: 1.5vh;
    box-sizing: border-box;
    border: solid 1px #ccc;
  }

  .homeText {
    width: 100%;
    overflow: hidden;
    border: none;
    height: 60vh;
  }

  .homeLeft {
    width: 56%;
    background: #fff;
    padding: 2vh;
    box-sizing: border-box;
    height: 86vh;

    .editBut {
      width: 100%;
      overflow: hidden;
      text-align: center;
      margin-top: 2vh;
    }
  }

  .homeRight {
    width: 43%;
    background: #fff;

    h2.hoRiH2 {
      display: block;
      font-weight: normal;
      font-size: 18px;
      width: 100%;
      height: 5vh;
      line-height: 5vh;
      background: #3d9ffe;
      color: #fff;
      margin: 0;
      padding: 0 2vh;
      box-sizing: border-box;
    }

    .homeBoxCount {
      width: 100%;
      overflow: hidden;
      padding: 2vh;
      box-sizing: border-box;
      max-height: 79vh;
      overflow-y: scroll;

      .textCount {
        width: 100%;
        overflow: hidden;

        p.pDiff {
          width: 100%;
          overflow: hidden;
          color: #333333;
          height: 30px;
          line-height: 30px;
          font-size: 14px;
          margin: 10px;

          span {
            display: inline-block;
            margin: 0 6px;
          }
        }

        .homeFlex {
          width: 100%;
          overflow: hidden;
          display: flex;
          flex-direction: row;
          justify-content: space-between;

          div {
            width: 32%;
            text-align: center;
            height: 46px;
            font-size: 14px;
            overflow: hidden;
            padding-top: 5px;
            background-size: 100% 100%;

            span {
              display: block;
              line-height: 20px;
            }

            p {
              display: block;
              margin: 0;
              line-height: 18px;
              font-size: 12px;
            }
          }

          div:nth-child(1) {
            background: url("../../assets/textReview/divOne.png") no-repeat
              center center;
            color: #fff;
            background-size: 100% 100%;

            p {
              color: #fff;
            }
          }

          div:nth-child(2) {
            background: url("../../assets/textReview/divTwo.png") no-repeat
              center center;
            color: #f9805c;
            background-size: 100% 100%;

            p {
              color: #f9805c;
            }
          }

          div:nth-child(3) {
            background: url("../../assets/textReview/divThree.png") no-repeat
              center center;
            color: #f9b81d;
            background-size: 100% 100%;

            p {
              color: #f9b81d;
            }
          }

          div:nth-child(4) {
            background: url("../../assets/textReview/divFour.png") no-repeat
              center center;
            color: #3d9ffe;
            background-size: 100% 100%;

            p {
              color: #3d9ffe;
            }
          }

          div:nth-child(5) {
            background: url("../../assets/textReview/divFive.png") no-repeat
              center center;
            color: #6978f9;
            background-size: 100% 100%;

            p {
              color: #6978f9;
            }
          }
        }
      }
    }
  }
}
</style>
