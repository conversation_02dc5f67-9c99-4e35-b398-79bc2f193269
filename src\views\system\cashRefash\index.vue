<template>
  <div class="home">
    <div class="homeHead"><span>缓存刷新</span></div>
    <div class="homeBox">
      <div>
        <img src="./../../../assets/images/beian.png" alt="" />
        <h3>网站备案缓存</h3>
        <p>更新时间：{{ webSiteRefreshTime }}</p>
        <el-button size="small" icon="el-icon-refresh" @click="getWebRefresh"
          >刷新</el-button
        >
      </div>
      <div>
        <img src="./../../../assets/images/meiti.png" alt="" />
        <h3>媒体账号缓存</h3>
        <p>更新时间：{{ accountRefreshTime }}</p>
        <el-button size="small" @click="getMedRefresh" icon="el-icon-refresh"
          >刷新</el-button
        >
      </div>
      <div>
        <img src="./../../../assets/images/jiankongci.png" alt="" />
        <h3>刷新词库缓存</h3>
        <p>更新时间：{{ wordRefreshTime }}</p>
        <el-button size="small" @click="getWordRefresh" icon="el-icon-refresh"
          >刷新</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import {
  getWebRefreshApi,
  getMedRefreshApi,
  getWordRefreshApi,
  getUpdataTimeApi,
} from "@/api/system/cashRefash";
export default {
  data() {
    return {
      webSiteRefreshTime: "",
      accountRefreshTime: "",
      wordRefreshTime: "",
    };
  },
  created() {
    this.getUpdataTime();
  },
  methods: {
    //   网站备案缓存
    getWebRefresh() {
      getWebRefreshApi().then((res) => {
        if (res.code == 200) {
          this.$message.success("刷新成功");
        }
      });
    },
    // 媒体账号缓存
    getMedRefresh() {
      getMedRefreshApi().then((res) => {
        if (res.code == 200) {
          this.$message.success("刷新成功");
        }
      });
    },
    // 刷新词过滤缓存
    getWordRefresh() {
      getWordRefreshApi().then((res) => {
        if (res.code == 200) {
          this.$message.success("刷新成功");
        }
      });
    },
    // 获取更新时间
    async getUpdataTime() {
      let res = await getUpdataTimeApi();
      this.webSiteRefreshTime = res.data.webSiteRefreshTime;
      this.accountRefreshTime = res.data.accountRefreshTime;
      this.wordRefreshTime = res.data.wordRefreshTime;
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: 889px;
  .homeHead {
    width: 100%;
    overflow: hidden;
    height: 60px;
    padding: 15px 20px;
    box-sizing: border-box;
    background: #fff;
    margin-bottom: 20px;
    span {
      display: inline-block;
      border-left: solid 3px #3d9ffe;
      height: 30px;
      line-height: 30px;
      padding-left: 10px;
    }
  }
  .homeBox {
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: wrap;
    div {
      text-align: center;
      width: 19%;
      background: #fff;
      padding: 40px 0px;
      box-sizing: border-box;
      margin-right: 1%;
      img {
      }
      h3 {
        font-weight: normal;
      }
      p {
        width: 100%;
        overflow: hidden;
        text-align: center;
        font-size: 14px;
        color: #999;
      }
    }
  }
}
</style>