import request from '@/utils/request'

// 查询备案网站列表
export function handleQueryApi (query) {
    return request({
        url: '/manage/siteType/list',
        method: 'get',
        params: query
    })
}
// 新增确定
export function addSitePlanApi (data) {
    return request({
        url: '/manage/siteType',
        method: 'post',
        data: data
    })
}

// 删除
export function handleDeleteApi (ids) {
    return request({
        url: '/manage/siteType/' + ids,
        method: 'delete'
    })
}
// 修改
export function editSitePlanApi (data) {
    return request({
        url: '/manage/siteType',
        method: 'put',
        data: data
    })
}
// 获取所属用户接口
export function userTypeApi (query) {
    return request({
        url: '/manage/siteType/list',
        method: 'get',
        params: query
    })
}
// 查看列表

export function handleUpdateApi (id) {
    return request({
        url: '/manage/siteType/' + id,
        method: 'get',
    })
}
// 查询分组下是否有站点
export function checkExistApi (data) {
    return request({
        url: '/manage/siteType/checkExist',
        method: 'post',
        data: data
    })
}
