import request from '@/utils/request'

// 查询任务管理列表
export function getDetailDataApi(query) {
    return request({
        url: '/task/checkTaskInfo/getInfo',
        method: 'get',
        params: query
    })
}

export function getDetailDataHisApi(query) {
    return request({
        url: '/home/<USER>',
        method: 'get',
        params: query
    })
}

export function getDetailDataTwoApi(query) {
    return request({
        url: '/task/checkTaskInfo/getInfoBySolrId',
        method: 'get',
        params: query
    })
}

// 查询微信任务管理列表
export function getWechatDetailDataApi(data) {
    return request({
        url: '/task/checkTaskInfo/getWechatInfo',
        method: 'post',
        data: data
    })
}
// 获取微信appid
export function getSignature(data) {
    return request({
        url: '/wechat/share/query',
        method: 'post',
        data,
        isToken:false
    })
}
// 获取列表
export function queryWechatList(data){
    return request({
        url: '/task/checkTaskInfo/getPushInfo',
        method: 'post',
        data,
        isToken:false
    })
}