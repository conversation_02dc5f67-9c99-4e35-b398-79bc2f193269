<template>
  <div class="home">
    <div class="homeFrom">
      <el-form :model="queryForm" :inline="true">
        <el-form-item prop="name" label="名称：">
          <el-input
            v-model.trim="queryForm.name"
            placeholder="请输入名称"
            size="small"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            v-hasPermi="['system:themClass:query']"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-row :gutter="10">
        <el-col :span="1.5">
          <el-button
            type="primary"
            @click="addList"
            class="mb8"
            size="mini"
            v-hasPermi="['system:themClass:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            @click="delList"
            class="mb8"
            size="mini"
            v-hasPermi="['system:themClass:del']"
            >批量删除</el-button
          >
        </el-col>
      </el-row>

      <el-table :data="themList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="name" label="名称"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间"> </el-table-column>
        <el-table-column prop="updateTime" label="更新时间"> </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="seeDialog(scope.row)"
              v-hasPermi="['system:themClass:edit']"
              >修改</el-button
            >
            <el-button
              type="text"
              @click="delList(scope.row)"
              size="small"
              v-hasPermi="['system:themClass:del']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="pageNum"
        :limit.sync="pageSize"
        @pagination="handleQuery"
      />
    </div>

    <!-- 新增修改弹框 -->
    <el-dialog :title="title" width="600px" :visible.sync="dialogFormVisible">
      <el-form :model="form" label-width="80px" :rules="rules" ref="formRef">
        <el-form-item label="名称：" prop="name">
          <el-input v-model.trim="form.name"></el-input>
        </el-form-item>
        <el-form-item label="备注：" prop="remark">
          <el-input type="textarea" v-model="form.remark"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="sureAdd" size="small"
          >确 定</el-button
        >
        <el-button @click="closeAdd" size="small">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  handleQueryApi,
  sureAddApi,
  editAddApi,
  delListApi,
  seeListApi,
} from "@/api/system/themClass";
export default {
  data() {
    return {
      queryForm: {
        name: "",
      },
      pageNum: 1,
      pageSize: 10,
      themList: [],
      total: 0,
      dialogFormVisible: false,
      form: {
        name: "",
        remark: "",
        id: "",
      },
      title: "新增专题分类",
      ids: [],
      single: true,
      // 非多个禁用
      multiple: true,
      rules: {
        name: [{ required: true, trigger: "blur", message: "名称不能为空" }],
        // remark: [{ required: true, trigger: "blur", message: "备注不能为空" }],
      },
    };
  },
  created() {
    this.handleQuery();
  },
  methods: {
    //   搜索
    async handleQuery() {
      let params = {
        name: this.queryForm.name,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      let res = await handleQueryApi(params);
      this.themList = res.rows;
      this.total = res.total;
    },
    // 重置
    resetQuery() {
      this.queryForm.name = "";
    },
    // 点击新增弹框
    addList() {
      this.dialogFormVisible = true;
      this.title = "新增专题分类";
      this.form.name = "";
      this.form.remark = "";
    },
    // 新增确定按钮
    sureAdd() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (this.form.id) {
            // 修改
            let params = {
              name: this.form.name,
              remark: this.form.remark,
              id: this.form.id,
            };
            editAddApi(params).then((res) => {
              if (res.code == 200) {
                this.$message.success("修改成功");
                this.dialogFormVisible = false;
                this.handleQuery();
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
              }
            });
          } else {
            //   新增
            let params = {
              name: this.form.name,
              remark: this.form.remark,
            };
            sureAddApi(params).then((res) => {
              if (res.code == 200) {
                this.dialogFormVisible = false;
                this.$message.success("新增成功");
                this.handleQuery();
                for (let key in this.form) {
                  this.$set(this.form, key, "");
                }
              }
            });
          }
        } else {
          this.$message.error("请输入正确信息");
        }
      });
    },
    // 取消
    closeAdd() {
      this.dialogFormVisible = false;
    },
    // 查看
    async seeDialog(row) {
      this.dialogFormVisible = true;
      this.title = "修改专题分类";
      let res = await seeListApi(row.id);
      this.form = res.data;
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 刪除
    delList(row) {
      const ids = row.id || this.ids;
      console.log("ids", ids);
      this.$confirm("是否确认删除该数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delListApi(ids);
        })
        .then((res) => {
          if (res.code == 200) {
            this.handleQuery();
            this.msgSuccess("删除成功");
          } else {
            this.$message.error(res.msg);
          }
        });
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: 889px;
  .homeFrom {
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 20px;
    box-sizing: border-box;
  }
}
</style>