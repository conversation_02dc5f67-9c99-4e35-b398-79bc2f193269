<template>
  <div class="home">
    <div class="homeBox" v-loading.fullscreen.lock="chartLoad">
      <div class="homeOne">
        <!-- <img
          src="../../../assets/images/boyuelogo.png"
          id="webPage"
          ref="webPage"
          alt=""
        /> -->
        <el-button size="small" class="dowReport" type="primary" @click="downloadReport"
          :disabled="disDown">下载报告</el-button>
      </div>
      <div class="homeTwo">
        <h2>{{ AllData.customerName }}</h2>
        <h2>智能媒体审校报告</h2>
      </div>
      <div class="homeThree">
        <!-- <p>安徽博约信息科技股份有限公司</p> -->
      </div>
      <div class="homeFour">
        <!-- <p>{{ nowData }}</p> -->
        <h2 class="homeFourTit" id="maxTitle1" ref="maxTitle1">
          一、 {{ serarData.type == 0 ? "网站" : "媒体账号" }}概况
        </h2>
        <div class="content" id="content1" ref="content1">
          本报告的数据监测对象为{{ AllData.customerName }}各类{{
            serarData.type == 0 ? "网站" : "媒体账号"
          }}，共计{{ AllData.websiteNum }}个。
          报告数据分析来源于中国科学技术大学舆情管理研究中心,智能媒体审校系统平台。此次监测时段为{{ AllData.startTime }}至{{ AllData.endTime }}，总共错误数为{{edited.total}}个,已修改{{edited.state1or3}}个,待修改{{edited.state0}}个，被正词过滤数{{edited.rightWord}}个。
        </div>
        <!-- <div class="imgs">
          <img
            id="webPage"
            ref="webPage"
            src="../../../assets/images/webIndex.png"
            alt=""
            srcset=""
          />
          <p id="imgTitle1" ref="imgTitle1" class="img-des">图1 审校系统截图</p>
        </div> -->
        <h2 id="maxTitle2" ref="maxTitle2">
          二、 {{ serarData.type == 0 ? "网站" : "媒体账号" }}监测情况
        </h2>
        <h3 id="title1" ref="title1">(一) 总体情况</h3>
        <h4 id="minTitle1" ref="minTitle1">1.纵向分析</h4>
        <div class="content" id="content2" ref="content2">
          此次监测时段为{{ AllData.startTime }}至{{ AllData.endTime }},
          系统平台对{{ AllData.customerName }}内各{{
            serarData.type == 0 ? "网站" : "媒体账号"
          }}稿件内容进行筛选，排查出{{
  AllData.wrongTaskInfoNum
}}条存在问题内容的媒体稿件。 纵向时间趋势如下图可见。{{
  AllData.customerName
}}网络内容问题数量总体上呈{{
  AllData.trendIndex == 1
  ? "下降"
  : AllData.trendIndex == 2
    ? "平稳"
    : AllData.trendIndex == 3
      ? "上升"
      : ""
}}趋势， 在{{ AllData.maxMonth }}月份达到峰值。
        </div>
        <div class="imgs">
          <div id="echart" ref="linechart" class="echartsbox"></div>
          <p id="imgTitle1" ref="imgTitle1" class="img-des">
            图1 监测月份内的内容审核数量图
          </p>
        </div>
        <div class="mt40"></div>
        <h4 id="minTitle2" ref="minTitle2">2.横向分析</h4>
        <div class="content" id="content3" ref="content3">
          其中，筛选排查出{{ AllData.wrongSourceNum }}个{{
            serarData.type == 0 ? "网站" : "媒体账号"
          }}（见图2）在审校中出现问题。 经筛选，共计审校出{{
  AllData.wrongTaskInfoNum
}}条问题内容。
          <template v-if="AllData.firstKey && AllData.secondKey">其中，{{ AllData.firstKey }}和{{ AllData.secondKey
          }}{{ serarData.type == 0 ? "网站" : "媒体账号" }}审校问题较突出，
            分别占比{{ AllData.first }}和{{ AllData.second }}。
          </template>
          <template v-if="AllData.maxKey">
            其中{{ AllData.maxKey }}为主要审校问题。
          </template>
        </div>
        <div class="imgs">
          <!-- 横向图表1 -->
          <!-- <div id="question" ref="linechartTwo" class="echartsbox"></div>
          <p id="imgTitle2" ref="imgTitle2" class="img-des" style="margin-bottom:60px;" >图2 存在审校问题的网站媒体</p> -->
          <!-- 横向图表2 -->
          <div id="question" ref="linechartTwo" class="echartsboxmsg"></div>
          <p id="imgTitle2" ref="imgTitle2" class="img-des">
            图2 存在审校问题的数量信息图示
          </p>
        </div>
        <h2 id="title2" ref="title2">
          (二）{{ serarData.type == 0 ? "网站" : "媒体账号" }}问题归类
        </h2>
        <div class="content" id="content4" ref="content4">
          根据审校内容，主要问题有
          <ul class="contUl">
            <li v-for="(item, index) in countMapKey" :key="index">
              {{ item }}{{ countMapKey.length - 1 == index ? "" : "," }}
            </li>
          </ul>
          等{{ countMapKey.length }}类问题，其中占比最高的为<em>{{
            AllData.maxKey
          }}</em>问题，占比约<em>{{ AllData.max }}</em>；
          <!-- 此外， -->
          <template v-if="countMapKey.length != 1">此外，
            <ul class="contUl">
              <li v-for="(item, index) in AllData.taskTypeOther" :key="index">
                {{ item }},
              </li>
            </ul>
            分别占比
            <ul class="contUl">
              <li v-for="(item, index) in AllData.taskTypeRateOther" :key="index">
                {{ item
                }}{{ AllData.taskTypeRateOther.length - 1 == index ? "" : "," }}
              </li>
            </ul>
            。
          </template>
          <div v-if="!AllData.sensitiveTaskList" class="sensitive">
            其中，
            <ul class="contUl">
              <li v-for="(item, index) in AllData.sensitiveTaskList" :key="index">
                {{ item
                }}{{ AllData.sensitiveTaskList.length - 1 == index ? "" : "," }}
              </li>
            </ul>
            涉政治敏感性较高，表述应更为谨慎。
          </div>
        </div>
        <div class="imgs">
          <div id="website" class="echartsbox" ref="linechartThree"></div>
          <p id="imgTitle3" ref="imgTitle3" class="img-des" style="margin-bottom: 60px">
            图3 审校问题归类
          </p>
          <div class="mt40"></div>
        </div>

        <!-- <img :src="chart4" alt="">
        <img :src="chart5" alt=""> -->
        <div class="quest-table" ref="chart4">
          <!-- <el-table :data="tableData" style="width: 100%" max-height="300">
            <template v-for="(item,index) in tableConfig">
              <el-table-column :label="item.label" :prop="item.label" align="center">
                <template v-for="itema in item.children">
                  <el-table-column v-if="itema.children && itema.children.length"  :coloumn-header="itema"></el-table-column>
                  <el-table-column v-else :label="itema.label" :prop="itema.prop" align="center"></el-table-column>
                </template>
              </el-table-column>
            </template>
          </el-table> -->
          <dynamic-table :table-data="tableData" :table-header="tableConfig"></dynamic-table>
        </div>
        <p id="imgTitle4" ref="imgTitle4" class="img-des">
          图4 存在审校问题的数量信息表
        </p>
        <div class="content" id="content5">
          <p ref="content5">
            根据系统校对结果，针对各网站以及新媒体问题统计进行划分，该报告用图示和表示的形式进行呈现，
            一方面能更直观地看见各网站问题数量对比，另一方面也有具体的数据呈现，可对应进行审校分析。
          </p>
          <h2 id="title3" ref="title3">(三) 重点审校突出展示</h2>
          <div class="quest-table" ref="chart5">
            <!-- <dynamic-table
              :table-data="keyWrongInfoList"
              :table-header="keyWrongInfoHead"
            ></dynamic-table> -->
            <el-table ref="singleTable" :data="keyWrongInfoList" highlight-current-row border style="width: 100%">
              <el-table-column type="index" label="序号" width="50">
              </el-table-column>
              <el-table-column prop="siteName" label="网站名称" width="120" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="title" label="标题" width="120" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="time" label="发布时间"> </el-table-column>
              <el-table-column prop="url" label="链接地址" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="checkTaskName" label="关键内容" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="suggestWord" label="建议词" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="checkTaskType" label="问题类别">
              </el-table-column>
              <el-table-column prop="wrongType" label="错误类型">
                <template slot-scope="scope">
                  <div v-if="scope.row.wrongType == '3'">严重错误</div>
                  <div v-if="scope.row.wrongType == '2'">一般错误</div>
                  <div v-if="scope.row.wrongType == '1'">自定义错误</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <h2 id="title4" ref="title4">(四) 重大问题审校情况</h2>
          <div class="content" id="content6" ref="content6">
            根据排查情况，此监测时间跨度内，共涉及{{
              AllData.keyWrongInfoNum
            }}个{{
  serarData.type == 0 ? "网站" : "媒体账号"
}}出现政治类表述错误，相关宣传人员需认真校对，在公开报道中应做到规范表述。
          </div>
          <div class="mt40"></div>
          <!-- <p class="tl">
            附件：{{ AllData.customerName }}排查审校政务网站信息表
          </p>
          <el-table :data="AllData.webSiteList" height="600">
            <el-table-column type="index"></el-table-column>
            <el-table-column prop="name"></el-table-column>
            <el-table-column prop="homeUrl"></el-table-column>
          </el-table> -->
        </div>
        <h2 id="maxTitle3" ref="maxTitle3">三、 审校问题总结</h2>
        <div class="content" id="content7" ref="content7">
          此次重点审查了{{ AllData.websiteNum }}家{{
            serarData.type == 0 ? "网站" : "媒体账号"
          }}内容。 根据排查情况，此监测时间跨度内，{{
  AllData.websiteNum - Number(AllData.wrongWebSiteNum)
}}个{{
  serarData.type == 0 ? "网站" : "媒体账号"
}}内容未发现明显编辑错误，校对工作到位，后续保持正常内容输出同时，注意信息无错误引导。
          此外{{ AllData.wrongWebSiteNum }}个{{
            serarData.type == 0 ? "网站" : "媒体账号"
          }}出现不同种类的编辑问题，部分网站发现一些政治类表述错误，建议网站负责人予以重视，及时对该类问题进行处理。
        </div>
        <div></div>
      </div>
    </div>
  </div>
</template>

<script>
import domtoimage from "dom-to-image";

// import html2Canvas from 'html2canvas';
import {
  getReportDataApi,
  createReport,
  getReportHistoryDataApi,
  createReportHistory
} from "@/api/conentReview/indexPageSee";
import DynamicTable from "./components/DynamicTable";

export default {
  components: {
    DynamicTable,
  },
  data () {
    return {
      webImage: "",
      disDown: true,
      serarData: {},
      AllData: {},
      nowData: "",
      endYear: "",
      countMapKey: [],
      countMapValue: [],
      tableData: [],
      tableConfig: [],
      chart1: "",
      chart2: "",
      chart3: "",
      chart4: "",
      chart5: "",
      wrongWords: [],
      keyWrongInfoList: [],
      questionTab: '',
      chartLoad: false,
      edited: {
        rightWord: 0,
        state0: 0,
        state1or3: 0,
        total:0
      }
    };
  },
  created () {
    this.questionTab = this.$route.query.questionTab
    this.serarData = JSON.parse(sessionStorage.getItem("reportQueryForm"));
    // this.getReportData();
    this.getDate();
  },
  mounted () {
    this.getReportData();
    this.image2Base64();
  },
  methods: {
    // 下载报告
    async downloadReport () {
      let params = {
        type: this.serarData.type,
        // title: "智能媒体审校报告", //报告标题
        title: `智能媒体审校报告`, //报告小标题
        company: "", //安徽博约信息科技股份有限公司
        time: '', //this.nowData报告时间
        // minTitle: `${this.AllData.customerName}智能媒体审校报告`, //报告小标题
        maxTitle1: this.replaceHtml(this.$refs.maxTitle1.innerText),
        maxTitle2: this.replaceHtml(this.$refs.maxTitle2.innerText),
        maxTitle3: this.replaceHtml(this.$refs.maxTitle3.innerText),
        title1: this.replaceHtml(this.$refs.title1.innerText),
        title2: this.replaceHtml(this.$refs.title2.innerText),
        title3: this.replaceHtml(this.$refs.title3.innerText),
        title4: this.replaceHtml(this.$refs.title4.innerText),
        minTitle1: this.replaceHtml(this.$refs.minTitle1.innerText),
        minTitle2: this.replaceHtml(this.$refs.minTitle2.innerText),
        imgTitle1: this.replaceHtml(this.$refs.imgTitle1.innerText),
        imgTitle2: this.replaceHtml(this.$refs.imgTitle2.innerText),
        imgTitle3: this.replaceHtml(this.$refs.imgTitle3.innerText),
        imgTitle4: this.replaceHtml(this.$refs.imgTitle4.innerText),
        // imgTitle5: this.replaceHtml(this.$refs.imgTitle5.innerText),
        // imgTitle6: this.replaceHtml(this.$refs.imgTitle6.innerText),
        content1: this.replaceHtml(this.$refs.content1.innerText),
        content2: this.replaceHtml(this.$refs.content2.innerText),
        content3: this.replaceHtml(this.$refs.content3.innerText),
        content4: this.replaceHtml(this.$refs.content4.innerText),
        content5: this.replaceHtml(this.$refs.content5.innerText),
        content6: this.replaceHtml(this.$refs.content6.innerText),
        content7: this.replaceHtml(this.$refs.content7.innerText),
        // content8: this.replaceHtml(this.$refs.content8.innerText),
        // content9: this.replaceHtml(this.$refs.content9.innerText),
        // img1: logoImg,
        // img2: this.webImage,
        chart1: this.chart1,
        chart2: this.chart2,
        chart3: this.chart3,
        chart4: this.chart4,
        chart5: this.chart5,
        taskData:
          this.AllData.countMap.length == 0
            ? "[]"
            : this.transMap(this.AllData.countMap, 1), //countMap
        siteVerifyList:
          this.AllData.siteVerifyList.length == 0
            ? "[]"
            : this.transMap(this.AllData.siteVerifyList, 2), //siteVerifyList
        webSiteInfo:
          this.AllData.webSiteList.length == 0
            ? "[]"
            : this.transMap(this.AllData.webSiteList, 3), //webSiteList
      };
      var formData = new FormData();
      formData.append("json", JSON.stringify(params));
      this.$confirm("是否确认下载报告?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (this.questionTab == '11') {
            return createReport(formData);
          } else {
            return createReportHistory(formData);
          }
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    replaceHtml (str) {
      return str
        .replaceAll("<em>", "")
        .replaceAll("</em>", "")
        .replaceAll("<span>", "")
        .replaceAll("</span>", "")
        .replaceAll("\n", "")
        .replaceAll("    ", "");
    },
    image2Base64 () {
      let that = this;
      let image = new Image();
      image.crossOrigin = "";
      image.src = this.$refs.webPage.src;
      image.onload = function () {
        that.webImage = that.$getBase64Image(image);
      };
    },
    image2Base64Chart4 () {
      let that = this;
      domtoimage.toPng(this.$refs.chart4,
      ).then(function (dataUrl) {
        var img = new Image();
        img.crossOrigin = "Anonymous";
        img.src = dataUrl;
        img.onload = function () {
          that.chart4 = dataUrl;
        };
      });
    },
    image2Base64Chart5 () {
      let that = this;
      domtoimage.toPng(this.$refs.chart5).then(function (dataUrl) {
        var img = new Image();
        img.crossOrigin = "Anonymous";
        img.src = dataUrl;
        img.onload = function () {
          that.chart5 = dataUrl;
        };
      });
    },
    // image2Base64Chart4() {
    //   html2Canvas(this.$refs.chart4,{
    //     backgroundColor:null,
    //     scrollY:100
    //   }).then((canvas)=> {
    //     this.chart4 = canvas.toDataURL("image/jpeg",1); // toDataURL: 图片格式转成 base64
    //   });
    // },
    // image2Base64Chart5() {
    //   html2Canvas(this.$refs.chart5,{
    //     backgroundColor:null,
    //     scrollY:100 }).then((canvas)=> {
    //     this.chart5 = canvas.toDataURL("image/jpeg",1); // toDataURL: 图片格式转成 base64
    //   });
    // },
    transMap (item, tag) {
      if (tag == 1) {
        let arr = [];
        item.map((itemb) => {
          arr.push({ name: itemb.name, value: item.value });
        });
        return JSON.stringify(arr);
      }
      if (tag == 2) {
        let brr = [];
        item.map((itemb) => {
          brr.push({
            name: itemb.siteName,
            value: itemb.taskCount,
            count: itemb.infoCount,
            host: itemb.host,
          });
        });
        return JSON.stringify(brr);
      }
      if (tag == 3) {
        let crr = [];
        item.map((itemb) => {
          crr.push({ name: itemb.name, host: itemb.host });
        });
        return JSON.stringify(crr);
      }
    },
    //   获取报告预览数据
    async getReportData () {
      this.chartLoad = true
      let params = {
        type: this.serarData.type,
        startTime: this.serarData.startTime,
        endTime: this.serarData.endTime,
        checkTaskId: this.serarData.checkTaskId,
        assignIds: this.serarData.assignIds,
        timeRound: this.serarData.timeRound,
        checkTaskTypeId: this.serarData.checkTaskTypeId,
        infoType: this.$route.query.infoType.split(',')

      };
      let res
      if (this.questionTab == '11') {
        res = await getReportDataApi(params);
      } else {
        res = await getReportHistoryDataApi(params)
      }
      if(res.code!=200){
        this.chartLoad = false
        this.$message({ message: res.msg, type: "error" });
        return
      }
      this.AllData = res.data;
      this.edited = res.data.edited
      this.chartLoad = false

      setTimeout(() => {
        this.disDown = false;
        this.image2Base64Chart4();
        this.image2Base64Chart5();
      }, 4000)

      this.endYear = res.data.endYear;
      this.tableData = res.data.questionTable;
      this.tableData.map((item) => {
        if (item.name) {
          item["网站名称"] = item.name;
        }
        if (item.count) {
          item["总计"] = item.count;
        }
      });
      let tableHead = res.data.questionTableX;
      tableHead.push("总计");
      tableHead.unshift("网站名称");
      tableHead.map((item, index) => {
        this.tableConfig.push({ id: index, label: item, prop: item });
      });
      this.keyWrongInfoList = res.data.keyWrongInfoList;

      for (let i = 0; i < res.data.countMap.length; i++) {
        this.countMapKey.push(res.data.countMap[i].name);
      }
      // console.log(' this.tableData', this.tableData);
      // this.tableData = [{'一般编辑类错误': "1","总计":'1','网站名称':'211'}]
      this.initChart();
      this.initChartTwo();
      this.initChartThree();
    },
    getDate () {
      let date = new Date();
      let dayDiff =
        date.getFullYear() +
        "年" +
        (date.getMonth() + 1) +
        "月" +
        date.getDate() +
        "日";
      this.nowData = dayDiff;
    },
    // 获取纵向echart图 图1
    initChart () {
      let chart = this.$echarts.init(this.$refs.linechart);
      let yearList = [];
      let yearCount = [];
      for (let i = 0; i < this.AllData.yearsTaskInfos.length; i++) {
        yearCount.push(this.AllData.yearsTaskInfos[i].address);
        yearList.push(this.AllData.yearsTaskInfos[i].publishTime);
      }
      let option = {
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          type: "category",
          data: yearList,
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: yearCount,
            type: "line",
          },
        ],
      };
      chart.setOption(option);
      var opts = {
        type: "png", // 导出的格式，可选 png, jpeg
        pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
      };
      setTimeout(() => {
        this.chart1 = chart.getDataURL(opts); //拿到base64 地址，就好下载了。
      }, 2000);
    },
    // // 获取横向图表 图2 纵向柱状图
    // initChartTwo() {
    //   let chart = this.$echarts.init(this.$refs.linechartTwo);
    //   let webSiteList = [];
    //   let webSiteWrongList = [];
    //   for (let i = 0; i < this.AllData.siteVerifyList.length; i++) {
    //     webSiteList.push(this.AllData.siteVerifyList[i].siteName);
    //     webSiteWrongList.push(this.AllData.siteVerifyList[i].infoCount);
    //   }
    //   let option = {
    //     backgroundColor: "#FFFFFF",
    //     yAxis: {
    //       type: "category",
    //       data: webSiteList,
    //     },
    //     xAxis: {
    //       type: "value",
    //     },
    //     tooltip: {
    //       trigger: "axis",
    //       axisPointer: {
    //         type: "shadow", // 'shadow' as default; can also be 'line' or 'shadow'
    //       },
    //     },
    //     grid: {
    //       left: "3%",
    //       right: "4%",
    //       bottom: "3%",
    //       containLabel: true,
    //     },
    //     series: [
    //       {
    //         label: {
    //           fontSize: "16px",
    //           show: true,
    //           position: "inside",
    //         },
    //         data: webSiteWrongList,
    //         type: "bar",
    //         name:'数量',
    //       },
    //     ],
    //   };
    //   chart.setOption(option);
    //   var opts = {
    //     type: "png", // 导出的格式，可选 png, jpeg
    //     pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
    //   };
    //   setTimeout(() => {
    //     this.chart2 = chart.getDataURL(opts); //拿到base64 地址，就好下载了。
    //   }, 2000);
    // },
    // 横向图表 图2 柱状图
    initChartTwo () {
      let chart = this.$echarts.init(this.$refs.linechartTwo);
      let webSiteList = this.AllData.chartThree.names;
      let chartThreeWrong = [];
      let allValues = [];
      this.AllData.chartThree.lists.forEach((item) => {
        allValues.push(item);
        chartThreeWrong.push(...item.wrongs);
      });

      let resultListAll = []; // 整体键值对格式
      allValues.map((item) => {
        let resultList = [];
        for (let i = 0; i < item.values.length; i++) {
          let obj = {};
          obj.name = item.wrongs[i];
          obj.value = item.values[i];
          resultList.push(obj);
        }
        resultListAll.push(resultList);
      });
      let chartThreeWrongs = [...new Set(chartThreeWrong)]; // x轴
      let threeValuesAll = []; // 包含所有x轴 键值对
      for (let index = 0; index < resultListAll.length; index++) {
        let threeWrongsParam = [];
        chartThreeWrongs.map((item) => {
          threeWrongsParam.push({ name: item, value: null });
        });
        threeWrongsParam.forEach((item) => {
          // 判断resultListAll里有没有threeWrongsParam对象
          let isInclude = resultListAll[index].some((it) => {
            return it.name == item.name;
          });
          if (isInclude == false) {
            // 没有值为null
            item.value = null;
          } else {
            // 有 将value赋值
            let param = resultListAll[index].filter(
              (itema) => itema.name === item.name
            );
            item.value = param[0].value;
          }
        });
        threeValuesAll.push(threeWrongsParam); // 循环加入到threeValuesAll里
      }
      let series = [];
      let elementArr = []; // 数据
      for (let index = 0; index < chartThreeWrongs.length; index++) {
        const element = [];
        threeValuesAll.map((item) => {
          item.map((itema) => {
            if (itema.name == chartThreeWrongs[index]) {
              // 将相同类型的数据放入数组里
              element.push(itema.value);
            }
          });
        });
        elementArr.push(element); // 生成chartThreeWrongs所有类型的数据
      }
      for (let i = 0; i < chartThreeWrongs.length; i++) {
        series.push({
          name: chartThreeWrongs[i],
          type: "bar",
          stack: "total",
          label: {
            show: true,
          },
          emphasis: {
            focus: "series",
          },
          data: elementArr[i],
        });
      }
      let option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          type: 'scroll',
          width: "80%",
          top: "2%",
          // itemWidth: 10,
          // itemHeight: 10,
        },
        grid: {
          top: "10%",
          left: "20%",
          right: "5%",
          bottom: "10%",
        },
        xAxis: {
          type: "value",
        },
        yAxis: {
          type: "category",
          data: webSiteList.slice(0, 20),
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            formatter: function (value) {
              var texts = value;
              if (texts.length > 15) {
                texts = texts.substr(0, 15) + '...';
              }
              return texts;
            }
          },
        },
        series: series,
      };
      chart.setOption(option);
      var opts = {
        type: "png", // 导出的格式，可选 png, jpeg
        pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
      };
      setTimeout(() => {
        this.chart2 = chart.getDataURL(opts); //拿到base64 地址，就好下载了。
      }, 2000);
    },
    // 审校问题归类 图3柱状图
    initChartThree () {
      let chart = this.$echarts.init(this.$refs.linechartThree);
      let chartFourNames = [];
      let chartFourLists = [];
      this.AllData.countMap.forEach((item) => {
        chartFourNames.push(item.name);
        chartFourLists.push(item.value);
      });
      let option = {
        xAxis: {
          type: "category",
          data: chartFourNames,
          axisLabel: {
            interval: 0, //横轴信息全部显示
            rotate: -20, //倾斜显示
          },
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            data: chartFourLists,
            type: "bar",
            label: {
              normal: {
                show: true,
                position: "top",
                fontSize: 14,
                color: "#333",
              },
            },
          },
        ],
      };
      chart.setOption(option);
      var opts = {
        type: "png", // 导出的格式，可选 png, jpeg
        pixelRatio: 2, // 导出的图片分辨率比例，默认为 1。
      };
      setTimeout(() => {
        this.chart3 = chart.getDataURL(opts); //拿到base64 地址，就好下载了。
      }, 2000);
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  .pro-func {
    margin-left: 2em;
  }

  width: 100%;
  overflow: hidden;
  background: #fff;
  padding: 20px 15%;
  box-sizing: border-box;
  color: #333;

  .sensitive {
    display: inline;
  }

  .homeBox {
    width: 100%;
    overflow: hidden;
    padding: 20px;

    .homeOne {
      width: 100%;
      overflow: hidden;

      img {
        display: inline-block;
        margin-bottom: 200px;
        width: 57.4mm;
        height: 32.8mm;
      }

      .dowReport {
        display: inline-block;
        float: right;
        margin-top: 20px;
        margin-bottom: 200px;
      }
    }

    .homeTwo {
      width: 100%;
      overflow: hidden;
      height: 297mm;

      h2 {
        text-align: center;
        // font-size: 28px;
        font-weight: normal;
        font-size: 45px;
        margin: 0;
        color: #333;
      }
    }

    .homeThree {
      width: 100%;
      overflow: hidden;
      text-align: center;

      p {
        margin-bottom: 10;
        padding: 0;
        text-align: center;
        color: #333;
      }
    }

    .homeFour {
      width: 100%;
      overflow: hidden;
      text-align: center;

      h2 {
        width: 100%;
        overflow: hidden;
        text-align: left;
        font-weight: normal;
        font-size: 26px;
        line-height: 32px;
        margin-top: 60px;
      }

      h3 {
        width: 100%;
        overflow: hidden;
        text-align: left;
        font-weight: normal;
        font-size: 24px;
        line-height: 32px;
      }

      h4 {
        width: 100%;
        overflow: hidden;
        text-align: left;
        font-weight: normal;
        font-size: 20px;
        line-height: 32px;
      }

      .content {
        width: 100%;
        overflow: hidden;
        margin-top: 20px;
        text-indent: 2em;
        line-height: 36px;
        text-align: left;

        span {
          // display: inline-block;
          font-weight: bold;
        }

        em {
          font-style: normal;
          color: #70ae47;
        }
      }

      .imgs {
        margin-top: 20px;
        text-align: center;

        img {
          width: 100%;
          overflow: hidden;
        }

        .echartsbox {
          width: 100%;
          height: 500px;
        }

        .echartsboxmsg {
          width: 100%;
          min-height: 600px;
        }
      }

      .contUl {
        margin: 0;
        padding: 0;
        display: inline;

        li {
          list-style: none;
          text-indent: 0;
          display: inline-block;
          margin-right: 6px;
        }
      }
    }
  }
}
</style>
