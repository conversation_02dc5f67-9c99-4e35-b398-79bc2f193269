<template>
  <div id="app">
    <router-view v-if="isRouterAlive" />
  </div>
</template>

<script>
import { removeToken } from "@/utils/auth";
export default {
  name: "App",
  provide() {
    //父组件中通过provide来提供变量，在子组件中通过inject来注入变量。
    return {
      reload: this.reload,
    };
  },
  data() {
    return {
      isRouterAlive: true, //控制视图是否显示的变量
    };
  },
  created() {
    this.getSysLogo();
  },
  // mounted () {
  //   this.$store.dispatch("getSysLogo").then((res) => {
  //     // let name = res.data.name;
  //     document.title = this.$store.state.user.sysName;
  //   });
  // },
  methods: {
    reload() {
      this.isRouterAlive = false; //先关闭，
      this.$nextTick(function () {
        this.isRouterAlive = true; //再打开
      });
    },
    getSysLogo() {
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      console.log(window.localStorage.getItem('context'), 'app-context');
      var url = document.location.pathname;
      var index = url
        .substr(url.indexOf("/", url.indexOf("/") - 1) + 1)
        .substring(
          0,
          url.substr(url.indexOf("/", url.indexOf("/") - 1) + 1).indexOf("/")
        );
      if (isMobile) {
        console.log('当前在手机端');
        this.$store.dispatch("getSysLogo", index).then((res) => {
          document.title = this.$store.state.user.sysName;
        })
      } else {
         if(url.includes('/singleLogin')){
            // 加缓存context
            window.localStorage.setItem('context', index)
            // 缓存context和地址不一致-退出登录
            if (window.localStorage.getItem('context') != index) {
              this.$store.dispatch("LogOut").then((res) => {
                location.href = `${index}/login`;
              });
            } else {
              this.$store.dispatch("getSysLogo", index).then((res) => {
                document.title = this.$store.state.user.sysName;
              });
            }
         } else if (url.includes('/textFrame')) { 
            window.localStorage.setItem('context', index)
         }
         else {
            // 缓存context和地址不一致-退出登录
            if (window.localStorage.getItem('context') != index) {
              this.$store.dispatch("LogOut").then((res) => {
                location.href = `${index}/login`;
              });
            } else {
              this.$store.dispatch("getSysLogo", index).then((res) => {
                document.title = this.$store.state.user.sysName;
              });
            }
            // 加缓存context
            window.localStorage.setItem('context', index)
         }
      }
      // let params = {
      //   context: index,
      // };
    },
  },
};
</script>
