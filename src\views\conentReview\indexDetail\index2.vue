<template>
  <div class="home">
    <div class="filterBox">
      <div class="title">数据检测</div>
      <!-- <div class="checkTab">
        <el-radio-group v-model="tabPosition" @change="changeType">
          <el-radio-button label="1">文本</el-radio-button>
          <el-radio-button label="3">音频</el-radio-button>
          <el-radio-button label="4">图片</el-radio-button>
          <el-radio-button label="5">视频</el-radio-button>
        </el-radio-group>
      </div> -->
      <el-form v-model="queryParams" :inline="true">
        <template v-if="tabPosition == '1'">
          <el-form-item prop="wrongWord" label="问题词语：">
            <el-input v-model.trim="queryParams.wrongWord" placeholder="请输入问题词语" size="small" clearable></el-input>
          </el-form-item>
          <el-form-item prop="adviceWord" label="建议词语：">
            <el-input v-model.trim="queryParams.adviceWord" placeholder="请输入建议词语" size="small" clearable></el-input>
          </el-form-item>
        </template>
        <template v-if="tabPosition == '4'||tabPosition == '5'">
          <el-form-item prop="suggestion" label="结果状态：">
            <el-select v-model="queryParams.suggestion" placeholder="请选择结果状态" style="width: 100%" size="small" multiple clearable>
              <el-option label="嫌疑" :value="1"></el-option>
              <el-option label="不通过" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="original" label="原文链接：">
            <el-input v-model.trim="queryParams.original" placeholder="请输入原文链接" size="small" clearable></el-input>
          </el-form-item>
        </template>
        <template v-if="tabPosition == '3'">
          <el-form-item prop="wrongWord" label="问题词语：">
            <el-input v-model.trim="queryParams.wrongWord" placeholder="请输入问题词语" size="small" clearable></el-input>
          </el-form-item>
          <el-form-item prop="adviceWord" label="建议词语：">
            <el-input v-model.trim="queryParams.adviceWord" placeholder="请输入建议词语" size="small" clearable></el-input>
          </el-form-item>
        </template>
        <!-- <template v-if="tabPosition == '5'">
          <el-form-item prop="original" label="原文链接：">
            <el-input v-model="queryParams.original" placeholder="请输入原文链接" size="small" clearable></el-input>
          </el-form-item>
        </template> -->


        <el-form-item>
          <el-button size="mini" type="primary" @click="searchFilter" icon="el-icon-search">搜索</el-button>
          <el-button size="mini" type @click="cancelsearchFilter" icon="el-icon-refresh">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-download" size="mini" :loading="exportLoading"
            @click="picListExport">导出</el-button>
        </el-col>
      </el-row>
      <el-table :data="liData" @selection-change="handleSelectionChange" v-loading="tableLoading">
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column type="index" label="序号" align="center" :index="getIndex" width="80"></el-table-column>
        <el-table-column label="信源名称" prop="name" align="center" show-overflow-tooltip></el-table-column>
        <template v-if="tabPosition === '1'">
          <el-table-column key="1-1" label="文章标题" prop="title" align="center"></el-table-column>
          <el-table-column key="1-2" label="问题词语" prop="wrongWord" align="center"></el-table-column>
          <el-table-column key="1-3" label="问题分类" prop="wordGroupDetail" align="center"></el-table-column>
          <el-table-column key="1-4" label="建议词语" prop="adviceWord" align="center"></el-table-column>
        </template>

        <template v-else-if="tabPosition === '4'">
          <el-table-column key="2-1" label="原文链接" prop="original" align="center">
            <template slot-scope="scope">
              <el-tooltip effect="dark" :content="scope.row.original" placement="top-start" :open-delay='500'>
                <a class="ellipsisA" :href="scope.row.original" target="_blank">{{ scope.row.original }}</a>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column key="2-2" label="图片" prop="picUrl" align="center">
            <template slot-scope="scope">
              <img style="max-height: 10vh;cursor: pointer;" :src="scope.row.picUrl" @click="jumpURL(scope.row.picUrl)" alt="无图片">
            </template>
          </el-table-column>
          <!-- <el-table-column key="2-2" label="错误词" prop="wrongWord" align="center"></el-table-column> -->
          <!-- <el-table-column key="2-3" label="错误类型" prop="wordGroup" align="center"></el-table-column> -->
          <!-- <el-table-column key="2-4" label="命中级别" prop="level" align="center"></el-table-column> -->
          <el-table-column key="2-3" label="结果状态" prop="suggestion" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
             {{ scope.row.suggestion=='1'?'嫌疑':'不通过' }}
            </template>
          </el-table-column>
          <el-table-column key="2-4" label="发布时间" prop="publishTime" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
             {{ scope.row.publishTime ? moment(Number(scope.row.publishTime)).format('YYYY-MM-DD HH:mm:ss') : '' }}
            </template>
          </el-table-column>
        </template>

        <template v-if="tabPosition === '3'">
          <el-table-column key="3-1" label="音频链接" prop="url" align="center">
            <template slot-scope="scope">
              <a :href="scope.row.url" target="_blank">{{ scope.row.url }}</a>
            </template>
          </el-table-column>
          <el-table-column key="3-2" label="问题词语" prop="wrongWord" align="center"></el-table-column>
          <el-table-column key="3-3" label="问题分类" prop="wordGroupDetail" align="center"></el-table-column>
          <el-table-column key="3-4" label="建议词语" prop="adviceWord" align="center"></el-table-column>
        </template>
        <template v-else-if="tabPosition === '5'">
          <el-table-column key="4-1" label="原文链接" prop="original" align="center">
            <template slot-scope="scope">
              <el-tooltip effect="dark" :content="scope.row.original" placement="top-start" :open-delay='500'>
                <a class="ellipsisA" :href="scope.row.original" target="_blank">{{ scope.row.original }}</a>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column key="4-2" label="视频链接" prop="videoUrl" align="center">
            <template slot-scope="scope">
              <el-tooltip effect="dark" :content="scope.row.videoUrl" placement="top-start" :open-delay='500'>
                <a class="ellipsisA" :href="scope.row.videoUrl" target="_blank">{{ scope.row.videoUrl }}</a>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column key="4-3" label="结果状态" prop="suggestion" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
             {{ scope.row.suggestion=='1'?'嫌疑':'不通过' }}
            </template>
          </el-table-column>
          <el-table-column key="4-4" label="发布时间" prop="publishTime" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
             {{ scope.row.publishTime ? moment(Number(scope.row.publishTime)).format('YYYY-MM-DD HH:mm:ss') : '' }}
            </template>
          </el-table-column>
        </template>
        <!-- <el-table-column label="发布时间" prop="postTime" align="center"></el-table-column> -->
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="goDetail(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination style="margin: 20px 0px; float: right" @size-change="handleSizeChange"
        @current-change="handleCurrentChange" :current-page="queryParams.pageIndex" :page-sizes="[10, 15, 20]"
        :page-size="queryParams.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" />
    </div>
    <el-dialog title="详情" :width="tabPosition === '4' ? '50%' : '80%'" :visible.sync="dialogVisible" close-on-click-modal>

      <TextResult ref="TextResultRef" v-if="tabPosition === '1' || tabPosition === '3'" :wrongData="wrongData" />
      <PicResult ref="PicResultRef" v-if="tabPosition === '4'" :wrongData="wrongData" />
      <VideoResult ref="VideoResultRef" v-if="tabPosition === '5'" :wrongData="wrongData" />
    </el-dialog>
  </div>
</template>

<script>
import { getError } from "@/api/checkData";
import { exportCheckDataApi } from "@/api/index";
const componentRefs = {
  '1': 'TextResultRef',
  '4': 'PicResultRef',
  '3': 'TextResultRef',
  '5': 'VideoResultRef'
}
export default {
  data() {
    return {
      liData: [],
      total: 0,
      queryParams: {
        pageIndex: 1,
        pageSize: 10,
        adviceWord : '',
        original : '',
      },
      // typeOptions: [],
      ids: [],
      // 非多个禁用
      multiple: true,
      exportLoading: false, //导出按钮loading



      host: '',//host
      tabPosition: '1',//tab页
      dialogVisible: false, //弹窗开关
      wrongData: '',
      tableLoading: false,
      serarData:{},//首页传递的搜索参数
    };
  },
  components: {
    TextResult: (resolve) => require(['./infoDetail/textResult'], resolve),
    PicResult: (resolve) => require(['./infoDetail/picResult'], resolve),
    VideoResult: (resolve) => require(['./infoDetail/videoResult'], resolve)
  },
  created() {
    this.serarData = JSON.parse(sessionStorage.getItem("queryForm"));
    if(this.$route.query.assignIds){
      this.serarData.assignIds=this.$route.query.assignIds;
    }
    if(this.$route.query.suggestion){
      this.queryParams.suggestion=JSON.parse(this.$route.query.suggestion);
      this.queryParams = {...this.queryParams}
    }


    // this.queryParams.startTime = this.$route.query.startTime
    // this.queryParams.endTime = this.$route.query.endTime
    // this.host = this.$route.query.host

    this.tabPosition = this.$route.query.dataType

    this.getInfoData();
    // 获取类型
    // this.getDicts("by_source_type").then((response) => {
    //   this.typeOptions = response.data;
    // });
  },
  watch: {
  },
  methods: {
    // 切换词类型
    changeType(val) {
      this.tabPosition == val;
      this.queryParams.wrongWord = ''
      this.queryParams.adviceWord = ''
      this.queryParams.original = ''
      this.queryParams.suggestion = []
      this.serarData.infoType=[val]
      this.searchFilter();
    },
    jumpURL(url) {
      window.open(url, '_blank')
    },
    // 搜索
    searchFilter() {
      // if (
      //   this.queryParams.startTime &&
      //   this.queryParams.endTime &&
      //   this.queryParams.startTime < this.queryParams.endTime
      // ) {
      //   this.getInfoData();
      // } else if (!this.queryParams.startTime && !this.queryParams.endTime) {
        this.getInfoData();
      // } else {
      //   this.msgError("请输入正确时间范围");
      // }
    },
    cancelsearchFilter() {
      for (let key in this.queryParams) {
        this.$set(this.queryParams, key, "");
      }
      // this.queryParams.startTime = this.$route.query.startTime
      // this.queryParams.endTime = this.$route.query.endTime
      this.queryParams.pageIndex = 1;
      this.queryParams.pageSize = 10;
      this.getInfoData();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.multiple = !selection.length;
    },
    // 导出
    exportData() {
      this.exportLoading = true;
      let params = {
        host: this.host,
        dataType: this.transDataType(this.tabPosition),
        postTimeS: this.queryParams.startTime,
        postTimeE: this.queryParams.endTime,
        wrongWord: this.queryParams.wrongWord,
        adviceWord: this.queryParams.adviceWord,
        original: this.queryParams.original,
        suggestion: this.queryParams.suggestion,
      };
      this.$confirm("是否确认导出数据项?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.exportLoading = true
        let res = await exportCheckDataApi(params);
        this.exportLoading = false
        const blob = res;
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = (e) => {
          const a = document.createElement('a');
          a.href = e.target.result;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        }
      });



      
    },
//图片视频的导出
picListExport() {

  let params = {
        ...this.serarData,
        wrongWord: this.queryParams.wrongWord,
        adviceWord: this.queryParams.adviceWord,
        original: this.queryParams.original,
        suggestion: this.queryParams.suggestion,
      };

      this.$confirm("您确定要导出数据项吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(async () => {
        this.exportLoading = true;
        let res = await exportCheckDataApi(params);
        this.exportLoading = false;
        const blob = res;
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = (e) => {
          const dataUrl = e.target.result;
          const head = dataUrl.split(",")[0];

          if (head == "data:application/json;base64") {
            const base64Data = dataUrl.split(",")[1]; // 提取Base64编码数据
            const decodedData = window.atob(base64Data); // 解码Base64数据
            const jsonStr = decodeURIComponent(escape(decodedData)); // 使用escape编码二进制字符串，然后用decodeURIComponent进行解码
            const jsonObject = JSON.parse(jsonStr); // 解析JSON字符串

            this.$message({
              type: "error",
              message: jsonObject.msg,
            });
          } else {
            const a = document.createElement("a");
            a.href = e.target.result;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
          }
        };
      });
    },
    getIndex(index) {
      return (
        (this.queryParams.pageIndex - 1) * this.queryParams.pageSize + index + 1
      );
    },
    //   分页
    handleSizeChange(val) {
      this.queryParams.pageSize = val;
      this.getInfoData();
    },
    handleCurrentChange(val) {
      this.queryParams.pageIndex = val;
      this.getInfoData();
    },

    // 进入当前问题详情页
    goDetail(row) {
      this.dialogVisible = true;
      this.wrongData = row;
      this.$nextTick(function () {
        this.$refs[componentRefs[this.tabPosition]].getListData()
      });
    },

    transDataType(tabPosition) {
      switch (tabPosition) {
        case '1':
          return 7
        case '2':
          return 10
        case '3':
          return 8
        case '4':
          return 9
        default:
          return 7
      }
    },

    //   获取数据
    async getInfoData() {
      let params = {
        ...this.serarData,

        // host: this.host,
        // dataType: this.transDataType(this.tabPosition),
        wrongWord: this.queryParams.wrongWord,
        adviceWord: this.queryParams.adviceWord,
        original: this.queryParams.original,
        suggestion: this.queryParams.suggestion,
        pageIndex: this.queryParams.pageIndex,
        pageSize: this.queryParams.pageSize,
        // postTimeS: this.queryParams.startTime,
        // postTimeE: this.queryParams.endTime,
      };
      this.tableLoading = true
      let res = await getError(params);
      this.tableLoading = false
      this.liData = res.data.list;
      this.total = res.data.total;
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  width: 100%;
  overflow: hidden;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
  min-height: 889px;

  .filterBox {
    .title {
      font-size: large;
      font-weight: bold;
      margin-bottom: 20px;
    }

    .checkTab {
      margin-bottom: 20px;
    }

    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 0 20px 20px 20px;
    box-sizing: border-box;

    h2.filH2 {
      width: 100%;
      overflow: hidden;
      background: #f5f5f5;
      margin: 0;
      padding: 0;

      span {
        display: inline-block;
        color: #fff;
        background: #ed9e2f;
        height: 38px;
        line-height: 38px;
        padding: 0 10px;
        font-size: 14px;
      }
    }

    ul {
      margin: 0;
      padding: 0;
      width: 100%;
      overflow: hidden;

      li {
        list-style: none;
        height: 36px;
        line-height: 36px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-bottom: dotted 1px #ccc;
        font-size: 14px;
        cursor: pointer;
        padding: 0 10px;
        box-sizing: border-box;

        p {
          margin: 0;
          padding: 0;
          display: inline-block;
          width: 60%;
          text-indent: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      li:hover {
        background-color: #e9f7ff;
        border-left: 2px solid #2899dd;

        p {
          color: #1870c2;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

a {
  color: #2899dd;
}
.ellipsisA {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
