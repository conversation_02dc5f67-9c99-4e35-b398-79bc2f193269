<template>
  <div class="login" ref="UI" :style="styleObj">
    <img class="logTitle" :src="$store.state.user.logoImg == '' ? logoImg : $store.state.user.logoImg
    " />
    <div class="login-form">
      <div v-show="showAccountLogo">
        <div class="loginLogo" v-show="wechatSure" @click="changeLoginType">
          <img class="loginPop" src="../assets/login/codeLoginPop.png" />
          <img class="loginWay" src="../assets/login/codeLogin.svg" />
        </div>
        <!-- <h3 class="wechatH3" @click="backAccountEvent" v-show="showAccountLogin">
          <i class="el-icon-arrow-left"></i>
          返回账号登录
        </h3> -->
        <div class="loginLogo" @click="backAccountEvent" v-show="showAccountLogin">
          <img class="loginPop" src="../assets/login/accountLoginPop.png" />
          <img class="loginWay" src="../assets/login/accountLogin.svg" />
        </div>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
          <h3 class="title" v-show="showLoginTitle">用户登录</h3>
          <h3 class="title" v-show="!showLoginTitle">微信用户登录</h3>
          <div v-show="codeCurSta" class="showWechat">
            <img src="../assets/login/wechatIcon.png" alt />
            微信扫码成功，请绑定账号
          </div>
          <el-form-item prop="username">
            <el-input v-model.trim="loginForm.username" type="text" auto-complete="off" placeholder="请输入账户名称">
              <!-- prefix-icon="el-icon-user-solid" -->
              <!-- <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" /> -->
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model.trim="loginForm.password" type="password" show-password auto-complete="off"
              placeholder="请输入登录密码" @keyup.enter.native="handleLogin">
              <!-- prefix-icon="el-icon-lock" -->
              <!-- <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" /> -->
            </el-input>
          </el-form-item>
          <Verify @success="capctchaCheckSuccess" :mode="'pop'" :captchaType="'blockPuzzle'"
            :imgSize="{ width: '330px', height: '155px' }" ref="verify"></Verify>
          <!-- <el-form-item prop="code">
            <el-input v-model.trim="loginForm.code" auto-complete="off" placeholder="验证码" style="width: 63%"
              @input="forceUpdate" @keyup.enter.native="handleLogin">
              <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div>
          </el-form-item> -->
          <el-checkbox v-model="loginForm.rememberMe" style="margin: 0px 0px 34px 0px">记住密码</el-checkbox>
          <el-form-item style="width: 100%; text-align: center">
            <el-button :loading="loading" size="medium" type="primary" style="width: 100%;background: #1366FF;"
              @click.native.prevent="handleLogin">
              <span v-if="!loading">{{ loginSure }}</span>
              <span v-else>登 录 中...</span>
            </el-button>
          </el-form-item>
        </el-form>
        <!-- <div class="loginWechatDiv" v-show="wechatSure">
          <img src="../assets/login/loginWechat.png" alt @click="changeLoginType" />
        </div> -->
      </div>



      <div v-show="showWechatLogo" class="weChatBox">

        <div class="loginLogo" @click="backAccountLogo">
          <img class="loginPop" src="../assets/login/accountLoginPop.png" />
          <img class="loginWay" src="../assets/login/accountLogin.svg" />
        </div>
        <!-- <h3 class="wechatH3" @click="backAccountLogo">
          <i class="el-icon-arrow-left"></i>
          返回账号登录
        </h3> -->
        <div class="wechatDiv" v-show="showCodeImg">
          <h3 class="title">微信扫码登录</h3>
          <img v-show="hasCodeImg" :src="wechatImg" alt />
          <div v-show="!hasCodeImg"
            style="width: 200px;height: 200px;margin: 0 auto;line-height: 200px;background: rgba(0, 0, 0, 0.5);">
            二维码异常</div>
          <span @click="refreshWechat" style="color: #0E7EFF;font-size: 14px;">
            <i class="el-icon-refresh-right" style="font-size: 24px;vertical-align: middle;"></i>刷新二维码
          </span>
        </div>
        <div v-show="showCodeRejetc" class="rejectShow">
          <img src="../assets/login/tan.png" alt="" />
          <p>您已取消此次登录</p>
          <el-button type="primary" @click="tryAgain">重试</el-button>
        </div>
      </div>
    </div>
    <!--  底部  -->
    <!-- <div class="el-login-footer">
      <span>Copyright © 2010-2021 boryou.com All Rights Reserved.</span>
    </div>-->
  </div>
</template>

<script>
import {
  getCodeImg,
  getWechatlogoApi,
  getCodeStatusApi,
  checkLoginApi,
} from "@/api/login";
import Verify from "@/components/Verifition/Verify";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { getSystemLogoApi } from "@/api/login"
export default {
  name: "Login",
  components: { Verify },
  inject: ["reload"],
  data() {
    return {
      styleObj: {},
      wechatSure: true,
      wechatImg: "",
      showAccountLogo: true,
      showWechatLogo: false,
      codeUrl: "",
      cookiePassword: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
        rejectNum: 0,
        wechatQRCodeId: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "账户名称不能为空" },
        ],
        password: [
          { required: true, trigger: "blur", message: "登录密码不能为空" },
        ],
        code: [
          { required: true, trigger: "change", message: "验证码不能为空" },
        ],
      },
      loading: false,
      redirect: undefined,
      bgImg: require("@/assets/login/loginBg.png"),
      logoImg: require("@/assets/login/loginTitle.png"),
      timer: "",
      codeStatus: "",
      showCodeImg: true,
      hasCodeImg: true,
      showCodeRejetc: false,
      showAccountLogin: false,
      showLoginTitle: true,
      codeCurSta: false,
      loginSure: "登录",
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
    codeStatus: {
      handler(val) {
        if (val == "SIGNED_IN") {
          clearInterval(this.timer);
        }
      },
      immediate: true,
    },
  },
  created() {
    // this.getCode();
    this.getCookie();
    this.getBgImg()
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate();
    },
    // 获取背景图片
    async getBgImg() {
      var url = document.location.pathname;
      var index = url.substr(url.indexOf('/', url.indexOf('/') - 1) + 1).substring(0, url.substr(url.indexOf('/', url.indexOf('/') - 1) + 1).indexOf('/'));
      let res = await getSystemLogoApi(index)
      if (res.data.backImage) {
        let imgLink = process.env.VUE_APP_BASE_API + res.data.backImage
        this.styleObj = {
          background: 'url(' + imgLink + ') no-repeat',
        }
      } else {
      this.styleObj = {
        background: 'url(' + require("@/assets/login/loginBg.png") + ') no-repeat',
      }
      }
    },
    start() {
      // 获取二维码状态
      this.timer = setInterval(() => {
        let params = {
          wechatQRCodeId: this.loginForm.wechatQRCodeId,
        };
        getCodeStatusApi(params).then((res) => {
          this.codeStatus = res.data;
          if (this.codeStatus == "SIGNED_IN") {
            this.$store.dispatch("weChatLogin", params).then(() => {
              this.$router.push({ path: "/index" }).catch(() => { });
            });
            clearInterval(this.timer);
          } else if (this.codeStatus == "NO_USER_SCANNED") {
            // 没有可登录账户,微信第一次扫码
            this.showWechatLogo = false;
            this.showAccountLogo = true;
            this.wechatSure = false;
            this.showLoginTitle = false;
            this.showAccountLogin = true;
            this.codeCurSta = true;
            this.loginForm.rejectNum = 1;
            this.loginSure = "确定绑定并登录";
            clearInterval(this.timer);
            // this.getCode();
          } else if (this.codeStatus == "REJECT_LOGIN") {
            // 拒绝登录
            this.showCodeRejetc = true;
            this.showCodeImg = false;
            this.wechatSure = false;
            this.showLoginTitle = true;
            this.wechatSure = true;
          }
        });
      }, 2000);
    },
    getCode() {
      getCodeImg().then((res) => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.loginForm.uuid = res.uuid;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get("rememberMe");
      const rejectNum = Cookies.get("rejectNum");
      const wechatQRCodeId = Cookies.get("wechatQRCodeId");
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password:
          password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
        rejectNum:
          rejectNum === undefined ? this.loginForm.rejectNum : rejectNum,
        wechatQRCodeId:
          wechatQRCodeId === undefined
            ? this.loginForm.wechatQRCodeId
            : wechatQRCodeId,
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.$refs.verify.show();
        }
      });
    },
    //滑块回调函数
    capctchaCheckSuccess(param) {
      this.loginForm.code = param.captchaVerification;
      this.loading = true;
      if (this.loginForm.rememberMe) {
        Cookies.set("username", this.loginForm.username, { expires: 30 });
        Cookies.set("password", encrypt(this.loginForm.password), {
          expires: 30,
        });
        Cookies.set("rememberMe", this.loginForm.rememberMe, {
          expires: 30,
        });
      } else {
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      if (this.showLoginTitle == true) {
        // 用户登录
        this.loginForm.rejectNum = "0";
      } else {
        this.loginForm.rejectNum = "1";
        this.loginForm.wechatQRCodeId = this.loginForm.wechatQRCodeId;
      }
      let params = {
        username: this.loginForm.username,
        // password: this.$md5(this.loginForm.password),
        password: this.loginForm.password,
        rememberMe: this.loginForm.rememberMe,
        // uuid: this.loginForm.uuid,
        code: this.loginForm.code,
        rejectNum: this.loginForm.rejectNum,
        wechatQRCodeId: this.loginForm.wechatQRCodeId,
        context: window.localStorage.getItem('context')
      };
      this.$store
        .dispatch("Login", params)
        .then((res) => {
          // // this.$router.push({ path: this.redirect || "/" }).catch(() => {});
          // this.$router.push({ path: "/index" });
          if (res.code == 200) {
            if (res.loginWay == "normalLogin") {
              this.$router.push({
                path: "/",
                query: { name: "adminuser" },
              });
            } else {
              this.$router.push({
                path: "/",
                query: { name: "normaluser" },
              });
            }
          } else {
            this.$message.error(res.msg);
            this.loading = false;
            // this.$forceUpdate();
            // this.getCode();
            // this.reload();
          }
        })
        .catch((err) => {
          this.loading = false;
          // this.getCode();
        });
    },

    async changeLoginType() {
      this.showWechatLogo = true;
      this.showAccountLogo = false;
      this.showCodeImg = true;
      this.showCodeRejetc = false;
      // this.loginForm.code = "";
      this.forceUpdate();
      this.getWechatLogo();
    },
    // 获取微信二维码
    getWechatLogo() {
      // let res = await getWechatlogoApi();
      // this.wechatImg = res.data.qRCode;
      // this.loginForm.wechatQRCodeId = res.data.wechatQRCodeId;
      // this.start();

      getWechatlogoApi().then(res => {
        this.hasCodeImg = true;
        this.wechatImg = res.data.qRCode;
        this.loginForm.wechatQRCodeId = res.data.wechatQRCodeId;
        this.start();
      }).catch(err=>{
        this.hasCodeImg = false;
        console.log(err)
      });

    },
    // 返回账号登录
    backAccountLogo() {
      this.showWechatLogo = false;
      this.showAccountLogo = true;
      this.wechatSure = true;
      this.loginSure = "登录";
      // this.getCode();
      clearInterval(this.timer);
      // this.loginForm.code = "";
      this.forceUpdate();
    },
    refreshWechat() {
      this.getWechatLogo();
    },
    // 重试
    tryAgain() {
      this.showCodeImg = true;
      this.showCodeRejetc = false;
      this.getWechatLogo();
    },
    // 返回账号登录
    backAccountEvent() {
      this.wechatSure = true;
      this.codeCurSta = false;
      this.showAccountLogin = false;
      this.showLoginTitle = true;
      // this.loginForm.code = "";
      this.forceUpdate();
      this.loginSure = "登录";
    },
  },
  destroyed() {
    clearInterval(this.timer);
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.rejectShow {
  width: 100%;
  overflow: hidden;
  text-align: center;
  padding: 40px 0px 20px;
  box-sizing: border-box;

  img {
    display: inline-block;
    width: 40px;
    height: 40px;
  }

  p {
    margin: 20px 0px 30px;
    padding: 0;
    text-align: center;
  }
}

.showWechat {
  width: 100%;
  text-align: center;
  height: 30px;
  line-height: 30px;
  margin-bottom: 10px;

  img {
    display: inline-block;
    height: 24px;
    width: 24px;
    margin-right: 10px;
    vertical-align: middle;
  }
}

.loginLogo {
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;

  .loginPop {
    height: 28px;
    display: inline-block;
    vertical-align: middle;
  }

  .loginWay {
    height: 70px;
    display: inline-block;
    vertical-align: middle;
  }
}


.wechatH3 {
  margin: 0;
  padding: 0;
  font-weight: normal;
  text-align: left;
  font-size: 16px;
  cursor: pointer;
  margin-bottom: 20px;
}

.weChatBox {
  width: 100%;
  overflow: hidden;

  .wechatDiv {
    width: 100%;
    overflow: hidden;
    text-align: center;
    // margin: 0px 0 68px;

    p {
      margin: 0;
      padding: 0;
      height: 30px;
      line-height: 30px;
      font-size: 20px;
    }

    span {
      display: block;
      text-align: center;
      font-size: 20px;
      cursor: pointer;
      margin-top: 10px;
    }

    img {
      width: 277px;
      margin: -35px;
    }
  }
}

.loginWechatDiv {
  width: 100%;
  overflow: hidden;
  text-align: center;

  img {
    display: inline-block;
    cursor: pointer;
    width: 30px;
    height: 30px;
  }
}

.login {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-size: cover !important;
  background-position: center !important;
  // background-image: url("../assets/login/loginBg.png") ;
  // background-size: 100% 100%;
  position: relative;

  .logTitle {
    position: absolute;
    left: 2.5vw;
    top: 4.1vh;
    display: inline-block;
    max-width: 27vw;
    max-height: 5vh;
  }
}

.title {
  margin: 20px auto 32px auto;
  text-align: center;
  font-size: 27px;
  // font-weight: normal;


  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  color: #333333;
}

.login-form {
  // border-radius: 10px;
  background: #ffffff;
  width: 440px;
  height: 480px;
  padding: 70px 30px 30px;
  position: absolute;
  right: 15%;
  // top: 30%;
  box-shadow: 2px 2px 15px 0px #ced2d5;

  .el-form-item{
    margin-bottom: 26px;
  }

  .el-input {
    height: 52px;

    input {
      height: 52px;
      border: 1px solid #EEEEEE;
      background-color: #F8F8F8;
    }
    
  }
  .el-form-item__content{
      line-height:52px;
      .el-button--medium{
        padding: 15px 20px;
        font-size: 16px;
      }
    }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}
</style>
