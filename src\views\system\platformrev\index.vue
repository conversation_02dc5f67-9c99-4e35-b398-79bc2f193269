
<template>
  <div class="app-container">
    <div class="home">
      <el-radio-group v-model="tabPosition" style="margin-bottom: 15px" @change="changeTab">
        <el-radio-button label="1">备案网站</el-radio-button>
        <el-radio-button label="2">媒体账号</el-radio-button>
      </el-radio-group>
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="120px">
        <el-form-item label="信源名称：" prop="name" v-show="tabPosition == 1">
          <el-input v-model.trim="queryParams.name" placeholder="请输入信源名称" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="备案域名：" prop="host" v-show="tabPosition == 1">
          <el-input v-model.trim="queryParams.host" placeholder="请输入备案域名" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="备案许可证号：" prop="recordLicenseNumber" v-if="tabPosition == 1">
          <el-input v-model.trim="queryParams.recordLicenseNumber" placeholder="请输入备案许可证号" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="信源名称：" prop="accountname" v-show="tabPosition == 2">
          <el-input v-model.trim="queryParams.accountname" placeholder="请输入信源名称" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="信源id：" prop="accountId" v-show="tabPosition == 2">
          <el-input v-model.trim="queryParams.accountId" placeholder="请输入信源id" clearable size="small"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="信源类型：" prop="mediaType" v-show="tabPosition == 2">
          <el-select v-model="queryParams.mediaType" placeholder="请选择信源类型" clearable size="small">
            <el-option v-for="dict in mediaTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属用户：" prop="customer">
          <el-select v-model="queryParams.customer" placeholder="请选择所属用户" clearable size="small" filterable
            @change="getgroupOption">
            <el-option v-for="(dict, index) in userList" :key="index" :label="`${dict.userName}(${dict.nickName})`"
              :value="parseInt(dict.userId)">{{ dict.userName }} ({{ dict.nickName }})</el-option>
            <!-- :value="[dict.userId, dict.customer]" -->
          </el-select>
        </el-form-item>
        <el-form-item label="所属分组：" prop="siteTypeId" v-show="showGroup">
          <el-select v-model="queryParams.siteTypeId" placeholder="请选择所属分组" clearable size="small">
            <el-option v-for="dict in groupOption" :key="dict.id" :label="dict.typeName" :value="dict.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['system:platformrev:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
            v-hasPermi="['system:platformrev:export']">批量导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="el-icon-folder" size="mini" plain :disabled="multiple"
            @click="moreGroup">批量分组</el-button>
        </el-col>
        <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExports"
          :loading="exportLoading"
          v-hasPermi="['system:platformrev:export']"
          >导出</el-button
        >
      </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="websiteList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="序号" :index="getIndex" key="1" width="80"></el-table-column>
        <el-table-column label="信源名称" key="2" align="center" prop="name" v-if="tabPosition == 1" />
        <el-table-column label="备案域名" key="3" align="center" prop="host" v-if="tabPosition == 1" />
        <el-table-column label="备案许可证号" align="center" key="4" prop="recordLicenseNumber" v-if="tabPosition == 1" />
        <el-table-column key="5" label="信源名称" align="center" prop="name" v-if="tabPosition == 2" />
        <el-table-column key="6" label="信源id" align="center" prop="accountId" v-if="tabPosition == 2" />
        <!-- <el-table-column key="7" label="账号昵称" align="center" prop="nickName" v-if="tabPosition == 2" /> -->
        <el-table-column key="8" label="所属分组" align="center" prop="siteTypeName" />
        <el-table-column key="9" label="信源类型" align="center" prop="mediaType" :formatter="mediaTypeFormat"
          v-if="tabPosition == 2" />
        <el-table-column key="10" label="最新采集时间" align="center" prop="newestTime" />
        <el-table-column key="11" label="是否持续采集" align="center" prop="isCollect" :formatter="isCollectFormat" />
        <el-table-column key="12" label="所属用户" align="center" prop="userName">
          <template slot-scope="scope">
            {{ scope.row.userName }}({{ scope.row.nickName }})
          </template>
        </el-table-column>

        <el-table-column key="13" label="操作" align="center" class-name="small-padding fixed-width"
          v-if="checkPermi(['system:platformrev:operate'])">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
            <el-button size="mini" type="text" icon="el-icon-folder" @click="editMoreGroup(scope.row)">修改分组</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />

      <!-- 添加或修改备案网站对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body :before-close="cancel">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="信源名称" prop="name" v-show="tabPosition == 1">
            <el-input v-model.trim="form.name" placeholder="请输入信源名称" />
          </el-form-item>
          <el-form-item label="首页链接" prop="homeUrl" v-show="tabPosition == 1">
            <el-input v-model.trim="form.homeUrl" placeholder="请输入首页链接" />
          </el-form-item>
          <el-form-item label="域名" prop="host" v-show="tabPosition == 1">
            <el-input v-model.trim="form.host" placeholder="请输入域名" />
          </el-form-item>
          <el-form-item label="备案许可号" prop="recordLicenseNumber" v-show="tabPosition == 1">
            <el-input v-model.trim="form.recordLicenseNumber" placeholder="请输入备案许可号" />
          </el-form-item>
          <el-form-item label="信源名称：" prop="accountname" v-show="tabPosition == 2">
            <el-input v-model.trim="form.accountname" placeholder="请输入信源名称" />
          </el-form-item>
          <el-form-item label="信源id：" prop="accountId" v-show="tabPosition == 2">
            <el-input v-model.trim="form.accountId" placeholder="请输入信源id" />
          </el-form-item>
          <!-- <el-form-item label="账号昵称：" prop="nickName" v-show="tabPosition == 2">
            <el-input v-model.trim="form.nickName" placeholder="请输入账号昵称" />
          </el-form-item> -->
          <el-form-item label="信源类型：" prop="mediaType" v-show="tabPosition == 2">
            <el-select v-model="form.mediaType" placeholder="请选择信源类型">
              <el-option v-for="dict in mediaTypeOptions" :key="dict.dictValue" :label="dict.dictLabel"
                :value="parseInt(dict.dictValue)"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否持续采集" prop="isCollect">
            <el-radio-group v-model="form.isCollect">
              <el-radio v-for="(item, index) in isCollectList" :key="index" :value="item.value" :label="item.value">{{
                item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="所属用户：" prop="customer" v-show="!form.id">
            <el-select v-model="form.customer" placeholder="请选择所属用户" filterable @change="getgroupOptionTwo">
              <el-option v-for="(dict, index) in userList" :key="index" :label="`${dict.userName}(${dict.nickName})`"
                :value="parseInt(dict.customer)">{{ dict.userName }} ({{ dict.nickName }})</el-option>
            </el-select>
          </el-form-item>
        <!-- <el-form-item
            label="所属分组："
            prop="siteTypeId"
            v-show="showGroupForm"
          >
            <el-select
              v-model="form.siteTypeId"
              placeholder="请选择所属分组"
              clearable
              size="small"
              @change="$forceUpdate()"
            >
              <el-option
                v-for="dict in groupOptionTwo"
                :key="dict.id"
                :label="dict.typeName"
                :value="dict.id"
              />
            </el-select>
                                                                                                                              </el-form-item> -->
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
      <el-dialog title="批量分组" :visible.sync="moreGroupOpen" width="600px" append-to-body :before-close="cancelGroup">
        <el-form :model="groupForm" :rules="groupRules" label-width="120px" ref="groupRef">
          <el-form-item label="所属用户" prop="customerName">
            <el-input v-model.trim="groupForm.customerName" style="width:410px" disabled />
          </el-form-item>
          <el-form-item label="选择分组" prop="siteTypeId">
            <SelectAdd ref="selectAdd" :actIndustryIds="groupOption" v-model="groupForm.siteTypeId" @saveActive="saveActive" @chooseActive="chooseActive"/>
            <!-- <el-select v-model="groupForm.siteTypeId" placeholder="请选择所属分组" clearable size="small"
              @change="$forceUpdate()" style="width: 100%">
              <el-option v-for="dict in groupOption" :key="dict.id" :label="dict.typeName" :value="dict.id" />
            </el-select> -->
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center">
          <el-button type="primary" @click="submitGroupForm">确 定</el-button>
          <el-button @click="cancelGroup">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 修改分组 -->
      <el-dialog title="修改分组" :visible.sync="editGroupOpen" width="40%" append-to-body :before-close="cancelFenForm">
        <el-form :model="editGroupFrom" :rules="editgroupRules" label-width="120px" ref="fenRef">
          <el-row>
            <el-col :span="12">
              <el-form-item label="所属用户">
                <el-popover trigger="hover" placement="top" width="300"
                  :disabled="isShowPoppver(getOwnUser(curstomerList[0]), 80)">
                  <p>{{ getOwnUser(curstomerList[0]) }}</p>
                  <div slot="reference">
                    <p class="groupP">{{ getOwnUser(curstomerList[0]) }}</p>
                  </div>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择分组" prop="ownGroupOne">
                <el-select v-model="editGroupFrom.ownGroupOne" placeholder="请选择所属分组" clearable size="small"
                  @change="$forceUpdate()" style="width: 100%">
                  <el-option v-for="dict in fenGroupOne" :key="dict.id" :label="dict.typeName" :value="dict.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-show="curstomerList[1]">
            <el-col :span="12">
              <el-form-item label="所属用户">
                <el-popover trigger="hover" placement="top" width="300"
                  :disabled="isShowPoppver(getOwnUser(curstomerList[1]), 20)">
                  <p>{{ getOwnUser(curstomerList[1]) }}</p>
                  <div slot="reference">
                    <p class="groupP">{{ getOwnUser(curstomerList[1]) }}</p>
                  </div>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择分组" :prop="curstomerList[1] ? 'ownGroupTwo' : ''">
                <el-select v-model="editGroupFrom.ownGroupTwo" placeholder="请选择所属分组" clearable size="small"
                  @change="$forceUpdate()" style="width: 100%">
                  <el-option v-for="dict in fenGroupTwo" :key="dict.id" :label="dict.typeName" :value="dict.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-show="curstomerList[2]">
            <el-col :span="12">
              <el-form-item label="所属用户">
                <el-popover trigger="hover" placement="top" width="300"
                  :disabled="isShowPoppver(getOwnUser(curstomerList[2]), 80)">
                  <p>{{ getOwnUser(curstomerList[2]) }}</p>
                  <div slot="reference">
                    <p class="groupP">{{ getOwnUser(curstomerList[2]) }}</p>
                  </div>
                </el-popover>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择分组" :prop="curstomerList[2] ? 'ownGroupThree' : ''">
                <el-select v-model="editGroupFrom.ownGroupThree" placeholder="请选择所属分组" clearable size="small"
                  @change="$forceUpdate()" style="width: 100%">
                  <el-option v-for="dict in fenGroupThree" :key="dict.id" :label="dict.typeName" :value="dict.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center">
          <el-button type="primary" @click="submitFenForm">确 定</el-button>
          <el-button @click="cancelFenForm">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 批量导入 -->
      <el-dialog :title="titleType" :visible.sync="dialogVisible" width="30%">
        <div style="text-align: center">
          <el-upload class="upload-demo" :action="tabPosition == 1 ? uploadFileUrl : uploadFileUrlTwo"
            :on-preview="handlePreview" :on-success="handleUploadSuccess" :on-remove="handleRemove"
            :before-remove="beforeRemove" :limit="1" :headers="headers" :on-exceed="handleExceed" :file-list="fileList">
            <el-button size="small">点击上传</el-button>
          <!-- <div slot="tip"
                                                                                                                                   class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div> -->
          </el-upload>
          <div style="text-align: center; margin-top: 40px">
            <el-button type="primary" @click="fileWebDown" size="small">{{ tabPosition == 1 ? "备案网站模板下载" : "媒体账号模板下载" }}
              <el-tooltip class="item" effect="light" content="用户名称是登录账号名，如果是多个用户的话，用英文逗号隔开" placement="top">
                <i class="el-icon-question" />
              </el-tooltip>
            </el-button>
            <el-button type="primary" size="small" @click="importTemp">确定</el-button>
            <el-button type="success" size="small" @click="ClosedialogVisible">取消</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listWebsite,
  getWebsite,
  delWebsite,
  addWebsite,
  updateWebsite,
  exportWebsite,
  fileWebDownApi,
  saveImportWebsiteApi,
  getOwnListApi,
  submitFenFormApi,
} from "@/api/platManage/filWeb";
import { listUser, listUserNew } from "@/api/system/user";
import {
  listMediacount,
  getMediacount,
  delMediacount,
  addMediacount,
  updateMediacount,
  exportMediacount,
  fileMedDownApi,
  saveImportMediaAccountApi,
  groupOptionApi,
  submitGroupFormApi,mediaExport,websiteExport
} from "@/api/platManage/medMang";
import { addSitePlanApi } from "@/api/system/sitePlan";
import { IsURL } from "@/utils/validate.js";
import { getToken } from "@/utils/auth";
import { checkPermi } from "@/utils/permission.js";
export default {
  name: "Website",
  components: {},
  data () {
    return {
      customerId: "",
      showGroup: false,
      groupOption: [],
      groupOptionTwo: [],
      disMul: true,
      userList: [],
      titleType: "批量导入备案网站",
      mediaTypeOptions: [],
      tabPosition: 1,
      fileList: [],
      uploadFileUrl:
        process.env.VUE_APP_BASE_API + "/platform/website/importWebsite", // 上传的文件服务器地址
      uploadFileUrlTwo:
        process.env.VUE_APP_BASE_API +
        "/platform/mediacount/importMediaAccount", // 上传的文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      dialogVisible: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      idsName: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 备案网站表格数据
      websiteList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否采集字典
      isCollectOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // 信源名称
        name: null,
        // 备案域名
        host: null,
        // 备案许可证号
        recordLicenseNumber: null,
        // 账号名称
        accountname: "",
        // 账号id
        accountId: "",
        // 媒体类型
        mediaType: "",
        // 所属用户
        customer: "",
        // 所属分组
        siteTypeId: "",
        mediaType: "",
      },
      showGroupForm: false,
      // 表单参数
      form: {
        name: "",
        homeUrl: "",
        host: "",
        recordLicenseNumber: "",
        accountname: "",
        accountId: "",
        nickName: "",
        mediaType: "",
        isCollect: "1",
        customer: "",
        siteTypeId: "",
      },
      isCollectList: [
        { name: "是", value: "1" },
        { name: "否", value: "0" },
      ],
      // 表单校验
      rules: {
        name: [
          { required: true, message: "信源名称不能为空", trigger: "blur" },
        ],
        homeUrl: [
          { required: true, message: "首页链接不能为空", trigger: "blur" },
        ],
        host: [{ required: true, message: "域名不能为空", trigger: "blur" }],
        // recordLicenseNumber: [
        //   { required: true, message: '备案许可号不能为空', trigger: 'blur' },
        // ],
        isCollect: [
          { required: true, message: "是否持续采集", trigger: "blur" },
        ],
      },
      resCode: "",
      moreGroupOpen: false,
      groupForm: {},
      groupRules: {
        siteTypeId: [
          { required: true, message: "所属分组不能为空", trigger: "change" },
        ],
      },
      editGroupOpen: false,
      editGroupFrom: {
        ownGroupOne: "",
        ownGroupTwo: "",
        ownGroupThree: "",
      },
      editgroupRules: {
        ownGroupOne: [
          { required: true, message: "分组不能为空", trigger: "change" },
        ],
        ownGroupTwo: [
          { required: true, message: "分组不能为空", trigger: "change" },
        ],
        ownGroupThree: [
          { required: true, message: "分组不能为空", trigger: "change" },
        ],
      },
      curstomerList: [],
      fenGroupOne: [],
      fenGroupTwo: [],
      fenGroupThree: [],
      lineId: "",
      customer: "",
      exportLoading:false
    };
  },
  created () {
    this.getList();
    this.getUserList()
    this.getDicts("by_website_collect").then((response) => {
      this.isCollectOptions = response.data;
    });
    this.getDicts("by_media_media_type").then((response) => {
      this.mediaTypeOptions = response.data;
    });
  },
  watch: {
    tabPosition: {
      handler (newv, oldv) {
        if (newv == 1) {
          this.rules = {
            name: [
              { required: true, message: "信源名称不能为空", trigger: "blur" },
            ],
            homeUrl: [
              { required: true, message: "首页链接不能为空", trigger: "blur" },
            ],
            host: [
              { required: true, message: "域名不能为空", trigger: "blur" },
            ],
            // recordLicenseNumber: [
            //   { required: true, message: '备案许可号不能为空', trigger: 'blur' },
            // ],
            isCollect: [
              { required: true, message: "是否持续采集", trigger: "blur" },
            ],
          };
        } else if ((newv = 2)) {
          this.rules = {
            accountname: [
              { required: true, message: "信源名称不能为空", trigger: "blur" },
            ],
            accountId: [
              { required: true, message: "信源id不能为空", trigger: "blur" },
            ],
            // nickName: [
            //   { required: true, message: "账号昵称不能为空", trigger: "blur" },
            // ],
            mediaType: [
              {
                required: true,
                message: "信源类型不能为空",
                trigger: "change",
              },
            ],
          };
        }
      },
      immediate: true,
    },
  },
  computed: {
    isShowPoppver () {
      return function (val, limit) {
        if (val && val.length >= limit) {
          return false;
        } else {
          return true;
        }
      };
    },
  },
  methods: {
    checkPermi,
    // 点击批量分组
    moreGroup () {
      if (this.queryParams.customer) {
        this.moreGroupOpen = true;
        this.userList.map((item) => {
          if (this.queryParams.customer == item.userId) {
            this.groupForm.customerName =
              item.userName + "(" + item.nickName + ")";
          }
        });
      } else {
        this.msgError("请选择所属用户");
      }
    },
    /** 导出按钮操作 */
    handleExports() {
      // const queryParams = this.queryParams;
      this.$confirm("您确定要导出数据吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then( ()=> {
          this.exportLoading = true
          let params = JSON.parse(JSON.stringify(this.queryParams))
          this.userList.map((item)=>{
            if(item.userId==this.queryParams.customer){
            params.customer = item.customer
          }
          })
          delete params.pageNum
          delete params.pageSize
          if(this.tabPosition=='1'){
            // 网站
            return websiteExport({...params,idList:this.ids});
          } else {
            params.name = params.accountname
            delete params.accountname
            return mediaExport({...params,idList:this.ids});
          
          }
        })
        .then((response) => {
          this.download(response.msg);
          this.exportLoading = false
        });
    },
    // 添加分类
    saveActive(addActive){
       let customerId = null
       this.userList.map((item)=>{
        if(item.userId==this.queryParams.customer){
          customerId= item.customer
          return
        }
      })
      console.log(customerId,'customerId');
      if(addActive){
          addSitePlanApi({
              typeName: addActive,
              customer: customerId}).then((res)=>{
            if(res.code==200){
                this.$message.success(res.msg);
                this.$refs.selectAdd.resetAddActive();
                // this.addActive = null;
            let params = {
            // type: this.tabPosition,
            customer: this.customerId,
          };
                groupOptionApi(params).then(res=>{
                    this.groupOption = res.rows;
                    this.groupOption.map((item)=>{
                        if(item.typeName==this.groupForm.siteTypeId){
                        this.$set(item,'tag',true)
                    }else{
                        this.$set(item,'tag',false)
                    }
                    })
                })
              }
          })
      }else{
          this.msgInfo('请输入分组')
      }    
    },
    // 选中分类
    chooseActive(item,index){
        this.$forceUpdate();
        this.groupOption.map((itema)=>{
            if(itema==item){
                itema.tag = true
            }else{
                itema.tag = false
            }
        })
    },
    // 确定批量分组
    submitGroupForm () {
      this.$refs["groupRef"].validate(async (valid) => {
        if (valid) {
          let siteTypeId =this.selectDictId(this.groupOption,this.groupForm.siteTypeId)
          let params = {
            siteTypeId: siteTypeId,
            sourceId: this.ids.join(),
            customer: this.customerId,
          };
          let res = await submitGroupFormApi(params);
          if (res.code == 200) {
            this.msgSuccess("批量分组成功");
            this.moreGroupOpen = false;
            this.getList();
          } else {
            this.msgError(res.msg);
          }
        } else {
          this.msgError("请输入正确信息");
        }
      });
    },
    // 确定修改分组
    async submitFenForm () {
      this.$refs["fenRef"].validate(async (valid) => {
        if (valid) {
          let aa = [
            this.editGroupFrom.ownGroupOne,
            this.editGroupFrom.ownGroupTwo,
            this.editGroupFrom.ownGroupThree,
          ];
          let params = {
            siteTypeIdList: aa.splice(0, this.curstomerList.length),
            sourceId: this.lineId,
            customer: this.customer,
          };
          let res = await submitFenFormApi(params);
          if (res.code == 200) {
            this.msgSuccess("修改成功");
            this.editGroupOpen = false;
            this.getList();
          } else {
            this.msgError(res.msg);
          }
        } else {
          this.msgError("请输入完整数据");
        }
      });
    },
    // 取消修改分组
    cancelFenForm () {
      this.editGroupOpen = false;
      for (let key in this.editGroupFrom) {
        this.$set(this.editGroupFrom, key, "");
      }
      //初始化的时候重置表单，清空效验规则
      this.$nextTick(() => {
        this.$refs["fenRef"].resetFields();
      });
    },
    // 取消批量分组
    cancelGroup () {
      this.groupForm.siteTypeId = "";
      this.moreGroupOpen = false;
    },
    // 所属分组切换
    async getgroupOption (val) {
      if (val) {
        this.queryParams.siteTypeId = "";
        this.showGroup = true;
        for (let i = 0; i < this.userList.length; i++) {
          if (this.userList[i].userId == val) {
            this.customerId = this.userList[i].customer;
            this.handleQuery();
          }
        }
        let params = {
          // type: this.tabPosition,
          customer: this.customerId,
        };
        let res = await groupOptionApi(params);
        if (res.code == 200) {
          this.groupOption = res.rows;
          this.groupOption.map((item)=>{
              item.tag = false
          })
        }
      } else {
        this.showGroup = false;
      }
    },
    async getgroupOptionTwo (val) {
      if (val) {
        this.form.siteTypeId = "";
        this.showGroupForm = true;
        let params = {
          customer: this.form.customer,
        };
        let res = await groupOptionApi(params);
        if (res.code == 200) {
          this.groupOptionTwo = res.rows;
          if ((this.title = "添加备案网站")) {
            this.form.siteTypeId = this.groupOptionTwo[0].id;
          } else {
            this.form.siteTypeId = "";
          }
        }
      } else {
        this.showGroupForm = false;
      }
    },

    //  获取所属用户
    async getUserList () {
      let params = {
        status: 0,
      };
      let res = await listUserNew(params);
      this.userList = res.rows;

    },
    // 信源类型字典翻译
    mediaTypeFormat (row, column) {
      return this.selectDictLabel(this.mediaTypeOptions, row.mediaType);
    },
    // 切换备案网站、媒体账号
    changeTab (val) {
      // this.resetForm("queryForm");
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        name: null,
        host: null,
        recordLicenseNumber: null,
        accountname: "",
        // 账号id
        accountId: "",
        mediaType: "",
        customer: this.queryParams.customer,
        siteTypeId: "",
      },
      this.resCode = "";
      this.getList();
      // this.showGroup = false;
    },
    getIndex (index) {
      return (
        (this.queryParams.pageNum - 1) * this.queryParams.pageSize + index + 1
      );
    },
    //   导入
    async importTemp () {
      if (this.tabPosition == 1) {
        if (this.resCode == 200) {
          let res = await saveImportWebsiteApi();
          if (res.code == 200) {
            this.$message.success("导入备案网站成功");
          } else {
            this.$message.error("导入备案网站失败");
          }
        }
      } else if (this.tabPosition == 2) {
        if (this.resCode) {
          let res = await saveImportMediaAccountApi();
          if (res.code == 200) {
            this.$message.success("导入媒体账号成功");
          } else {
            this.$message.error("导入媒体账号失败");
          }
        }
      }
      this.fileList = [];
      this.dialogVisible = false;
      this.getList();
    },
    ClosedialogVisible () {
      this.fileList = [];
      this.dialogVisible = false;
    },
    //   文件上传
    handleRemove (file, fileList) {
      console.log(file, fileList);
    },
    // 上传成功回调
    handleUploadSuccess (res, file) {
      this.resCode = res.code;
      if (res.code == 200) {
        this.$message.success(res.msg);
      } else {
        this.$message.error(res.msg);
      }
      this.$emit("input", res.url);
    },
    handlePreview (file) {
      console.log(file);
    },
    handleExceed (files, fileList) {
      this.$message.warning(
        `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length
        } 个文件`
      );
    },
    beforeRemove (file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    /** 查询备案网站列表 */
    getList () {
      this.loading = true;
      if (this.tabPosition == 1) {
        // 备案网站
        let params = {
          name: this.queryParams.name,
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          host: this.queryParams.host,
          recordLicenseNumber: this.queryParams.recordLicenseNumber,
          // customer: this.queryParams.customer[1],
          customer: this.customerId,
          siteTypeId: this.queryParams.siteTypeId,
        };
        listWebsite(params).then((response) => {
          this.websiteList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      } else if (this.tabPosition == 2) {
        // 媒体账号
        let params = {
          name: this.queryParams.accountname,
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize,
          accountId: this.queryParams.accountId,
          mediaType: this.queryParams.mediaType,
          // customer: this.queryParams.customer[1],
          customer: this.customerId,
          siteTypeId: this.queryParams.siteTypeId,
        };
        listMediacount(params).then((response) => {
          this.websiteList = response.rows;
          this.total = response.total;
          this.loading = false;
        });
      }
    },
    // 是否采集字典翻译
    isCollectFormat (row, column) {
      return this.selectDictLabel(this.isCollectOptions, row.isCollect);
    },

    // 取消按钮
    cancel () {
      this.open = false;
      this.showGroupForm = false;
      this.reset();
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        name: null,
        accountname: "",
        homeUrl: null,
        host: null,
        recordLicenseNumber: null,
        areaCode: null,
        isCollect: "1",
        customer: null,
        siteTypeId: "",
        accountId: "",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.customerId = "";
      for (let key in this.queryParams) {
        this.$set(this.queryParams, key, "");
      }
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 10;
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map((item) => item.id);
      this.idsName = selection.map((item) => item.name);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset();
      this.open = true;
      if (this.tabPosition == 1) {
        this.title = "添加备案网站";
      } else if (this.tabPosition == 2) {
        this.title = "添加媒体账号";
      }
    },
    // 修改分组
    async editMoreGroup (row) {
      this.lineId = row.id;
      this.editGroupOpen = true;
      this.customer = row.customer;
      if (row.customer) {
        this.curstomerList = row.customer.split(",");
        // 分组数据
        this.fenGroupOne = await this.getfenGroup(this.curstomerList[0]);
        this.fenGroupTwo = await this.getfenGroup(this.curstomerList[1]);
        this.fenGroupThree = await this.getfenGroup(this.curstomerList[2]);
        // 分组选中数据
        this.editGroupFrom.ownGroupOne = await this.getOwnList(
          row.id,
          this.curstomerList[0]
        );
        this.editGroupFrom.ownGroupTwo = await this.getOwnList(
          row.id,
          this.curstomerList[1]
        );
        this.editGroupFrom.ownGroupThree = await this.getOwnList(
          row.id,
          this.curstomerList[2]
        );
      }
      // 获取所选分组数据
    },
    async getOwnList (siteId, customer) {
      let aa = "";
      let params = {
        siteId: siteId,
        customer: customer,
      };
      let res = await getOwnListApi(params);
      if (res.code == 200) {
        aa = res.data[0].id;
      }
      return aa;
    },
    // 获取分组数据
    async getfenGroup (val) {
      let aa = [];
      let params = {
        customer: val,
      };
      let res = await groupOptionApi(params);
      if (res.code == 200) {
        aa = res.rows;
      }
      return aa;
    },
    // 获取所属用户
    getOwnUser (val) {
      let userP = [];
      this.userList.map((item) => {
        if (val == item.customer) {
          userP.push(`${item.userName}(${item.nickName})`);
        }
      });
      return userP.toString();
    },
    /** 修改按钮操作 */
    async handleUpdate (row) {
      this.reset();
      const id = row.id || this.ids;
      if (this.tabPosition == 1) {
        let res = await getWebsite(id);
        if (res.code == 200) {
          this.form = res.data;
          this.form.customer = parseInt(res.data.customer);
          this.form.isCollect = res.data.isCollect.toString();
          if (this.form.customer) {
            let params = {
              customer: this.form.customer,
            };
            let resType = await groupOptionApi(params);
            if (resType.code == 200) {
              // debugger;
              this.groupOptionTwo = resType.rows;
              this.showGroupForm = true;
              this.form.siteTypeId = res.data.siteTypeId;
            }
          }

          this.open = true;
          this.title = "修改备案网站";
        }
      } else {
        getMediacount(id).then((res) => {
          this.form = res.data;
          this.form.customer = parseInt(res.data.customer);
          this.form.accountname = res.data.name;
          this.form.isCollect = res.data.isCollect.toString();
          this.form.mediaType = parseInt(res.data.mediaType);
          if (res.data.siteTypeId) {
            this.showGroupForm = true;
            this.form.siteTypeId = res.data.siteTypeId;
          }
          this.open = true;
          this.title = "修改媒体账号";
        });
      }
    },
    /** 提交按钮 */
    submitForm () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            if (this.tabPosition == 1) {
              let params = {
                id: this.form.id,
                name: this.form.name,
                homeUrl: this.form.homeUrl,
                host: this.form.host,
                recordLicenseNumber: this.form.recordLicenseNumber,
                isCollect: this.form.isCollect,
                // customer: this.form.customer,
                // siteTypeId: this.form.siteTypeId,
              };
              updateWebsite(params).then((res) => {
                // debugger;
                if (res.code == 200) {
                  this.msgSuccess("修改备案网站成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.msgError(res.msg);
                  this.open = false;
                }
              });
            } else if (this.tabPosition == 2) {
              let params = {
                id: this.form.id,
                name: this.form.accountname,
                accountId: this.form.accountId,
                nickName: this.form.nickName,
                mediaType: this.form.mediaType,
                isCollect: this.form.isCollect,
                // customer: this.form.customer,
                // siteTypeId: this.form.siteTypeId,
              };
              updateMediacount(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("修改媒体账号成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.msgError(res.msg);
                  this.open = false;
                }
              });
            }
          } else {
            // 新增备案网站
            if (this.tabPosition == 1) {
              let params = {
                name: this.form.name,
                homeUrl: this.form.homeUrl,
                host: this.form.host,
                recordLicenseNumber: this.form.recordLicenseNumber,
                isCollect: this.form.isCollect,
                customer: this.form.customer,
                // siteTypeId: this.form.siteTypeId,
              };
              addWebsite(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增备案网站成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.msgError(res.msg);
                  this.open = false;
                }
              });
              //   新增媒体账号
            } else if (this.tabPosition == 2) {
              let params = {
                name: this.form.accountname,
                accountId: this.form.accountId,
                nickName: this.form.nickName,
                mediaType: this.form.mediaType,
                isCollect: this.form.isCollect,
                customer: this.form.customer,
                // siteTypeId: this.form.siteTypeId,
              };
              addMediacount(params).then((res) => {
                if (res.code == 200) {
                  this.msgSuccess("新增媒体账号成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.msgError(res.msg);
                  this.open = false;
                }
              });
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      const ids = row.id || this.ids;
      let idsName = this.idsName.join(",");
      //   const name = row.name || idsName;
      this.$confirm(
        `是否确认删除该${this.tabPosition == 1 ? "备案网站" : "媒体账号"
        }的数据项?`,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(async () => {
        if (this.tabPosition == 1) {
          let res = await delWebsite(ids);
          if (res.code == 200) {
            this.getList();
            this.msgSuccess("删除备案网站成功");
          } else {
            this.msgError(res.msg);
          }
        } else {
          let res = await delMediacount(ids);
          if (res.code == 200) {
            this.getList();
            this.msgSuccess("删除媒体账号成功");
          } else {
            this.msgError(res.msg);
          }
        }
      });
    },
    /** 导出按钮操作 */
    handleExport () {
      this.dialogVisible = true;
      if (this.tabPosition == 1) {
        this.titleType = "批量导入备案网站";
      } else {
        this.titleType = "批量导入媒体账号";
      }
    },
    // 备案网站模板下载
    fileWebDown () {
      if (this.tabPosition == 1) {
        fileWebDownApi();
      } else {
        fileMedDownApi();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.groupP {
  margin: 0;
  max-height: 66px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.app-container {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;
  min-height: calc(100vh - 60px);
}

.home {
  width: 100%;
  overflow: hidden;
  background: #fff;
  padding: 20px;
  box-sizing: border-box;
}

::v-deep .upload-demo {
  display: flex;
  width: 49%;
  margin: 0 auto;

  .el-upload-list {
    margin: 0;
    padding: 0;
    list-style: none;
    border: solid 1px #ccc;
    height: 32px;
    line-height: 32px;
    margin-left: 6px;
    border-radius: 5px;
    width: 200px;
    overflow: hidden;
  }

  .el-upload-list__item:first-child {
    margin-top: 0;
    height: 32px;
    line-height: 32px;
  }
}
</style>
