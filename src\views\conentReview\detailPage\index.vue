<template>
  <div class="home">
    <div v-loading="pageLoading" class="homeBox">
      <div class="homeLeftBox">
        <div class="homeLeftHead">
          <span>信息详情</span>
        </div>
        <div class="homeLeftUl">
          <h2 v-html="detailDataList.data.info.title" class="emRed"></h2>
          <ul>
            <li><span>来源：</span>{{ detailDataList.data.info.siteName }}</li>
            <!-- <li><span>错误分类：</span>{{ detailDataList.data.info.checkTaskType }}</li> -->
            <li style="cursor: pointer; color: #409eff">
              <!-- @click="goOrgText()" -->
              <span v-if="detailDataList.data.info.sector">
                <a
                :href="detailDataList.data.info.sector"
                referrerpolicy="no-referrer"
                target="_blank"
                >
                查看原文
              </a>
              <a
                style="margin-left: 10px;"
                :href="detailDataList.data.info.url"
                referrerpolicy="no-referrer"
                >
                下载附件
              </a>
              </span>
              <a
                v-else
                :href="detailDataList.data.info.url"
                referrerpolicy="no-referrer"
                target="_blank"
                >
                查看原文
              </a>
            </li>
            <li>原文时间：{{ detailDataList.data.info.time }}</li>
          </ul>
          <p
            class="textCont emRed"
            @contextmenu="contextmenu"
            v-html="detailDataList.data.info.content"
          ></p>
        </div>
      </div>
      <div class="homeRight">
        <span class="wordSpan">词语建议</span>
        <el-table
          :data="detailDataList.data.wordList"
          border=""
          :row-style="{ height: 10 + 'px' }"
          :cell-style="{ padding: 6 + 'px' }"
          :header-cell-style="{ height: '10px', padding: '6px' }"
        >
          <el-table-column
            prop="mistakeWord"
            label="问题词语"
          ></el-table-column>
          <el-table-column
            prop="correctWord"
            label="建议词语"
          >
          <template slot-scope="scope">
            <el-link style="font-weight: 400;" v-if="checkSuggestion(scope.row.correctWord)" :underline="false" type="primary" @click="goBaidu(scope.row.correctWord)">{{ scope.row.correctWord }}</el-link>
            <span v-else>{{ scope.row.correctWord }}</span>
          </template>  
        </el-table-column>
          <el-table-column
            prop="remark"
            label="释义"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <el-popover
                trigger="hover"
                placement="top"
                width="300"
                v-if="scope.row.remark"
              >
                <p>{{ scope.row.remark }}</p>
                <div slot="reference" style="background: none">
                  <p class="ellipsis">
                    {{ scope.row.remark }}
                  </p>
                </div>
              </el-popover>
              <p v-else class="ellipsis" style="color: #999; font-size: 12px">
                常见表述错误
              </p>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getDetailDataApi ,getDetailDataHisApi} from "@/api/conentReview/detailPage";
import {checkSuggestion} from "@/utils/index"
export default {
  name: "detailPage",
  inject: ["contextmenu"],
  data() {
    return {
      checkSuggestion,
      pageLoading: false,
      tableData: [{ name: "扶贫攻坚", nameTwo: "脱贫攻坚" }],
      detailDataList: {
        data: {
          wordList: [],
          info: {
            content:'暂无',
            siteName:'暂无',
            time:'暂无',
            title:'暂无',
          }
        }
      },
    };
  },
  created() {
    this.getDetailData();
  },
  methods: {
    //   获取详情页数据
    async getDetailData() {
      this.pageLoading = true;
      let params = {
        id: this.$route.query.id,
        time: this.$route.query.itemTime,
        solrId: this.$route.query.itemSolrId,
        checkTaskId: this.$route.query.itemCheckTaskId,
      };
      // 11是当前问题 12是历史问题
      if (this.$route.query.questionTab == '11') {
        this.detailDataList = JSON.parse(
          JSON.stringify(await getDetailDataApi(params))
        );
        this.pageLoading = false;
      } else {
        this.detailDataList = JSON.parse(
          JSON.stringify(await getDetailDataHisApi(params))
        );
        this.pageLoading = false;
      }
    },
    // 查看原文
    goOrgText() {
      window.open(this.detailDataList.data.info.url, "_blank");
    },
    goBaidu(word) {
      if (checkSuggestion(word)) {
        window.open(`https://www.baidu.com/s?wd=${word}`, '_blank')
      }
    },
  },
};
</script>

<style scoped lang="scss">
.ellipsis {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.el-popover {
  height: 150px;
  overflow: auto;
}
.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 6%;
  box-sizing: border-box;
  min-height: 889px;
  .homeBox {
    width: 100%;
    background: #fff;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 20px;
    box-sizing: border-box;
    min-height: 800px;
    .homeLeftBox {
      width: 68%;
      overflow: hidden;
      margin-bottom: 20px;
      .homeLeftHead {
        width: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        background: #f5f5f5;
        .homBut {
          display: inline-block;
          padding-top: 6px;
          padding-right: 10px;
        }
        span {
          display: inline-block;
          color: #fff;
          background: #ed9e2f;
          height: 40px;
          line-height: 40px;
          padding: 0 10px;
          font-size: 14px;
        }
      }
      .homeLeftUl {
        width: 100%;
        overflow: hidden;
        h2 {
          width: 100%;
          overflow: hidden;
          font-weight: normal;
          font-size: 24px;
        }
        ul {
          display: flex;
          flex-direction: row;
          justify-content: flex-start;
          //   flex-wrap: wrap;
          margin: 0;
          padding: 0;
          font-size: 14px;
          width: 100%;
          overflow: hidden;
          color: #666666;
          li {
            list-style: none;
            height: 30px;
            line-height: 30px;
            margin-right: 10px;
            span {
              display: inline-block;
              text-align: center;
              margin-right: 8px;
            }
          }
        }
        p.textCont {
          white-space: pre-wrap;
          line-height: 28px;
          border: solid 1px #ccc;
          padding: 20px;
          box-sizing: border-box;
          height: 500px;
          overflow-y: scroll;
        }
      }
    }
    .homeRight {
      background: #fff;
      width: 30%;
      span.wordSpan {
        display: block;
        width: 100%;
        height: 40px;
        line-height: 40px;
        background: #3d9ffe;
        color: #fff;
        text-align: center;
        font-size: 14px;
        margin-bottom: 20px;
      }
    }
  }
}
</style>
