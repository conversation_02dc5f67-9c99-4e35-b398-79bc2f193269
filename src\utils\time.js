import moment from "moment";
export const timeJson = {
    "day": [moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
    "days": [moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')],
    "twoday": [moment(new Date()).subtract(2, 'days').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
    "threedays": [moment(new Date()).subtract(3, 'days').format('YYYY-MM-DD HH:mm:ss'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')],
    "threeTendays": [moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD HH:mm:ss'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')],
    "week": [moment(new Date()).subtract(1, 'weeks').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
    "weeks": [moment(new Date()).subtract(1, 'weeks').format('YYYY-MM-DD HH:mm:ss'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')],
    "mouth": [moment(new Date()).subtract(1, 'months').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
    "mouths": [moment(new Date()).subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')],
    "year": [moment(new Date()).subtract(1, 'years').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
    "halfYear": [moment(new Date()).subtract(6, 'months').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
    "self": [moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD HH:mm:ss'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')],
    "threeMonth": [moment(new Date()).subtract(3, 'months').format('YYYY-MM-DD HH:mm:ss'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')],
    "today": [moment().format("YYYY-MM-DD 00:00:00"), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')]
}