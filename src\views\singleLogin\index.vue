<template>
  <div class="out">
    <img src="@/assets/images/jumpImg.png" alt="">
    <div>页面正在跳转中，请稍后... <i class="el-icon-loading"></i></div>
  </div>
</template>

<script>
import { encryptByDES, decryptByDES } from "@/des/des.js"
import { removeToken } from "@/utils/auth";
export default {
  name: "singleLogin",
  data() {
    return {
      loginForm: {
        username: "",
        password: "",
        code: "",
        uuid: "",
      },
      loading: false,
    };
  },
  created() {
    this.handleLogin()
  },
  methods: {
    handleLogin() {
      removeToken()
      if (this.$route.query?.token) {
        console.log('this.$route.query', this.$route.query)
        const token = decodeURIComponent(this.$route.query.token)
        const loginWay = decodeURIComponent(this.$route.query.loginWay)
        const params = { token: token, loginWay: loginWay }
        this.loading = true
        this.$store
          .dispatch('LoginBySingle', params)
          .then(() => {
            if (loginWay == "normalLogin") {
              this.$router.push({
                path: "/",
                query: { name: "adminuser" },
              });
            } else {
              this.$router.push({
                path: "/",
                query: { name: "normaluser" },
              });
            }
          })
          .catch(() => {
            this.loading = false
          })
      } else {
        var myKey = 'smartcheck123456';
        this.loginForm.username = decryptByDES(this.$route.query.username, myKey)
        this.loginForm.password = decryptByDES(this.$route.query.password, myKey)
        let params = {
          code: 1,
          username: this.loginForm.username,
          password: this.loginForm.password,
          uuid: 'Sj@@.#999',
        };
        this.$store
          .dispatch("singleLogin", params)
          .then((res) => {
            if (res.code == 200) {
              if (res.loginWay == "normalLogin") {
                this.$router.push({
                  path: "/",
                  query: { name: "adminuser" },
                });
              } else {
                this.$router.push({
                  path: "/",
                  query: { name: "normaluser" },
                });
              }
            } else {
              this.$message.error(res.msg);
              this.loading = false;
            }
          })
          .catch((err) => {
            this.loading = false;
          });


      }
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss">
.out {
  text-align: center;
  margin-top: 200px;
  box-sizing: border-box;

  img {
    width: 300px;
    height: 350px;
    margin-bottom: 30px;
  }
}
</style>
